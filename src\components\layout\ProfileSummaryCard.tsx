import React from 'react';
import { Card, CardContent } from '@/components/ui/Card';
import { Icon } from '@/components/ui/Icon';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';
import { formatDistanceToNow } from 'date-fns';
import { User } from '@/types/features/user/user.types';
import { UserRole } from '@/types/enums';

export interface ProfileSummaryCardProps {
  /** User data to display */
  user: User;
  /** Optional profile photo URL */
  profilePhotoUrl?: string;
  /** Callback for edit action */
  onEdit?: () => void;
  /** Callback for delete action */
  onDelete?: () => void;
  /** Loading state for edit action */
  editLoading?: boolean;
  /** Loading state for delete action */
  deleteLoading?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Loading state */
  loading?: boolean;
}

export const ProfileSummaryCard: React.FC<ProfileSummaryCardProps> = ({
  user,
  profilePhotoUrl,
  onEdit,
  onDelete,
  editLoading = false,
  deleteLoading = false,
  className = '',
  loading = false,
}) => {
  const getRoleBadgeVariant = (role: UserRole): string => {
    const roleColors: Record<UserRole, string> = {
      [UserRole.ADMIN]: 'bg-blue-100 text-blue-800 border-blue-200',
      [UserRole.CLIENT]: 'bg-green-100 text-green-800 border-green-200',
      [UserRole.FREELANCER]: 'bg-purple-100 text-purple-800 border-purple-200',
    };
    return roleColors[role] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const getRoleIcon = (role: UserRole): string => {
    const roleIcons: Record<UserRole, string> = {
      [UserRole.ADMIN]: 'Shield',
      [UserRole.CLIENT]: 'Building2',
      [UserRole.FREELANCER]: 'User',
    };
    return roleIcons[role] || 'User';
  };

  const formatRole = (role: UserRole): string => {
    const roleLabels: Record<UserRole, string> = {
      [UserRole.ADMIN]: 'Administrator',
      [UserRole.CLIENT]: 'Client',
      [UserRole.FREELANCER]: 'Freelancer',
    };
    return roleLabels[role] || role;
  };

  const getUserInitials = (): string => {
    return user.name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="flex items-start gap-6">
              <div className="h-24 w-24 bg-gray-300 rounded-full"></div>
              <div className="flex-1 space-y-3">
                <div className="h-7 bg-gray-300 rounded w-3/4"></div>
                <div className="h-5 bg-gray-300 rounded w-1/2"></div>
                <div className="flex gap-2">
                  <div className="h-6 bg-gray-300 rounded w-24"></div>
                  <div className="h-6 bg-gray-300 rounded w-32"></div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardContent className="p-6">
        <div className="flex items-start gap-6">
          {/* Avatar */}
          <Avatar className="h-24 w-24 border-4 border-white shadow-lg ring-2 ring-gray-100">
            {profilePhotoUrl && (
              <AvatarImage 
                src={profilePhotoUrl} 
                alt={user.name}
                className="object-cover" 
              />
            )}
            <AvatarFallback className="text-xl font-bold bg-gradient-to-br from-blue-500 to-purple-600 text-white">
              {getUserInitials()}
            </AvatarFallback>
          </Avatar>

          {/* Profile Info */}
          <div className="flex-1 space-y-4">
            {/* Name and Email */}
            <div className="space-y-1">
              <h1 className="text-2xl font-bold text-foreground">{user.name}</h1>
              <div className="flex items-center gap-2 text-muted-foreground">
                <Icon name="Mail" size="sm" />
                <span className="text-base">{user.email}</span>
              </div>
            </div>

            {/* Tags and Info */}
            <div className="flex items-center gap-3 flex-wrap">
              {/* Role Badge */}
              <Badge className={`${getRoleBadgeVariant(user.role)} border font-medium px-3 py-1.5`}>
                <Icon name={getRoleIcon(user.role) as any} size="xs" className="mr-2" />
                {formatRole(user.role)}
              </Badge>

              {/* Join Date Badge */}
              <Badge variant="outline" className="border-gray-300 text-gray-700 px-3 py-1.5">
                <Icon name="Calendar" size="xs" className="mr-2" />
                Joined {formatDistanceToNow(new Date(user.createdAt), { addSuffix: true })}
              </Badge>
            </div>
          </div>

          {/* Quick Actions */}
          {(onEdit || onDelete) && (
            <div className="flex items-start gap-2">
              {onEdit && (
                <Button
                  onClick={onEdit}
                  disabled={editLoading || deleteLoading}
                  size="sm"
                  variant="outline"
                  className="p-2"
                >
                  {editLoading ? (
                    <Icon name="Loader2" size="sm" className="animate-spin" />
                  ) : (
                    <Icon name="Edit" size="sm" />
                  )}
                </Button>
              )}
              
              {onDelete && (
                <Button
                  onClick={onDelete}
                  disabled={editLoading || deleteLoading}
                  size="sm"
                  variant="destructive"
                  className="p-2"
                >
                  {deleteLoading ? (
                    <Icon name="Loader2" size="sm" className="animate-spin" />
                  ) : (
                    <Icon name="Trash2" size="sm" />
                  )}
                </Button>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfileSummaryCard;