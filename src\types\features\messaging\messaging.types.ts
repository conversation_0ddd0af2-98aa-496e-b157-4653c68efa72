import { UserProfile } from '../auth/auth.types';

export interface APIError {
  message: string;
  code?: string;
  statusCode?: number;
  details?: Record<string, unknown>;
}

export enum MessageStatus {
  SENDING = 'SENDING',
  SENT = 'SENT',
  DELIVERED = 'DELIVERED',
  READ = 'READ',
  FAILED = 'FAILED'
}

export enum MessageType {
  TEXT = 'TEXT',
  IMAGE = 'IMAGE',
  FILE = 'FILE',
  SYSTEM = 'SYSTEM'
}

export interface FileInfo {
  url: string;
  name: string;
  size?: number;
  type?: string;
  previewUrl?: string;
}

export interface BaseMessage {
  id: string;
  conversationId: string;
  senderId: string;
  receiverId?: string;
  content: string;
  type?: MessageType | string;
  status?: MessageStatus | string;
  createdAt: string;
  updatedAt?: string;
  fileInfo?: FileInfo;
  metadata?: Record<string, unknown>;
}

export interface Message extends BaseMessage {
  sender: MessagingUser;
  isOwn: boolean;
  receiver?: MessagingUser;
  isSending?: boolean;
  error?: string | null;
}

export interface Conversation {
  id: string;
  participants: MessagingUser[];
  lastMessage?: Partial<Message>;
  unreadCount?: number;
  updatedAt?: string;
  jobId?: string;
  clientId?: string;
  freelancerId?: string;
  createdAt?: string;
  messages?: Array<ServerMessage | Message>;
  job?: {
    id: string;
    title: string;
    budget: number;
  };
  client?: MessagingUser;
  freelancer?: MessagingUser;
}

export type UIMessage = Message;

export type MessagingUser = Pick<UserProfile, 'id' | 'name' | 'avatar' | 'role'> & {
  email?: string;
  isOnline?: boolean;
  profilePhoto?: string;
};

export interface ServerMessage extends BaseMessage {
  messageText?: string;
  sender: MessagingUser & { role?: string };
  receiver?: MessagingUser;
  fileInfo?: FileInfo;
}

export interface SendMessageDto {
  conversationId: string;
  content: string;
  type?: MessageType;
  file?: File;
  metadata?: Record<string, unknown>;
}

export interface CreateConversationDto {
  participantIds: string[];
  jobId?: string;
  initialMessage?: string;
}

export interface MessagesResponse {
  items: Message[];
  nextToken?: string;
  conversation: Conversation;
}

export interface MessageBubbleProps {
  message: Message;
  showAvatar?: boolean;
  showStatus?: boolean;
  className?: string;
}

export interface ConversationListProps {
  conversations: Conversation[];
  selectedConversationId?: string;
  onSelectConversation: (conversationId: string) => void;
  loading?: boolean;
  emptyState?: React.ReactNode;
}

export type MessageEvent = {
  type: 'NEW_MESSAGE' | 'MESSAGE_UPDATED' | 'CONVERSATION_UPDATED';
  payload: Message | Conversation;
};

export type MessagingState = {
  conversations: Record<string, Conversation>;
  messages: Record<string, Message[]>;
  activeConversationId: string | null;
  isLoading: boolean;
  error: string | null;
};
