'use client';

import { useState } from 'react';
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";
import { jobCategoryService } from '@/api/job-categories/job-category.service';
import { CreateJobCategoryInput } from "@/types/features/job-categories/job-category.types";
import { Icon } from '@/components/ui/Icon';
import { ContentHeader } from '@/components/layout/ContentHeader';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Textarea } from '@/components/ui/Textarea';
import { useToast } from '@/components/ui/toast';

export default function NewJobCategoryPage() {
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const router = useRouter();
  const { showToast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState<CreateJobCategoryInput>({
    name: "",
    description: "",
    isActive: true,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setIsSubmitting(true);
      setError(null);
      
      if (!formData.name.trim()) {
        setError('Category name is required.');
        return;
      }

      await jobCategoryService.createJobCategory(formData);
      
      showToast('Success', {
        description: 'Job category created successfully',
        position: "top-right",
      });
      
      router.push('/admin/job-categories');
    } catch (err) {
      console.error('Error creating job category:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to create job category. Please try again.';
      setError(errorMessage);
      showToast('Error', {
        description: errorMessage,
        position: "top-right",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    router.push('/admin/job-categories');
  };

  if (!authLoading && (!isAuthenticated || user?.attributes?.['custom:role'] !== 'ADMIN')) {
    router.push('/login');
    return null;
  }

  if (authLoading || !isAuthenticated) {
    return (
      <div className="flex items-center justify-center h-full">
        <Icon name="Loader2" size="xl" className="animate-spin text-blue-500" />
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-4 sm:p-6">
      <ContentHeader
        title="Add New Job Category"
        subtitle="Fill in the details below to create a new job category."
        breadcrumbs={[
          { label: 'Dashboard', href: '/admin/dashboard' },
          { label: 'Job Categories', href: '/admin/job-categories' },
          { label: 'New Category', current: true }
        ]}
        showBackButton={true}
      />
      
      {error && (
        <div className="rounded-md bg-red-50 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <Icon name="XCircle" size="md" className="text-red-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}
      
      <div className="bg-card rounded-lg shadow-sm p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Category Name *
            </label>
            <Input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="Enter job category name"
              required
              className="w-full"
            />
            <p className="mt-1 text-sm text-gray-500">
              Choose a clear, descriptive name for the job category (e.g., &quot;Web Development&quot;, &quot;Graphic Design&quot;).
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <Textarea
              value={formData.description || ''}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Enter a detailed description of this job category"
              rows={4}
              className="w-full"
            />
            <p className="mt-1 text-sm text-gray-500">
              Provide a clear description that helps users understand what types of jobs belong in this category.
            </p>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="isActive"
              checked={formData.isActive}
              onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
              className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
            />
            <label htmlFor="isActive" className="text-sm font-medium text-gray-700">
              Active (category will be available for job posting)
            </label>
          </div>

          <div className="flex justify-end space-x-3 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
              className="min-w-[100px]"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !formData.name.trim()}
              className="min-w-[100px]"
            >
              {isSubmitting ? (
                <>
                  <Icon name="Loader2" size="sm" className="animate-spin mr-2" />
                  Creating...
                </>
              ) : (
                <>
                  <Icon name="Plus" size="sm" className="mr-2" />
                  Create Category
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}