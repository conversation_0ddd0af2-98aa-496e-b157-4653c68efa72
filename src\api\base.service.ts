import { handleError } from '@/utils/errorHandler';
import {
  type ApiResponse,
  type ApiSuccessResponse,
  type ApiErrorResponse,
  isErrorResponse,
  createErrorResponse
} from '@/types/api/response.types';

export class BaseService {
  protected async handleRequest<T>(
    request: () => Promise<Response>,
    options: {
      authRequired?: boolean;
      customErrorHandler?: (error: unknown) => never;
    } = { authRequired: true }
  ): Promise<ApiSuccessResponse<T>> {
    try {
      const response = await request();
      const data: ApiResponse<T> = await response.json().catch(() => ({
        success: false,
        error: {
          code: 'INVALID_RESPONSE',
          message: 'Failed to parse response',
          statusCode: response.status
        }
      }));

      if (!response.ok || !data.success) {
        const error = isErrorResponse(data)
          ? data.error
          : {
              code: 'UNKNOWN_ERROR',
              message: 'An unknown error occurred',
              statusCode: response.status
            };

        throw createErrorResponse(
          error.code,
          error.message,
          error.statusCode || 500,
          error.details
        );
      }

      return data as ApiSuccessResponse<T>;
    } catch (error: unknown) {
      const err = error as {
        error?: { statusCode?: number };
        status?: number;
        response?: { status?: number };
      };

      if (options.customErrorHandler) {
        options.customErrorHandler(error);
      }

      if (
        err?.error?.statusCode === 401 ||
        err?.status === 401 ||
        (err?.response?.status === 401 && options.authRequired)
      ) {
        // You might want to trigger a logout or redirect to login here
        // authService.logout();
        // window.location.href = '/login';
      }

      throw handleError(error);
    }
  }

  protected async get<T>(
    url: string,
    options: {
      headers?: HeadersInit;
      authRequired?: boolean;
    } = {}
  ): Promise<ApiSuccessResponse<T>> {
    return this.handleRequest<T>(
      () =>
        fetch(url, {
          method: 'GET',
          headers: this.getHeaders(options.headers),
          credentials: 'include'
        }),
      { authRequired: options.authRequired ?? true }
    );
  }

  protected async post<T>(
    url: string,
    data?: unknown,
    options: {
      headers?: HeadersInit;
      authRequired?: boolean;
    } = {}
  ): Promise<ApiSuccessResponse<T>> {
    return this.handleRequest<T>(
      () =>
        fetch(url, {
          method: 'POST',
          headers: this.getHeaders(options.headers),
          body: data ? JSON.stringify(data) : undefined,
          credentials: 'include'
        }),
      { authRequired: options.authRequired ?? true }
    );
  }

  protected async put<T>(
    url: string,
    data: unknown,
    options: {
      headers?: HeadersInit;
      authRequired?: boolean;
    } = {}
  ): Promise<ApiSuccessResponse<T>> {
    return this.handleRequest<T>(
      () =>
        fetch(url, {
          method: 'PUT',
          headers: this.getHeaders(options.headers),
          body: JSON.stringify(data),
          credentials: 'include'
        }),
      { authRequired: options.authRequired ?? true }
    );
  }

  protected async delete<T>(
    url: string,
    options: {
      headers?: HeadersInit;
      authRequired?: boolean;
      data?: unknown;
    } = {}
  ): Promise<ApiSuccessResponse<T>> {
    return this.handleRequest<T>(
      () =>
        fetch(url, {
          method: 'DELETE',
          headers: this.getHeaders(options.headers),
          body: options.data ? JSON.stringify(options.data) : undefined,
          credentials: 'include'
        }),
      { authRequired: options.authRequired ?? true }
    );
  }

  private getHeaders(customHeaders: HeadersInit = {}): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...customHeaders
    };


    return headers;
  }

  protected handleApiError(error: unknown): never {
    if (
      error && 
      typeof error === 'object' && 
      'success' in error && 
      error.success === false &&
      'error' in error &&
      error.error &&
      typeof error.error === 'object' &&
      'code' in error.error &&
      'message' in error.error &&
      'statusCode' in error.error
    ) {
      throw error as ApiErrorResponse;
    }
    
    const appError = handleError(error);
    throw createErrorResponse(
      appError.code,
      appError.message,
      appError.statusCode || 500,
      appError.details
    );
  }
}
