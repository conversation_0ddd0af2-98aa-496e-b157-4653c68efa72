'use client';

import { useEffect } from 'react';
import { Icon } from '@/components/ui/Icon';
import { Button } from '@/components/ui/Button';
import { cn } from '@/lib/utils';
import { MessagingContainer } from './MessagingContainer';
import { MessagingUser } from './types';
import { UserRole } from '@/types/enums';
import { Conversation, UIMessage as Message } from '@/types/features/messaging/messaging.types';

export interface MessagingDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  currentUser: MessagingUser;
  currentUserRole: 'CLIENT' | 'FREELANCER';
  conversations: Conversation[];
  onSendMessage: (conversationId: string, content: string) => Promise<void>;
  onLoadMoreMessages: (conversationId: string, before: Date) => Promise<Message[]>;
  onConversationSelect?: (conversationId: string) => void;
  onFileUpload?: (file: File) => Promise<string>;
  className?: string;
}

export function MessagingDrawer({
  isOpen,
  onClose,
  currentUser,
  currentUserRole,
  conversations,
  onSendMessage,
  onLoadMoreMessages,
  onConversationSelect,
  onFileUpload,
  className,
}: MessagingDrawerProps) {
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      const drawer = document.getElementById('messaging-drawer');
      
      if (drawer && !drawer.contains(target) && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = '';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black/50 z-40" onClick={onClose} />
      
      {/* Drawer */}
      <div 
        id="messaging-drawer"
        className={cn(
          'fixed inset-y-0 right-0 w-full max-w-md bg-background shadow-xl z-50',
          'transform transition-transform duration-300 ease-in-out',
          isOpen ? 'translate-x-0' : 'translate-x-full',
          className
        )}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-border">
            <h2 className="text-xl font-semibold">Messages</h2>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="text-muted-foreground hover:text-foreground"
            >
              <Icon name="X" className="h-5 w-5" />
            </Button>
          </div>
          
          {/* Messaging Container */}
          <div className="flex-1 overflow-hidden">
            <MessagingContainer
              currentUser={{
                id: currentUser.id,
                name: currentUser.name,
                avatar: currentUser.avatar,
                role: currentUser.role === 'CLIENT' ? UserRole.CLIENT : UserRole.FREELANCER,
                email: (currentUser as any).email || undefined,
                isOnline: (currentUser as any).isOnline || false,
              }}
              currentUserRole={currentUserRole === 'CLIENT' ? UserRole.CLIENT : UserRole.FREELANCER}
              initialConversations={conversations}
              onSendMessage={onSendMessage}
              onLoadMoreMessages={onLoadMoreMessages}
              onConversationSelect={onConversationSelect}
              onFileUpload={onFileUpload}
              showBackButton={false}
              onBack={onClose}
              className="h-full"
            />
          </div>
        </div>
      </div>
    </>
  );
}
