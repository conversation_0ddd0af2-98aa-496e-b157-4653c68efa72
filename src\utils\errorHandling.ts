
export class AppError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly context?: Record<string, unknown>,
    public readonly isUserFriendly: boolean = true
  ) {
    super(message);
    this.name = 'AppError';
  }
}

export const handleError = (error: unknown, defaultMessage = 'An unexpected error occurred') => {
  console.error('Error:', error);
  
  let message = defaultMessage;
  let title = 'Error';
  let variant: 'default' | 'destructive' | 'success' = 'destructive';
  
  if (error instanceof AppError) {
    message = error.message;
    title = error.code;
    
    if (error.context) {
      console.error('Error context:', error.context);
    }
  } else if (error instanceof Error) {
    message = error.message;
  }
  
  return { 
    message, 
    title, 
    variant,
    isUserFriendly: error instanceof AppError ? error.isUserFriendly : false 
  };
};

export const withErrorHandling = async <T>(
  operation: () => Promise<T>,
  options: {
    successMessage?: string;
    errorMessage?: string;
    onSuccess?: (data: T) => void;
    onError?: (error: unknown) => void;
    showToast?: (message: string, options?: { variant?: 'default' | 'destructive' | 'success' }) => void;
  } = {}
): Promise<{ data?: T; error?: Error; toastInfo?: { message: string; variant: 'default' | 'destructive' | 'success' } }> => {
  try {
    const data = await operation();
    
    if (options.successMessage && options.showToast) {
      options.showToast(options.successMessage, { variant: 'success' });
    }
    
    options.onSuccess?.(data);
    return { 
      data,
      toastInfo: options.successMessage ? {
        message: options.successMessage,
        variant: 'success'
      } : undefined
    };
  } catch (error) {
    const { message, variant, isUserFriendly } = handleError(error, options.errorMessage);
    
    if (!isUserFriendly) {
      console.error('Unhandled error:', error);
    }
    
    if (options.showToast) {
      options.showToast(message, { variant });
    }
    
    options.onError?.(error);
    return { 
      error: error instanceof Error ? error : new Error(String(error)),
      toastInfo: { message, variant }
    };
  }
};
