import { graphQLClient } from '../../lib/graphql/graphqlClient';
import { GET_JOB, LIST_JOBS } from './job.queries';
import {
  CREATE_JOB,
  UPDATE_JOB,
  DELETE_JOB,
  SUBMIT_PROPOSAL,
  UPDATE_PROPOSAL_STATUS,
  WITHD<PERSON>W_PROPOSAL
} from './job.mutations';
import type { 
  Job, 
  CreateJobInput, 
  UpdateJobInput, 
  JobWithProposalList
} from '../../types/features/jobs/job.types';
import type { JobFilter, TransformedJobFilter, JobFilterCondition } from '../../types/features/jobs/filter.types';
import type { JobProposal, CreateJobProposalInput, ProposalStatus } from '../../types/features/proposals/proposal.types';

export const jobApi = {
  createJob: async (input: CreateJobInput) => {
    const response = await graphQLClient.mutate<{ createJob: Job }>(
      CREATE_JOB,
      { input }
    );
    return response.createJob;
  },

  getJob: async (id: string) => {
    const response = await graphQLClient.execute<{ getJob: JobWithProposalList }>(
      GET_JOB,
      { id },
      { authMode: 'userPool' }
    );
    return response.getJob;
  },

  updateJob: async (input: UpdateJobInput) => {
    const response = await graphQLClient.mutate<{ updateJob: Job }>(
      UPDATE_JOB,
      { input }
    );
    return response.updateJob;
  },

  deleteJob: async (id: string) => {
    await graphQLClient.mutate<{ deleteJob: { id: string } }>(
      DELETE_JOB,
      { input: { id } },
      { authMode: 'userPool' }
    );
  },

  listJobs: async (filter?: JobFilter) => {
    const response = await graphQLClient.query<{ listJobs: { items: Job[]; nextToken?: string } }>(
      LIST_JOBS,
      {
        filter: filter ? jobApi.transformFilter(filter) : undefined,
        limit: filter?.limit,
        nextToken: filter?.nextToken
      }
    );
    return response?.listJobs || { items: [], nextToken: undefined };
  },

  submitProposal: async (input: CreateJobProposalInput) => {
    const response = await graphQLClient.mutate<{ createProposal: JobProposal }>(
      SUBMIT_PROPOSAL,
      { input }
    );
    return response.createProposal;
  },

  updateProposalStatus: async (input: { id: string; status: ProposalStatus }) => {
    const response = await graphQLClient.mutate<{ updateProposal: JobProposal }>(
      UPDATE_PROPOSAL_STATUS,
      { input }
    );
    return response.updateProposal;
  },

  withdrawProposal: async (proposalId: string) => {
    await graphQLClient.mutate<{ deleteProposal: { id: string } }>(
      WITHDRAW_PROPOSAL,
      { input: { id: proposalId } },
      { authMode: 'userPool' }
    );
  },

  /**
   * Transforms the job filter into a format suitable for GraphQL queries
   * @param filter The job filter to transform
   * @returns Transformed filter object for GraphQL queries
   */
  transformFilter(filter: JobFilter): TransformedJobFilter {
    const transformed: TransformedJobFilter = {};
    
    if (filter.clientId) {
      transformed.clientId = { eq: filter.clientId };
    }
    
    if (filter.category) {
      transformed.category = { eq: filter.category };
    } else if (filter.categories?.length) {
      transformed.category = { in: filter.categories };
    }
    
    if (filter.minBudget !== undefined || filter.maxBudget !== undefined) {
      transformed.budget = {};
      
      if (filter.minBudget !== undefined) {
        transformed.budget.ge = filter.minBudget;
      }
      
      if (filter.maxBudget !== undefined) {
        transformed.budget.le = filter.maxBudget;
      }
      
      if (filter.minBudget !== undefined && filter.maxBudget !== undefined) {
        transformed.budget = { between: [filter.minBudget, filter.maxBudget] };
      }
    }
    
    if (filter.jobType) {
      transformed.type = { eq: filter.jobType };
    }
    
    if (filter.status) {
      transformed.status = { eq: filter.status };
    }
    
    if (filter.isRemote !== undefined) {
      transformed.isRemote = { eq: filter.isRemote };
    }
    
    if (filter.skills?.length) {
      if (filter.skills.length === 1) {
        transformed.skills = { contains: filter.skills[0] };
      } else {
        transformed.skills = { in: filter.skills };
      }
    }
    
    const searchTerm = filter.searchTerm || filter.query;
    if (searchTerm) {
      transformed.title = { contains: searchTerm };
    }
    
    if (filter.isRemote !== undefined) {
      transformed.isRemote = { eq: filter.isRemote };
    }
    
    if (filter.status) {
      transformed.status = { eq: filter.status };
    }
    
    if (filter.searchTerm) {
      transformed.or = [
        { title: { contains: filter.searchTerm } },
        { description: { contains: filter.searchTerm } }
      ] as unknown as JobFilterCondition[];
    }
    
    return transformed;
  }
};
