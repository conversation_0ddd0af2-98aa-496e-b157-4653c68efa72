import { gql } from '@apollo/client';

export const CREATE_PAYMENT = gql`
  mutation CreatePayment($input: CreatePaymentInput!) {
    createPayment(input: $input) {
      id
      contractId
      amount
      status
      method
      paidAt
      createdAt
      updatedAt
      contract {
        id
        title
        status
      }
    }
  }
`;

export const UPDATE_PAYMENT = gql`
  mutation UpdatePayment($input: UpdatePaymentInput!) {
    updatePayment(input: $input) {
      id
      contractId
      amount
      status
      method
      paidAt
      createdAt
      updatedAt
    }
  }
`;

export const UPDATE_PAYMENT_STATUS = gql`
  mutation UpdatePayment($input: UpdatePaymentInput!) {
    updatePayment(input: $input) {
      id
      status
      paidAt
      updatedAt
    }
  }
`;

export const DELETE_PAYMENT = gql`
  mutation DeletePayment($input: DeletePaymentInput!) {
    deletePayment(input: $input) {
      id
    }
  }
`;

export const PROCESS_PAYMENT = gql`
  mutation ProcessPayment($input: ProcessPaymentInput!) {
    processPayment(input: $input) {
      id
      status
      paidAt
      amount
      contract {
        id
        status
      }
    }
  }
`;

export const PROCESS_REFUND = gql`
  mutation ProcessRefund($input: ProcessRefundInput!) {
    processRefund(input: $input) {
      id
      status
      paidAt
      contract {
        id
        status
      }
    }
  }
`;

export const BULK_UPDATE_PAYMENTS = gql`
  mutation BulkUpdatePayments($payments: [UpdatePaymentInput!]!) {
    bulkUpdatePayments(payments: $payments) {
      successful {
        id
        status
        updatedAt
      }
      failed {
        id
        error
      }
    }
  }
`;