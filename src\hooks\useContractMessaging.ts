import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth/AuthContext';
import { UserRole } from '@/types/features/auth/auth.types';
import { ExtendedContract } from '@/types/features/contracts/contract.types';
import { contractMessagingService } from '@/utils/contracts/contractMessagingService';

/**
 * Hook for handling contract-based messaging functionality
 */
export const useContractMessaging = () => {
  const router = useRouter();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  /**
   * Navigate to messaging for a specific contract
   */
  const navigateToContractMessaging = useCallback(async (
    contract: ExtendedContract,
    onSuccess?: (url: string) => void,
    onError?: (error: Error) => void
  ) => {
    if (!user) {
      const error = new Error('User must be authenticated to send messages');
      setError(error);
      onError?.(error);
      return;
    }

    const currentUserId = user.attributes?.sub || user.username;
    const userRole = user.attributes['custom:role'] as UserRole;

    if (!currentUserId) {
      const error = new Error('Unable to identify current user');
      setError(error);
      onError?.(error);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const messageUrl = await contractMessagingService.initiateContractMessaging(
        contract,
        currentUserId,
        userRole
      );
      
      router.push(messageUrl);
      onSuccess?.(messageUrl);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to open messaging');
      setError(error);
      onError?.(error);
    } finally {
      setIsLoading(false);
    }
  }, [user, router]);

  /**
   * Send a message directly within a contract context
   */
  const sendContractMessage = useCallback(async (
    contract: ExtendedContract,
    message: string,
    onSuccess?: () => void,
    onError?: (error: Error) => void
  ) => {
    if (!user) {
      const error = new Error('User must be authenticated to send messages');
      setError(error);
      onError?.(error);
      return;
    }

    const currentUserId = user.attributes?.sub || user.username;
    const userRole = user.attributes['custom:role'] as UserRole;

    if (!currentUserId) {
      const error = new Error('Unable to identify current user');
      setError(error);
      onError?.(error);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await contractMessagingService.sendContractMessage(
        contract,
        currentUserId,
        userRole,
        message
      );
      
      onSuccess?.();
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to send message');
      setError(error);
      onError?.(error);
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  /**
   * Check if current user can message about this contract
   */
  const canMessageAboutContract = useCallback((contract: ExtendedContract): boolean => {
    if (!user) return false;

    const currentUserId = user.attributes?.sub || user.username;
    const userRole = user.attributes['custom:role'] as UserRole;

    if (!currentUserId) return false;

    const recipient = contractMessagingService.getContractMessagingRecipient(
      contract,
      currentUserId,
      userRole
    );

    return recipient?.isValidParty ?? false;
  }, [user]);

  /**
   * Get messaging recipient information for a contract
   */
  const getMessagingRecipient = useCallback((contract: ExtendedContract) => {
    if (!user) return null;

    const currentUserId = user.attributes?.sub || user.username;
    const userRole = user.attributes['custom:role'] as UserRole;

    if (!currentUserId) return null;

    return contractMessagingService.getContractMessagingRecipient(
      contract,
      currentUserId,
      userRole
    );
  }, [user]);

  return {
    navigateToContractMessaging,
    sendContractMessage,
    canMessageAboutContract,
    getMessagingRecipient,
    isLoading,
    error,
  };
};