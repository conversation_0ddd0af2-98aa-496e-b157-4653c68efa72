import { gql } from '@apollo/client';

export const CREATE_SKILL = gql`
  mutation CreateSkill($input: CreateSkillInput!) {
    createSkill(input: $input) {
      id
      name
      description
      isActive
      jobCategoryId
      createdAt
      updatedAt
    }
  }
`;

export const UPDATE_SKILL = gql`
  mutation UpdateSkill($input: UpdateSkillInput!) {
    updateSkill(input: $input) {
      id
      name
      description
      isActive
      jobCategoryId
      createdAt
      updatedAt
    }
  }
`;

export const DELETE_SKILL = gql`
  mutation DeleteSkill($input: DeleteSkillInput!) {
    deleteSkill(input: $input) {
      id
    }
  }
`;