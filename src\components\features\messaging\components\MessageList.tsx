"use client";

import { cn } from "@/lib/utils";
import useToaster from "@/hooks/useToaster";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/Avatar";
import { Button } from "@/components/ui/Button";
import { Icon } from "@/components/ui/Icon";
import { getProfilePhotoUrl } from "@/utils/profilePhoto";
import { useState, useCallback, useEffect, useRef, useMemo } from "react";
import {
  UIMessage,
  MessagingUser,
} from "@/types/features/messaging/messaging.types";
import {
  safeFormatDate,
  shouldShowDate,
  formatMessageTime,
} from "@/utils/messaging/dateUtils";
import { filterValidMessages } from "@/utils/messaging/validationUtils";

export interface MessageListProps {
  messages: UIMessage[];
  currentUser: MessagingUser;
  otherUser: MessagingUser;
  loading?: boolean;
  onLoadMore?: () => void;
  hasMore?: boolean;
  className?: string;
}

export function MessageList({
  messages,
  currentUser,
  otherUser,
  loading = false,
  onLoadMore,
  hasMore = false,
  className,
}: MessageListProps) {
  const [showLoadMore, setShowLoadMore] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);
  const [isNearTop, setIsNearTop] = useState(false);

  const handleScroll = useCallback(() => {
    if (!scrollRef.current) return;

    const { scrollTop } = scrollRef.current;
    const isAtTop = scrollTop < 100;
    setIsNearTop(isAtTop);
    setShowLoadMore(isAtTop && hasMore && !loading);
  }, [hasMore, loading]);

  useEffect(() => {
    const scrollElement = scrollRef.current;
    if (scrollElement) {
      scrollElement.addEventListener("scroll", handleScroll);
      return () => scrollElement.removeEventListener("scroll", handleScroll);
    }
  }, [handleScroll]);

  useEffect(() => {
    if (scrollRef.current && !isNearTop) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [messages.length, isNearTop]);

  const { showError } = useToaster();

  const getUserInfo = (userId: string) => {
    return userId === currentUser.id ? currentUser : otherUser;
  };

  const validMessages = useMemo(() => {
    const filtered = filterValidMessages(messages);
    return [...filtered].sort(
      (a, b) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );
  }, [messages]);

  const getStatusIcon = (status: UIMessage["status"]) => {
    switch (status) {
      case "read":
        return "✓✓";
      case "delivered":
        return "✓";
      case "sent":
        return "○";
      default:
        return "";
    }
  };

  const handleFileDownload = (fileInfo: UIMessage["fileInfo"]) => {
    if (fileInfo?.url) {
      window.open(fileInfo.url, "_blank");
    }
  };

  if (loading && messages.length === 0) {
    return (
      <div className={cn("flex-1 flex items-center justify-center", className)}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p className="text-sm text-muted-foreground">Loading messages...</p>
        </div>
      </div>
    );
  }

  if (messages.length === 0) {
    return (
      <div
        className={cn(
          "flex-1 flex items-center justify-center px-6 py-12",
          className
        )}
      >
        <div className="text-center">
          <h3 className="text-2xl font-medium mb-4">No messages yet</h3>
          <p className="text-sm text-muted-foreground">
            Start the conversation by sending a message below.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex-1 flex flex-col overflow-hidden", className)}>
      {/* Load More Button */}
      {hasMore && showLoadMore && (
        <div className="flex justify-center mb-4">
          <Button
            variant="outline"
            size="sm"
            onClick={onLoadMore}
            disabled={loading}
            className="flex items-center gap-2"
          >
            {loading ? "Loading..." : "Load more messages"}
            <Icon name="ChevronUp" className="h-4 w-4" />
          </Button>
        </div>
      )}

      {/* Messages List */}
      <div ref={scrollRef} className="flex-1 overflow-y-auto p-4 space-y-4">
        {validMessages
          .map((message, index) => {
            try {
              const user = getUserInfo(message.senderId);
              const isCurrentUser = message.senderId === currentUser.id;
              const prevMessage = index > 0 ? validMessages[index - 1] : null;
              const showDate =
                index === 0 ||
                shouldShowDate(
                  new Date(message.createdAt),
                  prevMessage ? new Date(prevMessage.createdAt) : undefined
                );

              const messageDate = message.createdAt || new Date();
              const formattedDate = safeFormatDate(messageDate);

              return (
                <div key={message.id}>
                  {/* Date Separator */}
                  {showDate && (
                    <div className="flex items-center justify-center py-2">
                      <div className="bg-muted px-3 py-1 rounded-full">
                        <span className="text-xs text-muted-foreground font-medium">
                          {formattedDate}
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Message Row */}
                  <div
                    className={`flex items-start gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors ${
                      isCurrentUser ? "justify-end" : "justify-start"
                    }`}
                  >
                    {!isCurrentUser && (
                      <Avatar className="w-10 h-10 flex-shrink-0">
                        <AvatarImage
                          src={getProfilePhotoUrl(user.avatar)}
                          alt={user.name}
                        />
                        <AvatarFallback className="text-sm font-medium">
                          {user.name?.charAt(0).toUpperCase() || "?"}
                        </AvatarFallback>
                      </Avatar>
                    )}

                    {/* Message Content */}
                    <div
                      className={`flex-1 min-w-0 max-w-[80%] ${
                        isCurrentUser ? "text-right" : "text-left"
                      }`}
                    >
                      {/* Header */}
                      <div
                        className={`flex items-center gap-2 mb-1 ${
                          isCurrentUser ? "justify-end" : "justify-start"
                        }`}
                      >
                        <span className="font-medium text-sm text-foreground">
                          {isCurrentUser ? "You" : user.name}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {formatMessageTime(message.createdAt)}
                        </span>
                      </div>

                      {/* Message Body */}
                      <div
                        className={`text-sm text-foreground ${
                          isCurrentUser ? "flex justify-end" : ""
                        }`}
                      >
                        {message.type === "text" ? (
                          <div className="flex items-end gap-1">
                            <p
                              className={`whitespace-pre-wrap break-words px-3 py-2 rounded-lg ${
                                isCurrentUser
                                  ? "bg-primary text-primary-foreground"
                                  : "bg-muted"
                              }`}
                            >
                              {message.content}
                            </p>
                            {isCurrentUser && (
                              <span className="text-xs text-muted-foreground">
                                {getStatusIcon(message.status)}
                              </span>
                            )}
                          </div>
                        ) : message.type === "file" && message.fileInfo ? (
                          <div
                            className={`flex items-center gap-2 p-3 rounded-lg border ${
                              isCurrentUser
                                ? "bg-primary text-primary-foreground"
                                : "bg-muted"
                            }`}
                          >
                            <Icon name="FileText" className="h-4 w-4 mr-2" />
                            <div className="flex-1 min-w-0">
                              <p className="font-medium text-sm truncate">
                                {message.fileInfo.name}
                              </p>
                              <p className="text-xs opacity-80">
                                {message.fileInfo?.size
                                  ? (
                                      message.fileInfo.size /
                                      1024 /
                                      1024
                                    ).toFixed(2)
                                  : ""}{" "}
                                MB
                              </p>
                            </div>
                            <Button
                              variant={isCurrentUser ? "secondary" : "ghost"}
                              size="sm"
                              onClick={() =>
                                handleFileDownload(message.fileInfo)
                              }
                              className="flex-shrink-0"
                            >
                              <Icon name="Download" className="h-4 w-4" />
                            </Button>
                          </div>
                        ) : (
                          <p className="text-sm italic text-muted-foreground">
                            {message.content}
                          </p>
                        )}
                      </div>
                    </div>
                    {isCurrentUser && (
                      <Avatar className="w-10 h-10 flex-shrink-0">
                        <AvatarImage
                          src={
                            currentUser.avatar
                              ? getProfilePhotoUrl(currentUser.avatar)
                              : "/logo.png"
                          }
                          alt={currentUser.name}
                        />
                        <AvatarFallback className="text-sm font-medium">
                          {currentUser.name.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                    )}
                  </div>
                </div>
              );
            } catch (error) {
              console.error("Error rendering message:", error, message);
              showError("Failed to display a message");
              return null;
            }
          })
          .filter(Boolean)}

        {/* Loading indicator at bottom */}
        {loading && messages.length > 0 && (
          <div className="flex justify-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        )}
      </div>
    </div>
  );
}
