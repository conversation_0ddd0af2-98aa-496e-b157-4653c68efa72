"use client";

import { useState, useCallback, useEffect } from "react";
import { useRouter } from "next/navigation";
import dashboardService from "@/api/dashboard/dashboard.service";
import Link from "next/link";
import { Card } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Loading } from "@/components/ui/Loading";
import { Icon } from "@/components/ui/Icon";
import { ContentHeader } from "@/components/layout/ContentHeader";
import useToaster from "@/hooks/useToaster";
import { StatCard } from "@/components/features/dashboard/StatCard";
import { formatCurrency } from "@/utils/format";
import type { AdminDashboardData } from "@/api/dashboard/dashboard.types";

interface ExtendedDashboardStats extends AdminDashboardData {
  totalUsers: number;
  totalClients: number;
  totalFreelancers: number;
  totalAdmins: number;
  newUsersThisMonth: number;
  newJobsThisMonth: number;
  totalJobBudget: number;
  averageJobBudget: number;
  cancelledJobs: number;
  totalJobCategories: number;
}

const AdminDashboardPage = () => {
  const { showError } = useToaster();
  const router = useRouter();
  const [stats, setStats] = useState<ExtendedDashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);

      const dashboardData = (await dashboardService.getAdminDashboardData(
        true
      )) as ExtendedDashboardStats;

      setStats(dashboardData);
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      showError("Failed to load dashboard data");
    } finally {
      setLoading(false);
    }
  }, [showError]);

  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <Loading size="lg" />
      </div>
    );
  }

  return (
    <div className="w-full max-w-7xl mx-auto p-4 sm:p-6 space-y-6">
      <ContentHeader
        title="Admin Dashboard"
        subtitle="Real-time overview and insights of your freelance marketplace"
        breadcrumbs={[{ label: "Dashboard", current: true }]}
        actions={
          <div className="flex space-x-3">
            <Button size="sm" onClick={() => fetchDashboardData()}>
              <Icon name="RefreshCw" size="sm" className="mr-2" />
              Refresh
            </Button>
            <Link href="/admin/reports">
              <Button variant="outline" size="sm">
                <Icon name="FileText" size="sm" className="mr-2" />
                View Reports
              </Button>
            </Link>
          </div>
        }
      />

      {/* Key Performance Indicators */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
        {/* Total Users */}
        <StatCard
          title="Total Users"
          value={stats?.totalUsers || 0}
          icon="Users"
          iconColor="bg-blue-50 dark:bg-blue-900/20"
          loading={loading}
          onClick={() => router.push("/admin/users")}
          trend={{
            value: stats?.newUsersThisMonth || 0,
            period: "this month",
            isPositive: true,
          }}
          subtitle="Platform members"
          className="w-full"
        />

        {/* Total Jobs */}
        <StatCard
          title="Total Jobs"
          value={stats?.totalJobs || 0}
          icon="Briefcase"
          iconColor="bg-green-50 dark:bg-green-900/20"
          loading={loading}
          onClick={() => router.push("/admin/jobs")}
          trend={{
            value: stats?.newJobsThisMonth || 0,
            period: "this month",
            isPositive: true,
          }}
          subtitle="All job postings"
          className="w-full"
        />

        {/* Platform Revenue */}
        <StatCard
          title="Total Job Budget"
          value={stats?.totalJobBudget || 0}
          format="currency"
          icon="DollarSign"
          iconColor="bg-yellow-50 dark:bg-yellow-900/20"
          loading={loading}
          subtitle={`Avg: ${formatCurrency(stats?.averageJobBudget || 0)}`}
          className="w-full"
        />

        {/* Active Skills */}
        <StatCard
          title="Active Skills"
          value={stats?.activeSkills || 0}
          icon="Tag"
          iconColor="bg-purple-50 dark:bg-purple-900/20"
          loading={loading}
          onClick={() => router.push("/admin/skills")}
          percentage={
            stats?.totalSkills
              ? Math.round((stats.activeSkills / stats.totalSkills) * 100)
              : 0
          }
          subtitle={`of ${stats?.totalSkills || 0} total`}
          className="w-full"
        />
      </div>

      {/* Secondary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
        {/* Job Status Breakdown */}
        <StatCard
          title="Open Jobs"
          value={stats?.openJobs || 0}
          icon="Clock"
          iconColor="bg-blue-50 dark:bg-blue-900/20"
          loading={loading}
          changeType="neutral"
          badge={{
            text: "Active",
            variant: "default",
          }}
          className="w-full"
        />

        <StatCard
          title="In Progress"
          value={stats?.inProgressJobs || 0}
          icon="Clock"
          iconColor="bg-orange-50 dark:bg-orange-900/20"
          loading={loading}
          changeType="increase"
          className="w-full"
        />

        <StatCard
          title="Completed Jobs"
          value={stats?.completedJobs || 0}
          icon="CheckCircle"
          iconColor="bg-green-50 dark:bg-green-900/20"
          loading={loading}
          changeType="increase"
          className="w-full"
        />

        <StatCard
          title="Total Proposals"
          value={stats?.totalProposals || 0}
          icon="MessageSquare"
          iconColor="bg-indigo-50 dark:bg-indigo-900/20"
          loading={loading}
          subtitle="Platform engagement"
          className="w-full"
        />
      </div>

      {/* User Demographics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
        <StatCard
          title="Clients"
          value={stats?.totalClients || 0}
          icon="Building"
          iconColor="bg-green-50 dark:bg-green-900/20"
          loading={loading}
          onClick={() => router.push("/admin/users?role=CLIENT")}
          percentage={
            stats?.totalUsers
              ? Math.round((stats.totalClients / stats.totalUsers) * 100)
              : 0
          }
          className="w-full"
        />

        <StatCard
          title="Freelancers"
          value={stats?.totalFreelancers || 0}
          icon="UserPlus"
          iconColor="bg-blue-50 dark:bg-blue-900/20"
          loading={loading}
          onClick={() => router.push("/admin/users?role=FREELANCER")}
          percentage={
            stats?.totalUsers
              ? Math.round((stats.totalFreelancers / stats.totalUsers) * 100)
              : 0
          }
          className="w-full"
        />

        <StatCard
          title="Admins"
          value={stats?.totalAdmins || 0}
          icon="Users"
          iconColor="bg-red-50 dark:bg-red-900/20"
          loading={loading}
          onClick={() => router.push("/admin/users?role=ADMIN")}
          percentage={
            stats?.totalUsers
              ? Math.round((stats.totalAdmins / stats.totalUsers) * 100)
              : 0
          }
          className="w-full"
        />
      </div>

      {/* Recent Activity */}
      {/* Quick Actions & Management */}
      <Card className="p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Quick Actions
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Link href="/admin/jobs">
            <Button variant="outline" className="w-full justify-start">
              <Icon name="Briefcase" className="w-4 h-4 mr-2" />
              Manage Jobs
            </Button>
          </Link>
          <Link href="/admin/skills">
            <Button variant="outline" className="w-full justify-start">
              <Icon name="Tag" className="w-4 h-4 mr-2" />
              Manage Skills
            </Button>
          </Link>
          <Link href="/admin/users">
            <Button variant="outline" className="w-full justify-start">
              <Icon name="Users" className="w-4 h-4 mr-2" />
              Manage Users
            </Button>
          </Link>
          <Link href="/admin/reports">
            <Button variant="outline" className="w-full justify-start">
              <Icon name="FileText" className="w-4 h-4 mr-2" />
              View Reports
            </Button>
          </Link>
        </div>
      </Card>
    </div>
  );
};

export default AdminDashboardPage;
