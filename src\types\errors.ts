export interface ValidationErrorContext {
  [key: string]: any;
  receivedValue?: any;
  expectedFormat?: string;
}

export class ValidationError extends Error {
  constructor(
    message: string, 
    public field?: string,
    public context?: ValidationErrorContext
  ) {
    super(message);
    this.name = 'ValidationError';
    
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ValidationError);
    }
  }
  
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      field: this.field,
      context: this.context,
      stack: this.stack
    };
  }
}

export class AuthenticationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'AuthorizationError';
  }
}

export class ResourceNotFoundError extends Error {
  constructor(resource: string, id: string) {
    super(`${resource} with ID ${id} not found`);
    this.name = 'ResourceNotFoundError';
  }
}

export class RateLimitError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'RateLimitError';
  }
}

export type ServiceError = 
  | ValidationError
  | AuthenticationError
  | AuthorizationError
  | ResourceNotFoundError
  | RateLimitError
  | Error;
