"use client";
'use client';

import React, { useState } from "react";
import { CardElement } from "@stripe/react-stripe-js";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Alert, AlertDescription } from "../../ui/Alert";
import { FormField, Label } from "@/components/ui/Form";
import { Input } from "@/components/ui/Input";
import { Checkbox } from "@/components/ui/Checkbox";
import { Icon } from '@/components/ui/Icon';

interface PaymentMethodFormProps {
  onPaymentMethodReady?: (isReady: boolean) => void;
  onError?: (error: string) => void;
  showBillingAddress?: boolean;
  className?: string;
}

interface BillingDetails {
  name: string;
  email: string;
  address: {
    line1: string;
    line2: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };
}

export const PaymentMethodForm: React.FC<PaymentMethodFormProps> = ({
  onPaymentMethodReady,
  onError,
  showBillingAddress = true,
  className = "",
}) => {
  const [isComplete, setIsComplete] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [billingDetails, setBillingDetails] = useState<BillingDetails>({
    name: "",
    email: "",
    address: {
      line1: "",
      line2: "",
      city: "",
      state: "",
      postal_code: "",
      country: "US",
    },
  });
  const [savePaymentMethod, setSavePaymentMethod] = useState(false);

  const cardElementOptions = {
    style: {
      base: {
        fontSize: "16px",
        color: "#424770",
        "::placeholder": {
          color: "#aab7c4",
        },
        fontFamily: "system-ui, sans-serif",
        fontSmoothing: "antialiased",
      },
      invalid: {
        color: "#9e2146",
        iconColor: "#fa755a",
      },
    },
    hidePostalCode: !showBillingAddress,
  };

  const handleCardChange = (event: any) => {
    setError(event.error ? event.error.message : null);
    setIsComplete(event.complete);

    if (onPaymentMethodReady) {
      onPaymentMethodReady(event.complete && !event.error);
    }

    if (event.error && onError) {
      onError(event.error.message);
    }
  };

  const handleBillingChange = (field: string, value: string) => {
    if (field.startsWith("address.")) {
      const addressField = field.split(".")[1];
      setBillingDetails((prev) => ({
        ...prev,
        address: {
          ...prev.address,
          [addressField]: value,
        },
      }));
    } else {
      setBillingDetails((prev) => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Icon name="CreditCard" className="mr-2 h-4 w-4" />
          Payment Method
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Card Element */}
        <div className="space-y-2">
          <Label>Card Information</Label>
          <div className="p-3 border-2 border-input rounded-lg focus-within:border-ring transition-colors">
            <CardElement
              options={cardElementOptions}
              onChange={handleCardChange}
            />
          </div>
          {error && (
            <Alert variant="destructive">
              <Icon name="AlertCircle" className="h-4 w-4 text-red-500" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          {isComplete && !error && (
            <div className="flex items-center gap-2 text-sm text-green-600">
              <Icon name="CheckCircle" className="h-4 w-4 text-green-500" />
              <span>Card details are valid</span>
            </div>
          )}
        </div>

        {/* Billing Address */}
        {showBillingAddress && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Billing Address</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField label="Full Name" required>
                <Input
                  type="text"
                  placeholder="John Doe"
                  value={billingDetails.name}
                  onChange={(e) => handleBillingChange("name", e.target.value)}
                />
              </FormField>

              <FormField label="Email Address" required>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  value={billingDetails.email}
                  onChange={(e) => handleBillingChange("email", e.target.value)}
                />
              </FormField>
            </div>

            <FormField label="Address Line 1" required>
              <Input
                type="text"
                placeholder="123 Main Street"
                value={billingDetails.address.line1}
                onChange={(e) =>
                  handleBillingChange("address.line1", e.target.value)
                }
              />
            </FormField>

            <FormField label="Address Line 2">
              <Input
                type="text"
                placeholder="Apartment, suite, etc. (optional)"
                value={billingDetails.address.line2}
                onChange={(e) =>
                  handleBillingChange("address.line2", e.target.value)
                }
              />
            </FormField>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField label="City" required>
                <Input
                  type="text"
                  placeholder="New York"
                  value={billingDetails.address.city}
                  onChange={(e) =>
                    handleBillingChange("address.city", e.target.value)
                  }
                />
              </FormField>

              <FormField label="State" required>
                <Input
                  type="text"
                  placeholder="NY"
                  value={billingDetails.address.state}
                  onChange={(e) =>
                    handleBillingChange("address.state", e.target.value)
                  }
                />
              </FormField>

              <FormField label="Postal Code" required>
                <Input
                  type="text"
                  placeholder="10001"
                  value={billingDetails.address.postal_code}
                  onChange={(e) =>
                    handleBillingChange("address.postal_code", e.target.value)
                  }
                />
              </FormField>
            </div>
          </div>
        )}

        {/* Save Payment Method Option */}
        <div className="flex items-center space-x-2">
          <Checkbox
            id="save-payment-method"
            checked={savePaymentMethod}
            onChange={(e) =>
              setSavePaymentMethod(e.target.checked)
            }
          />
          <Label htmlFor="save-payment-method" className="text-sm">
            Save this payment method for future use
          </Label>
        </div>

        {/* Security Notice */}
        <div className="flex items-start gap-3 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <Icon name="Lock" className="mr-2 h-4 w-4" />
          <div className="text-sm">
            <p className="font-medium text-blue-900 mb-1">
              Secure Payment Processing
            </p>
            <p className="text-blue-700">
              Your payment information is encrypted using industry-standard SSL
              technology. We never store your complete card details on our
              servers.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default PaymentMethodForm;
