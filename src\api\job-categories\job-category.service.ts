import { jobCategoryApi } from './job-category.api';
import type {
  JobCategory as BaseJobCategory,
  CreateJobCategoryInput,
  UpdateJobCategoryInput,
  ModelJobCategoryFilterInput,
} from '@/types/features/job-categories/job-category.types';
import type { Skill } from '@/types/features/skills/skill.types';
import {
  ValidationError,
  ResourceNotFoundError,
  RateLimitError,
  type ServiceError,
} from '@/types/errors';

export interface JobCategory extends BaseJobCategory {
  skills?: Skill[];
}

function isServiceError(error: unknown): error is ServiceError {
  return error instanceof Error && 'name' in error;
}

function handleApiError(operation: string, error: unknown): never {
  if (isServiceError(error)) {
    throw error;
  }

  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  
  if (errorMessage.includes('not found')) {
    throw new ResourceNotFoundError('Job Category', 'unknown');
  }
  
  if (errorMessage.includes('rate limit') || errorMessage.includes('too many requests')) {
    throw new RateLimitError('Too many requests. Please try again later.');
  }
  
  console.error(`Error in ${operation}:`, error);
  throw new Error(`Failed to ${operation.toLowerCase()}: ${errorMessage}`);
}

export const jobCategoryService = {
  async createJobCategory(input: CreateJobCategoryInput): Promise<JobCategory> {
    if (!input.name?.trim()) {
      throw new ValidationError('Name is required', 'name');
    }

    try {
      return await jobCategoryApi.createJobCategory({
        ...input,
        isActive: input.isActive ?? true,
      });
    } catch (error) {
      return handleApiError('create job category', error);
    }
  },

  async updateJobCategory(input: UpdateJobCategoryInput): Promise<JobCategory> {
    if (!input.id) {
      throw new ValidationError('ID is required', 'id');
    }
    if (input.name && !input.name.trim()) {
      throw new ValidationError('Name cannot be empty', 'name');
    }

    try {
      return await jobCategoryApi.updateJobCategory(input);
    } catch (error) {
      return handleApiError('update job category', error);
    }
  },

  async deleteJobCategory(id: string): Promise<boolean> {
    if (!id) {
      throw new ValidationError('ID is required', 'id');
    }

    try {
      await jobCategoryApi.deleteJobCategory({ id });
      return true;
    } catch (error) {
      return handleApiError('delete job category', error);
    }
  },

  async getJobCategory(id: string, includeSkills: boolean = false): Promise<JobCategory | null> {
    try {
      const category = await jobCategoryApi.getJobCategory(id, includeSkills);
      if (!category) return null;
      
      if (includeSkills && !category.skills) {
        const skillsResponse = await jobCategoryApi.listSkillsByCategory(id);
        return { ...category, skills: skillsResponse.items };
      }
      
      return category as JobCategory;
    } catch (error) {
      if (error instanceof Error && error.message.includes('not found')) {
        return null;
      }
      return handleApiError('get job category', error);
    }
  },

  async listJobCategoriesWithSkills(
    filter?: ModelJobCategoryFilterInput,
    limit?: number,
    nextToken?: string
  ) {
    try {
      const response = await jobCategoryApi.listJobCategories(filter, limit, nextToken, true);
      
      const categoriesWithSkills = await Promise.all(
        response.items.map(async (category) => {
          if (category.skills) return category;
          const skillsResponse = await jobCategoryApi.listSkillsByCategory(category.id);
          return { ...category, skills: skillsResponse.items };
        })
      );

      return {
        ...response,
        items: categoriesWithSkills,
      };
    } catch (error) {
      return handleApiError('list job categories with skills', error);
    }
  },

  async listJobCategories(filter?: ModelJobCategoryFilterInput, limit?: number, nextToken?: string) {
    try {
      return await jobCategoryApi.listJobCategories(filter, limit, nextToken);
    } catch (error) {
      return handleApiError('list job categories', error);
    }
  },

  async searchJobCategories(searchTerm: string): Promise<JobCategory[]> {
    if (!searchTerm?.trim()) {
      return [];
    }

    try {
      return await jobCategoryApi.searchJobCategories(searchTerm);
    } catch (error) {
      return handleApiError('search job categories', error);
    }
  },
};

export default jobCategoryService;
