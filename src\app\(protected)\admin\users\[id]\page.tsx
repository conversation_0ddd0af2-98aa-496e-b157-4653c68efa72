"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter, useParams } from "next/navigation";

import { useAuth } from "@/lib/auth/AuthContext";
import { userService } from "@/api/users/user.service";
import { jobService } from "@/api/jobs/job.service";
import { contractService } from "@/api/contracts/contract.service";
import { proposalService } from "@/api/proposals/proposal.service";
import { getProfilePhotoUrl } from "@/utils/profilePhoto";
import { User, UserStatistics } from "@/types/features/user/user.types";
import { UserRole } from "@/types/enums";
import { Icon } from "@/components/ui/Icon";
import { ContentHeader } from "@/components/layout/ContentHeader";
import {
  ProfileSummaryCard,
  BiographySkillsCard,
  ActivityStatsGrid,
} from "@/components/layout";
import { Button } from "@/components/ui/Button";
import { Card, CardContent } from "@/components/ui/Card";
import { ConfirmDialog } from "@/components/ui/ConfirmDialog";
import useToaster from "@/hooks/useToaster";

export default function ViewUserPage() {
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const router = useRouter();
  const params = useParams();
  const { showSuccess, showError } = useToaster();
  const [isLoading, setIsLoading] = useState(true);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [userData, setUserData] = useState<User | null>(null);
  const [profilePhotoUrl, setProfilePhotoUrl] = useState<string | undefined>();
  const [userStats, setUserStats] = useState<UserStatistics | null>(null);
  const [statsLoading, setStatsLoading] = useState(true);

  const loadUser = useCallback(async () => {
    if (!isAuthenticated || !params.id) return;

    try {
      setIsLoading(true);
      const user = await userService.getUserById(params.id as string);
      setUserData(user);

      if (user.profilePhoto) {
        try {
          const validAvatarUrl = getProfilePhotoUrl(user.profilePhoto);
          if (validAvatarUrl && validAvatarUrl.startsWith("http")) {
            setProfilePhotoUrl(validAvatarUrl);
          }
        } catch (error) {
          console.error("Error processing profile photo:", error);
        }
      }

      setStatsLoading(true);
      try {
        const statistics = await fetchUserStatistics(user.id, user.role);
        setUserStats(statistics);
      } catch (error) {
        console.error("Error loading user statistics:", error);
      } finally {
        setStatsLoading(false);
      }
    } catch (err) {
      console.error("Error loading user:", err);
      showError("Failed to load user data. Please try again.");
      router.push("/admin/users");
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, params.id, router, showError]);

  const fetchUserStatistics = async (
    userId: string,
    userRole: UserRole
  ): Promise<UserStatistics> => {
    const stats: UserStatistics = {
      totalJobs: 0,
      activeJobs: 0,
      completedJobs: 0,
      totalProposals: 0,
      acceptedProposals: 0,
      activeContracts: 0,
      completedContracts: 0,
      totalEarned: 0,
      totalSpent: 0,
    };

    try {
      if (userRole === UserRole.CLIENT) {
        const jobs = await jobService.listMyJobs(userId);
        stats.totalJobs = jobs.length;
        stats.activeJobs = jobs.filter(
          (job) => job.status === "OPEN" || job.status === "IN_PROGRESS"
        ).length;
        stats.completedJobs = jobs.filter(
          (job) => job.status === "COMPLETED"
        ).length;

        const contracts = await contractService.getUserContracts(userId);
        stats.activeContracts = contracts.filter(
          (c) => c.status === "ACTIVE" || c.status === "WORK_SUBMITTED"
        ).length;
        stats.completedContracts = contracts.filter(
          (c) => c.status === "COMPLETED" || c.status === "PAID"
        ).length;

        stats.totalSpent = contracts
          .filter((c) => c.status === "PAID" || c.status === "COMPLETED")
          .reduce((sum, contract) => sum + (contract.budget || 0), 0);
      }

      if (userRole === UserRole.FREELANCER) {
        const proposals = await proposalService.listMyProposals(userId);
        stats.totalProposals = proposals.length;
        stats.acceptedProposals = proposals.filter(
          (p) => p.status === "ACCEPTED"
        ).length;

        const contracts = await contractService.getUserContracts(userId);
        stats.activeContracts = contracts.filter(
          (c) => c.status === "ACTIVE" || c.status === "WORK_SUBMITTED"
        ).length;
        stats.completedContracts = contracts.filter(
          (c) => c.status === "COMPLETED" || c.status === "PAID"
        ).length;

        stats.totalEarned = contracts
          .filter((c) => c.status === "PAID" || c.status === "COMPLETED")
          .reduce((sum, contract) => sum + (contract.budget || 0), 0);
      }
    } catch (error) {
      console.error("Error fetching user statistics:", error);
    }

    return stats;
  };

  useEffect(() => {
    loadUser();
  }, [loadUser]);

  const handleEdit = () => {
    router.push(`/admin/users/${params.id}/edit`);
  };

  const handleDelete = () => {
    setShowDeleteDialog(true);
  };

  const handleConfirmDelete = async () => {
    if (!userData) return;

    try {
      setDeleteLoading(true);
      await userService.deleteUser(userData.id);

      showSuccess("User deleted successfully");

      router.push("/admin/users");
    } catch (err) {
      console.error("Error deleting user:", err);
      showError("Failed to delete user. Please try again.");
    } finally {
      setDeleteLoading(false);
      setShowDeleteDialog(false);
    }
  };

  const handleCancelDelete = () => {
    setShowDeleteDialog(false);
  };

  if (
    !authLoading &&
    (!isAuthenticated || user?.attributes?.["custom:role"] !== "ADMIN")
  ) {
    router.push("/login");
    return null;
  }

  if (authLoading || !isAuthenticated || isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Icon name="Loader2" size="xl" className="animate-spin text-blue-500" />
      </div>
    );
  }

  if (!userData) {
    return (
      <div className="w-full max-w-7xl mx-auto p-4 sm:p-6 space-y-6">
        <Card>
          <CardContent className="text-center py-12">
            <Icon
              name="AlertCircle"
              size="xl"
              className="mx-auto text-destructive mb-4"
            />
            <h2 className="text-xl font-semibold text-foreground mb-2">
              User Not Found
            </h2>
            <p className="text-muted-foreground mb-6">
              The user you&apos;re looking for doesn&apos;t exist or may have
              been deleted.
            </p>
            <Button onClick={() => router.push("/admin/users")}>
              <Icon name="ArrowLeft" size="sm" className="mr-2" />
              Back to Users
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full max-w-7xl mx-auto p-4 sm:p-6 space-y-6">
      <ContentHeader
        title={userData.name}
        subtitle="View and manage user details"
        breadcrumbs={[
          { label: "Dashboard", href: "/admin/dashboard" },
          { label: "Users", href: "/admin/users" },
          { label: userData.name, current: true },
        ]}
        showBackButton={true}
      />

      <div className="space-y-6">
        {/* Profile Summary with Integrated Quick Actions */}
        <ProfileSummaryCard
          user={userData}
          profilePhotoUrl={profilePhotoUrl}
          onEdit={handleEdit}
          onDelete={handleDelete}
          editLoading={false}
          deleteLoading={deleteLoading}
          loading={isLoading}
        />

        {/* Biography + Skills */}
        <BiographySkillsCard user={userData} loading={isLoading} />

        {/* Activity & Statistics */}
        <ActivityStatsGrid
          stats={userStats}
          userRole={userData.role}
          loading={statsLoading}
        />
      </div>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={showDeleteDialog}
        title="Delete User Account"
        message={`Are you sure you want to permanently delete "${userData.name}"? This action cannot be undone and will remove all user data, including profile information, skills, and any associated records.`}
        confirmText={deleteLoading ? "Deleting..." : "Delete User"}
        cancelText="Cancel"
        confirmVariant="destructive"
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        isLoading={deleteLoading}
      />
    </div>
  );
}
