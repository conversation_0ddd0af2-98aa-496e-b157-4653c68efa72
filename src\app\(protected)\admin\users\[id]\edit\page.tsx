'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";
import { userService } from '@/api/users/user.service';
import { UpdateUserInput, User } from "@/types/features/user/user.types";
import { UserRole } from "@/types/enums";
import { Icon } from '@/components/ui/Icon';
import { ContentHeader } from '@/components/layout/ContentHeader';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Textarea } from '@/components/ui/Textarea';
import { useToast } from '@/components/ui/toast';

export default function EditUserPage() {
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const router = useRouter();
  const params = useParams();
  const { showToast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [userData, setUserData] = useState<User | null>(null);

  const [formData, setFormData] = useState<UpdateUserInput>({
    id: params.id as string,
    name: "",
    email: "",
    bio: "",
    skills: [],
    profilePhoto: "",
  });

  useEffect(() => {
    const loadUser = async () => {
      if (!isAuthenticated || !params.id) return;

      try {
        setIsLoading(true);
        const user = await userService.getUserById(params.id as string);
        setUserData(user);
        
        setFormData({
          id: user.id,
          name: user.name,
          email: user.email,
          bio: user.bio || "",
          skills: user.skills || [],
          profilePhoto: user.profilePhoto || "",
        });
      } catch (err) {
        console.error('Error loading user:', err);
        setError('Failed to load user data.');
        showToast('Error', {
          description: 'Failed to load user data. Please try again.',
          position: "top-right",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadUser();
  }, [isAuthenticated, params.id, showToast]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setIsSubmitting(true);
      setError(null);
      
      if (!formData.name || !formData.name.trim()) {
        setError('User name is required.');
        return;
      }

      if (!formData.email || !formData.email.trim()) {
        setError('User email is required.');
        return;
      }

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        setError('Please enter a valid email address.');
        return;
      }

      await userService.updateUser(formData);
      
      showToast('Success', {
        description: 'User updated successfully',
        position: "top-right",
      });
      
      router.push('/admin/users');
    } catch (err) {
      console.error('Error updating user:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to update user. Please try again.';
      setError(errorMessage);
      showToast('Error', {
        description: errorMessage,
        position: "top-right",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    router.push('/admin/users');
  };

  const handleSkillsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const skillsText = e.target.value;
    const skillsArray = skillsText
      .split(',')
      .map(skill => skill.trim())
      .filter(skill => skill.length > 0);
    setFormData({ ...formData, skills: skillsArray });
  };

  if (!authLoading && (!isAuthenticated || user?.attributes?.['custom:role'] !== 'ADMIN')) {
    router.push('/login');
    return null;
  }

  if (authLoading || !isAuthenticated || isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Icon name="Loader2" size="xl" className="animate-spin text-blue-500" />
      </div>
    );
  }

  if (!userData) {
    return (
      <div className="w-full max-w-6xl mx-auto p-4 sm:p-6">
        <div className="text-center py-8">
          <Icon name="AlertCircle" size="xl" className="mx-auto text-red-500 mb-4" />
          <h2 className="text-lg font-semibold text-gray-900 mb-2">User Not Found</h2>
          <p className="text-gray-600 mb-4">The user you&apos;re looking for doesn&apos;t exist.</p>
          <Button onClick={() => router.push('/admin/users')}>
            Back to Users
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-4 sm:p-6">
      <ContentHeader
        title="Edit User"
        subtitle="Update the user details below."
        breadcrumbs={[
          { label: 'Dashboard', href: '/admin/dashboard' },
          { label: 'Users', href: '/admin/users' },
          { label: userData.name, href: `/admin/users/${userData.id}` },
          { label: 'Edit', current: true }
        ]}
        showBackButton={true}
      />
      
      {error && (
        <div className="rounded-md bg-red-50 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <Icon name="XCircle" size="md" className="text-red-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}
      
      <div className="bg-card rounded-lg shadow-sm p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Full Name *
              </label>
              <Input
                type="text"
                value={formData.name || ''}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Enter full name"
                required
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Address *
              </label>
              <Input
                type="email"
                value={formData.email || ''}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                placeholder="Enter email address"
                required
                className="w-full"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Profile Photo URL
            </label>
            <Input
              type="url"
              value={formData.profilePhoto || ''}
              onChange={(e) => setFormData({ ...formData, profilePhoto: e.target.value })}
              placeholder="Enter profile photo URL"
              className="w-full"
            />
            <p className="mt-1 text-sm text-gray-500">
              Enter a valid URL to the user&apos;s profile photo (optional).
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Biography
            </label>
            <Textarea
              value={formData.bio || ''}
              onChange={(e) => setFormData({ ...formData, bio: e.target.value })}
              placeholder="Enter user biography"
              rows={4}
              className="w-full"
            />
            <p className="mt-1 text-sm text-gray-500">
              A brief description about the user (optional).
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Skills (comma separated)
            </label>
            <Input
              type="text"
              value={formData.skills?.join(', ') || ''}
              onChange={handleSkillsChange}
              placeholder="e.g. React, Node.js, TypeScript, Design"
              className="w-full"
            />
            <p className="mt-1 text-sm text-gray-500">
              Enter skills separated by commas. Relevant for freelancers.
            </p>
          </div>

          {/* Role Display (Read-only) */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              User Role
            </label>
            <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500">
              {userData.role === UserRole.ADMIN && 'Administrator'}
              {userData.role === UserRole.CLIENT && 'Client'}
              {userData.role === UserRole.FREELANCER && 'Freelancer'}
            </div>
            <p className="mt-1 text-sm text-gray-500">
              User roles cannot be changed through this interface for security reasons.
            </p>
          </div>

          <div className="flex justify-end space-x-3 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
              className="min-w-[100px]"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !formData.name || !formData.name.trim() || !formData.email || !formData.email.trim()}
              className="min-w-[100px]"
            >
              {isSubmitting ? (
                <>
                  <Icon name="Loader2" size="sm" className="animate-spin mr-2" />
                  Updating...
                </>
              ) : (
                <>
                  <Icon name="Check" size="sm" className="mr-2" />
                  Update User
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
