import { skillApi } from './skill.api';
import { 
  Skill, 
  CreateSkillInput, 
  UpdateSkillInput, 
  SkillFilter 
} from '@/types/features/skills/skill.types';
import { createError, ErrorCode, isAppError } from '@/utils/errorHandler';

const handleApiError = (operation: string, error: unknown): never => {
  if (isAppError(error)) {
    throw error;
  }

  const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
  console.error(`[SkillService] Error in ${operation}:`, error);

  throw createError(
    `Failed to ${operation.toLowerCase()}: ${errorMessage}`,
    ErrorCode.INTERNAL_SERVER_ERROR,
    { statusCode: 500 }
  );
};

export const skillService = {
  /**
   * Create a new skill
   */
  async createSkill(input: CreateSkillInput): Promise<Skill> {
    try {
      if (!input.name?.trim()) {
        throw createError(
          'Skill name is required',
          ErrorCode.VALIDATION_ERROR,
          { statusCode: 400 }
        );
      }

      if (!input.jobCategoryId?.trim()) {
        throw createError(
          'Job category is required',
          ErrorCode.VALIDATION_ERROR,
          { statusCode: 400 }
        );
      }

      const skill = await skillApi.createSkill(input);
      if (!skill) {
        throw createError(
          'Failed to create skill: No data returned',
          ErrorCode.INTERNAL_SERVER_ERROR,
          { statusCode: 500 }
        );
      }

      return skill;
    } catch (error) {
      return handleApiError('createSkill', error);
    }
  },

  /**
   * Get a skill by ID
   */
  async getSkill(id: string): Promise<Skill> {
    try {
      if (!id) {
        throw createError(
          'Skill ID is required',
          ErrorCode.VALIDATION_ERROR,
          { statusCode: 400 }
        );
      }

      const skill = await skillApi.getSkill(id);
      if (!skill) {
        throw createError(
          `Skill with ID ${id} not found`,
          ErrorCode.NOT_FOUND,
          { statusCode: 404 }
        );
      }

      return skill;
    } catch (error) {
      return handleApiError('getSkill', error);
    }
  },

  /**
   * Update an existing skill
   */
  async updateSkill(input: UpdateSkillInput): Promise<Skill> {
    try {
      if (!input.id) {
        throw createError(
          'Skill ID is required for update',
          ErrorCode.VALIDATION_ERROR,
          { statusCode: 400 }
        );
      }

      const updatedSkill = await skillApi.updateSkill(input);
      if (!updatedSkill) {
        throw createError(
          `Skill with ID ${input.id} not found or update failed`,
          ErrorCode.NOT_FOUND,
          { statusCode: 404 }
        );
      }

      return updatedSkill;
    } catch (error) {
      return handleApiError('updateSkill', error);
    }
  },

  /**
   * Delete a skill by ID
   */
  async deleteSkill(id: string): Promise<void> {
    try {
      if (!id) {
        throw createError(
          'Skill ID is required',
          ErrorCode.VALIDATION_ERROR,
          { statusCode: 400 }
        );
      }

      await skillApi.deleteSkill(id);
    } catch (error) {
      return handleApiError('deleteSkill', error);
    }
  },

  /**
   * List skills with optional filtering and pagination
   */
  async listSkills(filter: SkillFilter = {}): Promise<{ items: Skill[]; nextToken?: string }> {
    try {
      const result = await skillApi.listSkills(filter);
      
      const items = (result.items || []).map(skill => ({
        ...skill,
        name: skill.name || 'Unnamed Skill',
        jobCategoryId: skill.jobCategoryId || '',
        isActive: skill.isActive !== false,
        createdAt: skill.createdAt || new Date().toISOString(),
        updatedAt: skill.updatedAt || new Date().toISOString()
      }));

      return {
        items,
        nextToken: result.nextToken
      };
    } catch (error) {
      return handleApiError('listSkills', error);
    }
  }
};