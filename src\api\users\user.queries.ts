import { gql } from '@apollo/client';

export const GET_USER = gql`
  query GetUser($id: ID!) {
    getUser(id: $id) {
      id
      name
      email
      role
      profilePhoto
      bio
      skills
      createdAt
      updatedAt
    }
  }
`;

export const LIST_USERS = gql`
  query ListUsers(
    $filter: ModelUserFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listUsers(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        name
        email
        role
        profilePhoto
        bio
        skills
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;

export const LIST_USER_PROFILES = gql`
  query ListUserProfiles(
    $filter: ModelUserProfileFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listUserProfiles(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        name
        email
        role
        profilePhoto
        bio
        skills
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;
