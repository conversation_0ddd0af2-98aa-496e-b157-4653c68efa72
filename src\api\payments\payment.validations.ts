import {
  PaymentMethod,
  PaymentStatus,
  CreatePaymentDto,
  UpdatePaymentDto,
  PaymentValidationResult
} from '@/types/features/payments/payment.types';

/**
 * Validation functions for payment operations
 */

export const validateCreatePayment = (data: CreatePaymentDto): PaymentValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!data.contractId || !data.contractId.trim()) {
    errors.push('Contract ID is required');
  }

  if (typeof data.amount !== 'number' || data.amount <= 0) {
    errors.push('Amount must be a positive number');
  } else if (data.amount < 0.50) {
    errors.push('Minimum payment amount is $0.50');
  } else if (data.amount > 999999.99) {
    errors.push('Maximum payment amount is $999,999.99');
  } else if (data.amount > 10000) {
    warnings.push('Large payment amount detected. Please verify the amount is correct.');
  }

  if (data.currency) {
    const validCurrencies = ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY'];
    if (!validCurrencies.includes(data.currency.toUpperCase())) {
      errors.push('Invalid currency code');
    }
  }

  if (!data.method || !Object.values(PaymentMethod).includes(data.method)) {
    errors.push('Valid payment method is required');
  }

  if (data.description && data.description.length > 500) {
    errors.push('Description cannot exceed 500 characters');
  }

  if (data.metadata) {
    if (typeof data.metadata !== 'object') {
      errors.push('Metadata must be an object');
    } else {
      const metadataSize = JSON.stringify(data.metadata).length;
      if (metadataSize > 5000) {
        errors.push('Metadata size cannot exceed 5KB');
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

export const validateUpdatePayment = (data: UpdatePaymentDto): PaymentValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (data.status && !Object.values(PaymentStatus).includes(data.status)) {
    errors.push('Invalid payment status');
  }

  if (data.transactionId && typeof data.transactionId !== 'string') {
    errors.push('Transaction ID must be a string');
  }

  if (data.paidAt) {
    const paidAtDate = new Date(data.paidAt);
    if (isNaN(paidAtDate.getTime())) {
      errors.push('Invalid paid at date format');
    } else {
      const now = new Date();
      if (paidAtDate > now) {
        errors.push('Paid at date cannot be in the future');
      }
    }
  }

  if (data.metadata) {
    if (typeof data.metadata !== 'object') {
      errors.push('Metadata must be an object');
    } else {
      const metadataSize = JSON.stringify(data.metadata).length;
      if (metadataSize > 5000) {
        errors.push('Metadata size cannot exceed 5KB');
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

export const validatePaymentAmount = (amount: number, currency: string = 'USD'): PaymentValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (typeof amount !== 'number' || amount <= 0) {
    errors.push('Amount must be a positive number');
    return { isValid: false, errors };
  }

  const minimums: Record<string, number> = {
    USD: 0.50,
    EUR: 0.50,
    GBP: 0.30,
    JPY: 50,
    CAD: 0.50,
    AUD: 0.50,
  };

  const minAmount = minimums[currency.toUpperCase()] || 0.50;
  if (amount < minAmount) {
    errors.push(`Minimum payment amount for ${currency} is ${minAmount}`);
  }

  const maxAmount = 999999.99;
  if (amount > maxAmount) {
    errors.push(`Maximum payment amount is ${maxAmount}`);
  }

  if (amount > 10000) {
    warnings.push('Large payment amount detected. Please verify the amount is correct.');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

export const validateRefundAmount = (
  refundAmount: number,
  originalAmount: number,
  currency: string = 'USD'
): PaymentValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (typeof refundAmount !== 'number' || refundAmount <= 0) {
    errors.push('Refund amount must be a positive number');
    return { isValid: false, errors };
  }

  if (refundAmount > originalAmount) {
    errors.push('Refund amount cannot exceed the original payment amount');
  }

  const minimums: Record<string, number> = {
    USD: 0.50,
    EUR: 0.50,
    GBP: 0.30,
    JPY: 50,
    CAD: 0.50,
    AUD: 0.50,
  };

  const minAmount = minimums[currency.toUpperCase()] || 0.50;
  if (refundAmount < minAmount) {
    errors.push(`Minimum refund amount for ${currency} is ${minAmount}`);
  }

  if (refundAmount < originalAmount) {
    warnings.push('This is a partial refund. The remaining amount will not be refunded.');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

export const validatePaymentIntent = (paymentIntentData: any): PaymentValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!paymentIntentData) {
    errors.push('Payment intent data is required');
    return { isValid: false, errors };
  }

  if (!paymentIntentData.amount) {
    errors.push('Amount is required');
  }

  if (!paymentIntentData.currency) {
    errors.push('Currency is required');
  }

  if (!paymentIntentData.contractId) {
    errors.push('Contract ID is required');
  }

  if (!paymentIntentData.clientId) {
    errors.push('Client ID is required');
  }

  if (paymentIntentData.amount) {
    const amountValidation = validatePaymentAmount(paymentIntentData.amount, paymentIntentData.currency);
    errors.push(...amountValidation.errors);
    warnings.push(...(amountValidation.warnings || []));
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

export const validatePaymentStatus = (
  currentStatus: PaymentStatus,
  newStatus: PaymentStatus
): PaymentValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  const validTransitions: Record<PaymentStatus, PaymentStatus[]> = {
    [PaymentStatus.PENDING]: [PaymentStatus.PROCESSING, PaymentStatus.FAILED, PaymentStatus.CANCELLED],
    [PaymentStatus.PROCESSING]: [PaymentStatus.PAID, PaymentStatus.COMPLETED, PaymentStatus.FAILED],
    [PaymentStatus.PAID]: [PaymentStatus.COMPLETED, PaymentStatus.REFUNDED],
    [PaymentStatus.COMPLETED]: [PaymentStatus.REFUNDED],
    [PaymentStatus.FAILED]: [PaymentStatus.PENDING],
    [PaymentStatus.REFUNDED]: [],
    [PaymentStatus.CANCELLED]: [],
  };

  if (!validTransitions[currentStatus].includes(newStatus)) {
    errors.push(`Invalid status transition from ${currentStatus} to ${newStatus}`);
  }

  if (currentStatus === PaymentStatus.COMPLETED && newStatus === PaymentStatus.REFUNDED) {
    warnings.push('This will refund a completed payment. Make sure this is intended.');
  }

  if (currentStatus === PaymentStatus.FAILED && newStatus === PaymentStatus.PENDING) {
    warnings.push('Retrying a failed payment. Consider investigating the original failure cause.');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};