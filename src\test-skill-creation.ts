import { skillService } from '@/api/skills/skill.service';

export async function testSkillCreation() {
  const skillData = {
    name: "Angular",
    jobCategoryId: "web-development-category-id",
    description: "Modern web framework by Google",
    isActive: true
  };

  try {
    const result = await skillService.createSkill(skillData);
    console.log('Skill created successfully:', result);
  } catch (error) {
    console.error('Error creating skill:', error);
  }
}

// The GraphQL mutation should now work with this structure:
/*
{
  "query": "mutation CreateSkill($input: CreateSkillInput!) {\n  createSkill(input: $input) {\n    id\n    name\n    description\n    isActive\n    jobCategoryId\n    createdAt\n    updatedAt\n  }\n}\n",
  "variables": {
    "input": {
      "name": "Angular",
      "jobCategoryId": "your-actual-job-category-id-here",
      "description": "",
      "isActive": true
    }
  }
}
*/