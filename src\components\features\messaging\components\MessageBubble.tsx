'use client';

import { MessagingUser } from '../types';
import { UIMessage as Message } from '@/types/features/messaging/messaging.types';
import { cn } from '@/lib/utils';
import Image from 'next/image';
import { formatDistanceToNow } from 'date-fns';

export interface MessageBubbleProps {
  message: Message;
  isCurrentUser: boolean;
  showAvatar: boolean;
  showTimestamp: boolean;
  user: MessagingUser;
}

export function MessageBubble({
  message,
  isCurrentUser,
  showAvatar,
  showTimestamp,
  user,
}: MessageBubbleProps) {
  const messageAlignment = isCurrentUser ? 'end' : 'start';
  const bubbleColors = isCurrentUser
    ? 'bg-primary text-primary-foreground'
    : 'bg-muted text-foreground';

  return (
    <div
      className={cn('flex w-full mb-4', {
        'justify-end': isCurrentUser,
        'justify-start': !isCurrentUser,
      })}
    >
      {!isCurrentUser && showAvatar && (
        <div className="flex-shrink-0 mr-3">
          <div className="relative">
            <div className="w-10 h-10 rounded-full bg-muted-foreground/10 flex items-center justify-center overflow-hidden">
              {user.avatar && !user.avatar.endsWith('default-avatar.png') ? (
                <Image
                  src={user.avatar}
                  alt={user.name || 'User avatar'}
                  width={40}
                  height={40}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const fallback = target.nextElementSibling as HTMLElement;
                    if (fallback) fallback.style.display = 'flex';
                  }}
                />
              ) : null}
              <div 
                className={cn(
                  'w-full h-full flex items-center justify-center',
                  'text-lg font-medium text-muted-foreground',
                  user.avatar && !user.avatar.endsWith('default-avatar.png') ? 'hidden' : ''
                )}
                style={{
                  display: user.avatar && !user.avatar.endsWith('default-avatar.png') ? 'none' : 'flex'
                }}
              >
                {user.name?.charAt(0).toUpperCase() || '?'}
              </div>
            </div>
            {user.isOnline && (
              <span className="absolute bottom-0 right-0 w-3 h-3 rounded-full bg-green-500 border-2 border-background"></span>
            )}
          </div>
        </div>
      )}

      <div className={`max-w-[80%] md:max-w-[70%] lg:max-w-[60%] flex flex-col ${messageAlignment}`}>
        <div
          className={cn(
            'px-4 py-2 rounded-2xl',
            bubbleColors,
            {
              'rounded-tr-none': !isCurrentUser,
              'rounded-tl-none': isCurrentUser,
            }
          )}
        >
          {message.type === 'text' || !message.type ? (
            <p className="whitespace-pre-wrap break-words">{message.content}</p>
          ) : message.type === 'file' || message.type === 'image' ? (
            <div className="flex items-center">
              <span className="mr-2">📎</span>
              <a
                href={message.fileInfo?.url || '#'}
                target="_blank"
                rel="noopener noreferrer"
                className="hover:underline"
              >
                {message.fileInfo?.name || 'File'}
              </a>
            </div>
          ) : (
            <p className="text-sm italic">{message.content}</p>
          )}
        </div>

        {showTimestamp && (
          <div className="mt-1 flex items-center text-xs text-muted-foreground">
            <span className="mr-2">
              {formatDistanceToNow(new Date(message.createdAt), { addSuffix: true })}
            </span>
            {isCurrentUser && (
              <span className="text-xs">
                {message.status === 'read' ? '✓✓' : message.status === 'delivered' ? '✓' : ''}
              </span>
            )}
          </div>
        )}
      </div>

      {isCurrentUser && (
        <div className="flex-shrink-0 ml-3">
          <div className="w-10 h-10 rounded-full bg-muted-foreground/10 flex items-center justify-center overflow-hidden">
            {user.avatar ? (
              <Image
                src={user.avatar}
                alt={user.name}
                width={40}
                height={40}
                className="w-full h-full object-cover"
              />
            ) : (
              <span className="text-lg font-medium text-muted-foreground">
                {user.name.charAt(0).toUpperCase()}
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
