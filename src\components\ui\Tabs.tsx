'use client';

import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

export interface TabItem {
  id: string;
  label: string;
  content: React.ReactNode;
  disabled?: boolean;
  badge?: string | number;
}

export interface TabsProps {
  items: TabItem[];
  defaultTab?: string;
  onChange?: (tabId: string) => void;
  className?: string;
  variant?: 'default' | 'pills' | 'underline';
}

const Tabs: React.FC<TabsProps> = ({
  items,
  defaultTab,
  onChange,
  className,
  variant = 'default',
}) => {
  const [activeTab, setActiveTab] = useState(defaultTab || items[0]?.id);

  useEffect(() => {
    if (defaultTab && defaultTab !== activeTab) {
      setActiveTab(defaultTab);
    }
  }, [defaultTab, activeTab]);

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    onChange?.(tabId);
  };

  const handleKeyDown = (event: React.KeyboardEvent, tabId: string) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleTabChange(tabId);
    } else if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {
      event.preventDefault();
      const currentIndex = items.findIndex((item) => item.id === activeTab);
      const direction = event.key === 'ArrowLeft' ? -1 : 1;
      const nextIndex = (currentIndex + direction + items.length) % items.length;
      const nextTab = items[nextIndex];
      if (!nextTab.disabled) {
        handleTabChange(nextTab.id);
      }
    }
  };

  const getTabClasses = (item: TabItem, isActive: boolean) => {
    const baseClasses = 'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 cursor-pointer';
    
    switch (variant) {
      case 'pills':
        return cn(
          baseClasses,
          isActive
            ? 'bg-primary text-primary-foreground shadow-sm'
            : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground',
          item.disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-accent/50 transition-colors'
        );
      case 'underline':
        return cn(
          'inline-flex items-center justify-center whitespace-nowrap border-b-2 px-1 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 cursor-pointer',
          isActive
            ? 'border-primary text-foreground'
            : 'border-transparent text-muted-foreground hover:text-foreground',
          item.disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-accent/50 transition-colors'
        );
      default:
        return cn(
          baseClasses,
          isActive
            ? 'bg-background text-foreground shadow-sm'
            : 'text-muted-foreground hover:text-foreground',
          item.disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-accent/20 transition-colors',
          'cursor-pointer'
        );
    }
  };

  const activeItem = items.find((item) => item.id === activeTab);

  return (
    <div className={cn('w-full', className)}>
      <div
        className={cn(
          'inline-flex items-center justify-center rounded-md p-1',
          variant === 'default' && 'bg-muted text-muted-foreground',
          variant === 'underline' && 'border-b'
        )}
        role="tablist"
      >
        {items.map((item) => {
          const isActive = item.id === activeTab;
          return (
            <button
              key={item.id}
              role="tab"
              aria-selected={isActive}
              aria-controls={`tabpanel-${item.id}`}
              tabIndex={isActive ? 0 : -1}
              disabled={item.disabled}
              className={getTabClasses(item, isActive)}
              onClick={() => !item.disabled && handleTabChange(item.id)}
              onKeyDown={(e) => !item.disabled && handleKeyDown(e, item.id)}
            >
              {item.label}
              {item.badge && (
                <span className="ml-2 inline-flex items-center justify-center rounded-full bg-primary/10 px-2 py-0.5 text-xs font-medium text-primary">
                  {item.badge}
                </span>
              )}
            </button>
          );
        })}
      </div>
      
      <div className="mt-4">
        {activeItem && (
          <div
            role="tabpanel"
            id={`tabpanel-${activeItem.id}`}
            aria-labelledby={`tab-${activeItem.id}`}
          >
            {activeItem.content}
          </div>
        )}
      </div>
    </div>
  );
};

export { Tabs };
