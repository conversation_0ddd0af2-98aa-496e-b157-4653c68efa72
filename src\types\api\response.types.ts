import { ErrorCode } from '@/utils/errorHandler';

/**
 * Standard API response format for successful responses
 */
export interface ApiSuccessResponse<T = unknown> {
  success: true;
  data: T;
  meta?: {
    page?: number;
    limit?: number;
    total?: number;
    [key: string]: unknown;
  };
}

/**
 * Standard API response format for error responses
 */
export interface ApiErrorResponse {
  success: false;
  error: {
    /**
     * Machine-readable error code
     * @example 'VALIDATION_ERROR'
     */
    code: ErrorCode | string;
    
    /**
     * Human-readable error message
     * @example 'Invalid input data'
     */
    message: string;
    
    /**
     * HTTP status code
     * @example 400
     */
    statusCode: number;
    
    /**
     * Additional error details
     * For validation errors, this might contain field-specific errors
     */
    details?: Record<string, unknown>;
    
    /**
     * Stack trace (only included in development)
     */
    stack?: string;
  };
}

/**
 * Type representing a standard API response that can be either success or error
 */
export type ApiResponse<T = unknown> = ApiSuccessResponse<T> | ApiErrorResponse;

/**
 * Type guard to check if a response is an error response
 */
export function isErrorResponse(
  response: ApiResponse<unknown>
): response is ApiErrorResponse {
  return !response.success;
}

/**
 * Creates a success response with the given data
 */
export function createSuccessResponse<T = unknown>(
  data: T,
  meta?: ApiSuccessResponse['meta']
): ApiSuccessResponse<T> {
  return {
    success: true,
    data,
    ...(meta && { meta })
  };
}

/**
 * Creates an error response with the given error details
 */
export function createErrorResponse(
  code: string,
  message: string,
  statusCode: number,
  details?: Record<string, unknown>,
  includeStack: boolean = process.env.NODE_ENV === 'development'
): ApiErrorResponse {
  return {
    success: false,
    error: {
      code,
      message,
      statusCode,
      ...(details && { details }),
      ...(includeStack && { stack: new Error().stack })
    }
  };
}

/**
 * Creates a validation error response
 */
export function createValidationErrorResponse(
  message: string = 'Validation failed',
  errors: Record<string, string[]>,
  statusCode: number = 400
): ApiErrorResponse {
  return createErrorResponse(
    'VALIDATION_ERROR',
    message,
    statusCode,
    { errors }
  );
}

/**
 * Creates an unauthorized error response
 */
export function createUnauthorizedErrorResponse(
  message: string = 'Authentication required'
): ApiErrorResponse {
  return createErrorResponse(
    'UNAUTHORIZED',
    message,
    401
  );
}

/**
 * Creates a forbidden error response
 */
export function createForbiddenErrorResponse(
  message: string = 'Insufficient permissions'
): ApiErrorResponse {
  return createErrorResponse(
    'FORBIDDEN',
    message,
    403
  );
}

/**
 * Creates a not found error response
 */
export function createNotFoundErrorResponse(
  resource: string,
  id?: string | number
): ApiErrorResponse {
  const message = id 
    ? `${resource} with ID ${id} not found`
    : `${resource} not found`;
    
  return createErrorResponse(
    'NOT_FOUND',
    message,
    404
  );
}

/**
 * Creates an internal server error response
 */
export function createInternalServerErrorResponse(
  error?: Error
): ApiErrorResponse {
  return createErrorResponse(
    'INTERNAL_SERVER_ERROR',
    'An internal server error occurred',
    500,
    error ? { originalError: error.message } : undefined,
    true
  );
}
