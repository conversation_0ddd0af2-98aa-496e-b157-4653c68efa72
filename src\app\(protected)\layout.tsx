"use client";

import { useAuth } from "@/lib/auth/AuthContext";
import { useEffect, useState } from "react";
import { Loading, LoadingOverlay } from "@/components/ui";
import { MessagingProvider } from "@/contexts/MessagingContext";
import { MessagingUser } from "@/components/features/messaging/types";
import messageService from "@/api/messaging/message.service";

export default function ProtectedLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { loading, user, isInitialized, isLoggingOut, cognitoUserId } =
    useAuth();

  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted || loading || !isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Loading size="lg" />
      </div>
    );
  }

  const currentUser: MessagingUser = {
    id: cognitoUserId || user?.username || "",
    name: user?.attributes?.name || "User",
    email: user?.attributes?.email || "",
    avatar: user?.attributes?.profilePhoto || "",
    role: (user?.attributes?.["custom:role"] === "FREELANCER"
      ? "FREELANCER"
      : "CLIENT") as "CLIENT" | "FREELANCER",
    isOnline: true,
  };

  const handleSendMessage = async (
    conversationId: string,
    content: string
  ): Promise<void> => {
    if (!conversationId || !content.trim() || !currentUser.id) return;

    try {
      await messageService.sendMessage(
        conversationId,
        currentUser.id,
        currentUser.role === "FREELANCER" ? "CLIENT" : "FREELANCER",
        content
      );
    } catch (error) {
      console.error("Failed to send message:", error);
      throw error;
    }
  };

  const handleLoadMoreMessages = async (
    conversationId: string,
    before: Date
  ): Promise<any[]> => {
    if (!conversationId || !before) return [];

    return new Promise<any[]>((resolve) => {
      const messages: any[] = [];
      
      const unsubscribe = messageService.subscribeToMessages(
        conversationId,
        (message) => {
          messages.push({
            id: message.id,
            content: message.content,
            senderId: message.senderId,
            receiverId: message.receiverId,
            conversationId: message.conversationId,
            createdAt: message.createdAt,
            updatedAt: message.updatedAt || message.createdAt,
            status: message.status || "delivered",
            type: message.type || "text",
            sender: message.sender,
            receiver: message.receiver
          });
        },
        (error) => {
          console.error("Error loading more messages:", error);
          resolve([]);
        },
        cognitoUserId || user?.username
      );

      setTimeout(() => {
        unsubscribe.unsubscribe();
        resolve(messages);
      }, 1000);
    });
  };

  const handleFileUpload = async (file: File) => {
    return URL.createObjectURL(file);
  };

  return (
    <MessagingProvider
      currentUser={currentUser}
      initialConversations={[]}
      onSendMessage={handleSendMessage}
      onLoadMoreMessages={handleLoadMoreMessages}
      onFileUpload={handleFileUpload}
    >
      {children}

      {/* Global Logout Loading Overlay */}
      {isLoggingOut && (
        <LoadingOverlay
          isLoading={isLoggingOut}
          className="fixed inset-0 z-[9999] bg-background/80 backdrop-blur-sm"
          loadingText="Signing out..."
        >
          {children}
        </LoadingOverlay>
      )}
    </MessagingProvider>
  );
}
