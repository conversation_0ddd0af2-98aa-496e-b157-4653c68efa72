"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";

import { useAuth } from "@/lib/auth/AuthContext";
import { skillService } from "@/api/skills/skill.service";
import { jobCategoryService } from "@/api/job-categories/job-category.service";
import { Skill } from "@/types/features/skills/skill.types";
import { JobCategory } from "@/types/features/job-categories/job-category.types";
import { Icon } from "@/components/ui/Icon";
import { ContentHeader } from "@/components/layout/ContentHeader";
import { SkillSummaryCard, SkillDetailsInfoCard } from "@/components/layout";
import { Button } from "@/components/ui/Button";
import { ConfirmDialog } from "@/components/ui/ConfirmDialog";
import useToaster from "@/hooks/useToaster";

export default function ViewSkillPage() {
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const router = useRouter();
  const params = useParams();
  const { showSuccess, showError } = useToaster();
  const [isLoading, setIsLoading] = useState(true);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [skill, setSkill] = useState<Skill | null>(null);
  const [jobCategories, setJobCategories] = useState<JobCategory[]>([]);

  useEffect(() => {
    const loadData = async () => {
      if (!isAuthenticated || !params.id) return;

      try {
        setIsLoading(true);

        const [skillData, categoriesResponse] = await Promise.all([
          skillService.getSkill(params.id as string),
          jobCategoryService.listJobCategories(undefined, 100),
        ]);

        setSkill(skillData);
        setJobCategories(categoriesResponse.items || []);
      } catch (err) {
        console.error("Error loading skill:", err);
        showSuccess("Failed to load skill data. Please try again.");
        router.push("/admin/skills");
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [isAuthenticated, params.id, router, showSuccess]);

  const handleEdit = () => {
    router.push(`/admin/skills/${params.id}/edit`);
  };

  const handleDelete = () => {
    setShowDeleteDialog(true);
  };

  const handleConfirmDelete = async () => {
    if (!skill) return;

    try {
      setDeleteLoading(true);
      await skillService.deleteSkill(skill.id);

      showSuccess("Skill deleted successfully");

      router.push("/admin/skills");
    } catch (err) {
      console.error("Error deleting skill:", err);
      showError("Failed to delete skill. Please try again.");
    } finally {
      setDeleteLoading(false);
      setShowDeleteDialog(false);
    }
  };

  const getCategoryName = (jobCategoryId: string) => {
    const category = jobCategories.find((cat) => cat.id === jobCategoryId);
    return category ? category.name : "Unknown Category";
  };

  const handleCancelDelete = () => {
    setShowDeleteDialog(false);
  };

  if (
    !authLoading &&
    (!isAuthenticated || user?.attributes?.["custom:role"] !== "ADMIN")
  ) {
    router.push("/login");
    return null;
  }

  if (authLoading || !isAuthenticated || isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Icon name="Loader2" size="xl" className="animate-spin text-blue-500" />
      </div>
    );
  }

  if (!skill) {
    return (
      <div className="w-full max-w-6xl mx-auto p-4 sm:p-6">
        <div className="text-center py-8">
          <Icon
            name="AlertCircle"
            size="xl"
            className="mx-auto text-red-500 mb-4"
          />
          <h2 className="text-lg font-semibold text-gray-900 mb-2">
            Skill Not Found
          </h2>
          <p className="text-gray-600 mb-4">
            The skill you&apos;re looking for doesn&apos;t exist.
          </p>
          <Button onClick={() => router.push("/admin/skills")}>
            Back to Skills
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-4 sm:p-6">
      <ContentHeader
        title={skill.name}
        subtitle="View skill details and manage settings"
        breadcrumbs={[
          { label: "Dashboard", href: "/admin/dashboard" },
          { label: "Skills", href: "/admin/skills" },
          { label: skill.name, current: true },
        ]}
        showBackButton={true}
      />

      <div className="space-y-6">
        <SkillSummaryCard
          skill={skill}
          categoryName={getCategoryName(skill.jobCategoryId)}
          onEdit={handleEdit}
          onDelete={handleDelete}
          deleteLoading={deleteLoading}
          loading={isLoading}
        />
        <SkillDetailsInfoCard
          skill={skill}
          categoryName={getCategoryName(skill.jobCategoryId)}
          loading={isLoading}
        />
      </div>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={showDeleteDialog}
        title="Delete Skill"
        message={`Are you sure you want to delete "${skill.name}"? This action cannot be undone and will permanently delete the skill and all associated data.`}
        confirmText={deleteLoading ? "Deleting..." : "Delete"}
        cancelText="Cancel"
        confirmVariant="destructive"
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        isLoading={deleteLoading}
      />
    </div>
  );
}
