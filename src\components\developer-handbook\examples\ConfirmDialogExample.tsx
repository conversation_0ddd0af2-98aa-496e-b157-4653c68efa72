import React, { useState } from "react";
import { Button } from "@/components/ui/Button";
import { ConfirmDialog } from "@/components/ui/ConfirmDialog";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import { Container } from "@/components/layout";

export const ConfirmDialogExample: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<string>("");
  const [variant, setVariant] = React.useState<
    "default" | "destructive" | "outline"
  >("default");

  const handleConfirm = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      setIsOpen(false);
      setResult("Confirmed!");
    }, 1500);
  };

  const handleCancel = () => {
    setIsOpen(false);
    setResult("Cancelled!");
  };

  const openDialog = (
    type: "default" | "destructive" | "outline" = "default"
  ) => {
    setVariant(type);
    setResult("");
    setIsOpen(true);
  };

  return (
    <Container className="py-8 space-y-8">
      <div>
        <h1 className="text-3xl font-bold mb-2">Confirm Dialog</h1>
        <p className="text-muted-foreground">
          A customizable dialog for confirming user actions.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Examples</CardTitle>
          <CardDescription>
            Different styles and variants of the confirm dialog.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Variants</h3>
            <div className="flex flex-wrap gap-4">
              <Button onClick={() => openDialog("default")}>
                Default Dialog
              </Button>
              <Button
                variant="destructive"
                onClick={() => openDialog("destructive")}
              >
                Destructive Action
              </Button>
              <Button variant="outline" onClick={() => openDialog("outline")}>
                Outline Variant
              </Button>
            </div>
          </div>

          {result && (
            <div className="p-4 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 rounded-md">
              Action: {result}
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Usage</CardTitle>
          <CardDescription>
            How to use the ConfirmDialog component.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <pre className="bg-muted p-4 rounded-md overflow-x-auto text-sm">
            {`import { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { ConfirmDialog } from '@/components/ui/ConfirmDialog';

export function MyComponent() {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleConfirm = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      setIsOpen(false);
    }, 1000);
  };

  return (
    <>
      <Button onClick={() => setIsOpen(true)}>Delete Item</Button>
      
      <ConfirmDialog
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onConfirm={handleConfirm}
        title="Confirm Action"
        description="Are you sure you want to perform this action?"
        confirmText="Confirm"
        cancelText="Cancel"
        isLoading={isLoading}
        variant="default"
      />
    </>
  );
}`}
          </pre>
        </CardContent>
      </Card>

      <ConfirmDialog
        open={isOpen}
        onCancel={handleCancel}
        onConfirm={handleConfirm}
        title={variant === "destructive" ? "Delete Item" : "Confirm Action"}
        message={
          variant === "destructive"
            ? "Are you sure you want to delete this item? This action cannot be undone."
            : "Are you sure you want to perform this action?"
        }
        confirmText={variant === "destructive" ? "Delete" : "Confirm"}
        cancelText="Cancel"
        isLoading={isLoading}
        confirmVariant={variant}
      />
    </Container>
  );
};

export default ConfirmDialogExample;
