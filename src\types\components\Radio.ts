import { InputHTMLAttributes } from 'react';

export interface RadioOption {
  /** Value of the radio option */
  value: string;
  /** Label text for the radio option */
  label: string;
  /** Optional description for the radio option */
  description?: string;
  /** Whether the radio option is disabled */
  disabled?: boolean;
}

export interface RadioGroupProps {
  /** Name attribute for the radio group */
  name: string;
  /** Array of radio options */
  options: RadioOption[];
  /** Currently selected value */
  value?: string;
  /** Callback when selection changes */
  onChange?: (value: string) => void;
  /** Additional CSS classes */
  className?: string;
  /** Layout orientation */
  orientation?: 'horizontal' | 'vertical';
  /** Whether the radio group has an error */
  error?: boolean;
}

export interface RadioProps extends InputHTMLAttributes<HTMLInputElement> {
  /** Label text */
  label?: string;
  /** Optional description */
  description?: string;
  /** Whether the radio has an error */
  error?: boolean;
}
