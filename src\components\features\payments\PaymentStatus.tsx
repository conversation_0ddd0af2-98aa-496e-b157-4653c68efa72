'use client';

import React from 'react';
import { Badge } from '@/components/ui/Badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Icon } from '@/components/ui/Icon';
import { PaymentStatus as PaymentStatusEnum } from '@/types/features/payments/payment.types';
import { paymentService } from '@/api/payments';

interface PaymentStatusProps {
  status: PaymentStatusEnum;
  amount: number;
  currency: string;
  transactionId?: string;
  paidAt?: string;
  errorMessage?: string;
  onRetry?: () => void;
  onViewDetails?: () => void;
  onDownloadReceipt?: () => void;
  className?: string;
  showActions?: boolean;
}

const getStatusConfig = (status: PaymentStatusEnum) => {
  switch (status) {
    case PaymentStatusEnum.PAID:
    case PaymentStatusEnum.COMPLETED:
      return {
        iconName: 'CheckCircle' as const,
        label: 'Paid',
        color: 'bg-green-100 text-green-800 border-green-200',
        badgeVariant: 'default' as const,
        message: 'Payment completed successfully',
        alertVariant: 'default' as const,
      };

    case PaymentStatusEnum.PENDING:
      return {
        iconName: 'Clock' as const,
        label: 'Pending',
        color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        badgeVariant: 'secondary' as const,
        message: 'Payment is being processed',
        alertVariant: 'default' as const,
      };

    case PaymentStatusEnum.PROCESSING:
      return {
        iconName: 'RefreshCw' as const,
        label: 'Processing',
        color: 'bg-blue-100 text-blue-800 border-blue-200',
        badgeVariant: 'secondary' as const,
        message: 'Payment is currently being processed',
        alertVariant: 'default' as const,
      };

    case PaymentStatusEnum.FAILED:
      return {
        iconName: 'XCircle' as const,
        label: 'Failed',
        color: 'bg-red-100 text-red-800 border-red-200',
        badgeVariant: 'destructive' as const,
        message: 'Payment failed to process',
        alertVariant: 'destructive' as const,
      };

    case PaymentStatusEnum.CANCELLED:
      return {
        iconName: 'XCircle' as const,
        label: 'Cancelled',
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        badgeVariant: 'outline' as const,
        message: 'Payment was cancelled',
        alertVariant: 'default' as const,
      };

    case PaymentStatusEnum.REFUNDED:
      return {
        iconName: 'AlertTriangle' as const,
        label: 'Refunded',
        color: 'bg-orange-100 text-orange-800 border-orange-200',
        badgeVariant: 'secondary' as const,
        message: 'Payment has been refunded',
        alertVariant: 'default' as const,
      };

    default:
      return {
        iconName: 'AlertTriangle' as const,
        label: 'Unknown',
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        badgeVariant: 'outline' as const,
        message: 'Payment status unknown',
        alertVariant: 'default' as const,
      };
  }
};

export const PaymentStatus: React.FC<PaymentStatusProps> = ({
  status,
  amount,
  currency,
  transactionId,
  paidAt,
  errorMessage,
  onRetry,
  onViewDetails,
  onDownloadReceipt,
  className = '',
  showActions = true,
}) => {
  const config = getStatusConfig(status);
  const formattedAmount = paymentService.formatAmount(amount * 100, currency);

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return dateString;
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Icon name="CreditCard" className="h-5 w-5" />
            Payment Status
          </div>
          <Badge variant={config.badgeVariant} className={`flex items-center gap-1 ${config.color}`}>
            <Icon name={config.iconName} className="h-3 w-3" />
            {config.label}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Status Alert */}
        <div className="rounded-lg border p-4">
          <div className="flex items-start gap-3">
            <Icon name={config.iconName} className="h-4 w-4 mt-0.5" />
            <div>
              <p className="text-sm">{config.message}</p>
              {errorMessage && status === PaymentStatusEnum.FAILED && (
                <p className="mt-1 text-sm text-red-600">
                  <strong>Error:</strong> {errorMessage}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Payment Details */}
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-600">Amount:</span>
            <span className="text-lg font-bold text-gray-900">{formattedAmount}</span>
          </div>

          {transactionId && (
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-600">Transaction ID:</span>
              <span className="text-sm font-mono text-gray-900 bg-gray-100 px-2 py-1 rounded">
                {transactionId}
              </span>
            </div>
          )}

          {paidAt && (
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-600">Paid At:</span>
              <span className="text-sm text-gray-900">{formatDate(paidAt)}</span>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        {showActions && (
          <div className="flex flex-wrap gap-2 pt-4 border-t">
            {status === PaymentStatusEnum.FAILED && onRetry && (
              <Button
                onClick={onRetry}
                size="sm"
                variant="outline"
                className="flex items-center gap-2"
              >
                <Icon name="RefreshCw" className="h-4 w-4" />
                Retry Payment
              </Button>
            )}

            {onViewDetails && (
              <Button
                onClick={onViewDetails}
                size="sm"
                variant="outline"
                className="flex items-center gap-2"
              >
                <Icon name="Eye" className="h-4 w-4" />
                View Details
              </Button>
            )}

            {(status === PaymentStatusEnum.PAID || status === PaymentStatusEnum.COMPLETED) && onDownloadReceipt && (
              <Button
                onClick={onDownloadReceipt}
                size="sm"
                variant="outline"
                className="flex items-center gap-2"
              >
                <Icon name="Download" className="h-4 w-4" />
                Download Receipt
              </Button>
            )}
          </div>
        )}

        {/* Processing Animation */}
        {status === PaymentStatusEnum.PROCESSING && (
          <div className="flex items-center justify-center py-4">
            <div className="flex items-center gap-2 text-blue-600">
              <Icon name="RefreshCw" className="h-5 w-5 animate-spin" />
              <span className="text-sm">Processing payment...</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default PaymentStatus;