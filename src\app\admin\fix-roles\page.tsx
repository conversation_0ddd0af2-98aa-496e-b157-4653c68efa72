'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { updateCurrentUserRole } from '@/utils/updateUserRole';
import type { UpdateRoleResult } from '@/types/features/auth/auth.types';
import { UserRole } from '@/types/enums';
import { useAuth } from '@/lib/auth/AuthContext';

export default function FixRolesPage() {
  const { user, refresh } = useAuth();
  const [result, setResult] = useState<UpdateRoleResult | null>(null);
  const [loading, setLoading] = useState(false);

  const handleUpdateRole = async (role: UserRole) => {
    setLoading(true);
    setResult(null);
    
    try {
      const updateResult = await updateCurrentUserRole(role);
      setResult(updateResult);
      
      if (updateResult.success) {
        setTimeout(async () => {
          try {
            await refresh();
          } catch (error) {
            console.error('Error refreshing user data:', error);
          }
        }, 1000);
      }
    } catch (error) {
      setResult({
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    } finally {
      setLoading(false);
    }
  };

  const currentRole = user?.attributes?.['custom:role'];

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle>Fix User Roles</CardTitle>
          <p className="text-sm text-gray-600">
            This utility helps fix missing or incorrect user roles in Cognito.
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Current User Info */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium mb-2">Current User</h3>
            <p><strong>Email:</strong> {user?.attributes?.email || 'Not available'}</p>
            <p><strong>Name:</strong> {user?.attributes?.name || 'Not available'}</p>
            <p><strong>Current Role:</strong> {currentRole || 'NOT SET'}</p>
          </div>

          {/* Role Update Buttons */}
          <div className="space-y-4">
            <h3 className="font-medium">Update Role</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <Button
                onClick={() => handleUpdateRole(UserRole.CLIENT)}
                disabled={loading}
                variant={currentRole === UserRole.CLIENT ? 'default' : 'outline'}
                className="w-full"
              >
                Set as CLIENT
              </Button>
              <Button
                onClick={() => handleUpdateRole(UserRole.FREELANCER)}
                disabled={loading}
                variant={currentRole === UserRole.FREELANCER ? 'default' : 'outline'}
                className="w-full"
              >
                Set as FREELANCER
              </Button>
              <Button
                onClick={() => handleUpdateRole(UserRole.ADMIN)}
                disabled={loading}
                variant={currentRole === UserRole.ADMIN ? 'default' : 'outline'}
                className="w-full"
              >
                Set as ADMIN
              </Button>
            </div>
          </div>

          {/* Result Display */}
          {result && (
            <div className={`p-4 rounded-lg ${
              result.success 
                ? 'bg-green-50 border border-green-200' 
                : 'bg-red-50 border border-red-200'
            }`}>
              <p className={`font-medium ${
                result.success ? 'text-green-800' : 'text-red-800'
              }`}>
                {result.success ? '✓ Success' : '✗ Error'}
              </p>
              <p className={`text-sm ${
                result.success ? 'text-green-700' : 'text-red-700'
              }`}>
                {result.message}
              </p>
            </div>
          )}

          {/* Instructions */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-medium text-blue-800 mb-2">Instructions</h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>1. This page allows you to update your own user role in Cognito</li>
              <li>2. Select the appropriate role for your account</li>
              <li>3. After updating, you may need to log out and log back in</li>
              <li>4. For <EMAIL>, select &#34;Set as FREELANCER&#34;</li>
            </ul>
          </div>

          {loading && (
            <div className="text-center">
              <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <p className="text-sm text-gray-600 mt-2">Updating role...</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
