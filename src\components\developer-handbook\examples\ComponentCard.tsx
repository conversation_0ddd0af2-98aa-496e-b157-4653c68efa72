import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/Card";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";

interface ComponentCardProps {
  title: string;
  description: string;
  code: string;
  children: React.ReactNode;
  usage?: string;
  props?: Array<{
    name: string;
    type: string;
    default: string;
    description: string;
  }>;
}

export const ComponentCard: React.FC<ComponentCardProps> = ({
  title,
  description,
  code,
  children,
  usage,
  props = [],
}) => {
  return (
    <Card className="mb-8">
      <CardHeader>
        <CardTitle className="text-2xl">{title}</CardTitle>
        <p className="text-muted-foreground">{description}</p>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="rounded-md border p-4">
          <div className="mb-4">
            <h4 className="text-sm font-medium mb-2">Preview</h4>
            <div className="p-4 bg-muted/20 rounded">{children}</div>
          </div>

          <div className="mt-6">
            <h4 className="text-sm font-medium mb-2">Usage</h4>
            <SyntaxHighlighter
              language="tsx"
              style={vscDarkPlus}
              className="rounded-md text-sm"
            >
              {usage || code}
            </SyntaxHighlighter>
          </div>

          {props.length > 0 && (
            <div className="mt-6">
              <h4 className="text-sm font-medium mb-2">Props</h4>
              <div className="overflow-x-auto">
                <table className="min-w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-2 px-4">Prop</th>
                      <th className="text-left py-2 px-4">Type</th>
                      <th className="text-left py-2 px-4">Default</th>
                      <th className="text-left py-2 px-4">Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    {props.map((prop) => (
                      <tr key={prop.name} className="border-b">
                        <td className="py-2 px-4 font-mono text-xs">
                          {prop.name}
                        </td>
                        <td className="py-2 px-4 font-mono text-xs">
                          {prop.type}
                        </td>
                        <td className="py-2 px-4 font-mono text-xs">
                          {prop.default}
                        </td>
                        <td className="py-2 px-4">{prop.description}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ComponentCard;
