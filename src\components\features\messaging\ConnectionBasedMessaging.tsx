import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Icon } from "@/components/ui/Icon";
import { useMessaging } from "@/contexts/MessagingContext";
import { useAuth } from "@/lib/auth/AuthContext";

interface ConnectionBasedMessagingProps {
  jobId: string;
  otherUserId: string;
  otherUserRole: "client" | "freelancer";
  onThreadCreated?: (conversationId: string) => void;
  buttonVariant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
  buttonText?: string;
  showAsCard?: boolean;
  className?: string;
}

export const ConnectionBasedMessaging: React.FC<
  ConnectionBasedMessagingProps
> = ({
  jobId,
  otherUserId,
  otherUserRole,
  onThreadCreated,
  buttonVariant = "outline",
  buttonText = "Message",
  showAsCard = false,
}) => {
  const { cognitoUserId } = useAuth();
  const {
    validateMessagingPermission,
    startThreadFromApplication,
    startThreadFromInvitation,
    isMessagingLoading,
    messagingError,
  } = useMessaging();

  const [isLoading, setIsLoading] = useState(false);
  const [canMessage, setCanMessage] = useState<boolean | null>(null);
  const [message, setMessage] = useState("");
  const [showMessageInput, setShowMessageInput] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);

  useEffect(() => {
    const checkPermissions = async () => {
      if (!cognitoUserId) {
        setCanMessage(false);
        return;
      }

      try {
        const { canMessage: canUserMessage, reason } =
          await validateMessagingPermission(otherUserId, jobId);

        setCanMessage(canUserMessage);
        if (!canUserMessage && reason) {
          setValidationError(reason);
        }
      } catch (err) {
        console.error("Error checking messaging permissions:", err);
        setCanMessage(false);
        setValidationError("Unable to verify messaging permissions");
      }
    };

    checkPermissions();
  }, [otherUserId, jobId, validateMessagingPermission, cognitoUserId]);

  const handleStartConversation = async () => {
    if (!cognitoUserId || !message.trim()) return;

    try {
      setIsLoading(true);

      let conversationId: string;

      if (otherUserRole === "client") {
        conversationId = await startThreadFromApplication(
          jobId,
          cognitoUserId,
          otherUserId,
          message.trim()
        );
      } else {
        conversationId = await startThreadFromInvitation(
          jobId,
          cognitoUserId,
          otherUserId,
          message.trim()
        );
      }

      setShowMessageInput(false);
      setMessage("");
      onThreadCreated?.(conversationId);
    } catch (err) {
      console.error("Error starting conversation:", err);
      setValidationError("Failed to start conversation. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  if (canMessage === null) {
    return (
      <div className="flex justify-center p-4">
        <Icon name="Loader2" className="h-6 w-6 animate-spin" />
      </div>
    );
  }

  if (!canMessage) {
    return (
      <div className="p-4">
        <p className="text-sm text-muted-foreground">
          {validationError || "Messaging is only available for connected users"}
        </p>
      </div>
    );
  }

  const content = (
    <div>
      {showMessageInput ? (
        <div className="mt-2">
          <textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Type your message..."
            rows={3}
            className="w-full p-2 border rounded-md mb-2 focus:outline-none focus:ring-2 focus:ring-primary"
          />
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowMessageInput(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={handleStartConversation}
              disabled={isLoading}
            >
              {isLoading || isMessagingLoading ? (
                <>
                  <Icon name="Loader2" className="mr-2 h-4 w-4 animate-spin" />
                  Sending...
                </>
              ) : (
                "Send Message"
              )}
            </Button>
          </div>
          {messagingError && (
            <p className="mt-2 text-sm text-destructive">
              {messagingError.message}
            </p>
          )}
        </div>
      ) : (
        <Button
          variant={buttonVariant}
          onClick={() => setShowMessageInput(true)}
          className={showAsCard ? "w-full" : ""}
        >
          {buttonText}
        </Button>
      )}
    </div>
  );

  if (showAsCard) {
    return (
      <Card className="border rounded-lg overflow-hidden bg-card">
        <CardContent className="p-4">
          <h3 className="text-lg font-semibold mb-4">Send a Message</h3>
          {content}
        </CardContent>
      </Card>
    );
  }

  return content;
};
