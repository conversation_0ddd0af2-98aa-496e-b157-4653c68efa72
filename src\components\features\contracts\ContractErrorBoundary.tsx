"use client";

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Icon } from '@/components/ui/Icon';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

export class ContractErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Contract workflow error:', error, errorInfo);
    this.setState({ errorInfo });
    this.props.onError?.(error, errorInfo);
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  handleGoHome = () => {
    window.location.href = '/contracts';
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-[400px] flex items-center justify-center p-4">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
                <Icon name="AlertTriangle" className="h-6 w-6 text-red-600" />
              </div>
              <CardTitle className="text-lg">Contract Workflow Error</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-4">
                  Something went wrong with the contract workflow. This could be due to:
                </p>
                <ul className="text-sm text-gray-500 text-left space-y-1 mb-4">
                  <li>• Network connectivity issues</li>
                  <li>• Server temporarily unavailable</li>
                  <li>• Invalid contract data</li>
                  <li>• Permission issues</li>
                </ul>
              </div>
              
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="text-xs bg-gray-50 p-2 rounded border">
                  <summary className="cursor-pointer font-medium">Error Details</summary>
                  <pre className="mt-2 whitespace-pre-wrap">
                    {this.state.error.message}
                    {this.state.errorInfo?.componentStack}
                  </pre>
                </details>
              )}
              
              <div className="flex gap-2 justify-center">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={this.handleRetry}
                  className="flex items-center gap-2"
                >
                  <Icon name="RefreshCw" className="h-4 w-4" />
                  Try Again
                </Button>
                <Button
                  size="sm"
                  onClick={this.handleGoHome}
                  className="flex items-center gap-2"
                >
                  <Icon name="Home" className="h-4 w-4" />
                  Go to Contracts
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export const useContractErrorHandler = () => {
  const handleError = (error: Error, context?: string) => {
    console.error(`Contract workflow error${context ? ` in ${context}` : ''}:`, error);
    
    return {
      message: getErrorMessage(error),
      isNetworkError: isNetworkError(error),
      isPermissionError: isPermissionError(error),
      isValidationError: isValidationError(error)
    };
  };

  return { handleError };
};

const getErrorMessage = (error: Error): string => {
  if (isNetworkError(error)) {
    return 'Network connection failed. Please check your internet connection and try again.';
  }
  
  if (isPermissionError(error)) {
    return 'You do not have permission to perform this action.';
  }
  
  if (isValidationError(error)) {
    return 'Invalid data provided. Please check your input and try again.';
  }
  
  if (error.message.includes('Contract not found')) {
    return 'The contract you are looking for could not be found.';
  }
  
  if (error.message.includes('Job not found')) {
    return 'The associated job could not be found.';
  }
  
  return 'An unexpected error occurred. Please try again or contact support if the problem persists.';
};

const isNetworkError = (error: Error): boolean => {
  return error.message.includes('fetch') || 
         error.message.includes('network') ||
         error.message.includes('NetworkError') ||
         error.name === 'NetworkError';
};

const isPermissionError = (error: Error): boolean => {
  return error.message.includes('403') ||
         error.message.includes('Forbidden') ||
         error.message.includes('Unauthorized') ||
         error.message.includes('permission');
};

const isValidationError = (error: Error): boolean => {
  return error.message.includes('validation') ||
         error.message.includes('invalid') ||
         error.message.includes('required') ||
         error.message.includes('400');
};

export const validateContractAction = (
  contractStatus: string,
  userRole: string,
  action: string
): { isValid: boolean; message?: string } => {
  const validations: Record<string, (status: string, role: string) => boolean> = {
    accept: (status, role) => status === 'PENDING_FREELANCER_ACCEPTANCE' && role === 'FREELANCER',
    reject: (status, role) => status === 'PENDING_FREELANCER_ACCEPTANCE' && role === 'FREELANCER',
    submitWork: (status, role) => (status === 'ACTIVE' || status === 'REVISIONS_REQUESTED') && role === 'FREELANCER',
    approveWork: (status, role) => status === 'WORK_SUBMITTED' && role === 'CLIENT',
    requestRevisions: (status, role) => status === 'WORK_SUBMITTED' && role === 'CLIENT',
    markPaid: (status, role) => status === 'COMPLETED' && role === 'CLIENT',
    cancel: (status, role) => ['ACTIVE', 'REVISIONS_REQUESTED'].includes(status) && ['CLIENT', 'FREELANCER'].includes(role)
  };

  const validator = validations[action];
  if (!validator) {
    return { isValid: false, message: 'Unknown action' };
  }

  const isValid = validator(contractStatus, userRole);
  if (!isValid) {
    return {
      isValid: false,
      message: `Cannot ${action} contract in ${contractStatus} status as ${userRole}`
    };
  }

  return { isValid: true };
};

export default ContractErrorBoundary;
