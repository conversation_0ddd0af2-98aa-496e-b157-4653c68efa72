import React from 'react';
import { Button } from '@/components/ui/Button';
import { ConfirmDialog } from '@/components/ui/ConfirmDialog';
import { ContractStatus } from '@/types/features/contracts/contract.types';
import { ContractStatusBadge } from './ContractStatusBadge';
import { Icon } from '@/components/ui/Icon';

interface ContractStatusUpdateProps {
  currentStatus: ContractStatus;
  onStatusUpdate: (status: ContractStatus) => Promise<void>;
  isUpdating?: boolean;
  isClient?: boolean;
  isFreelancer?: boolean;
  className?: string;
}

export const ContractStatusUpdate: React.FC<ContractStatusUpdateProps> = ({
  currentStatus,
  onStatusUpdate,
  isUpdating = false,
  className = '',
}) => {
  const [open, setOpen] = React.useState(false);
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [confirmDialog, setConfirmDialog] = React.useState<{
    open: boolean;
    status: ContractStatus | null;
    statusInfo: { label: string; description: string } | null;
  }>({
    open: false,
    status: null,
    statusInfo: null,
  });

  const getAvailableStatuses = () => {
    switch (currentStatus) {
      case 'DRAFT':
        return [
          { value: 'PENDING_FREELANCER_ACCEPTANCE', label: 'Send to Freelancer', description: 'Send contract to freelancer for acceptance.' },
          { value: 'CANCELLED', label: 'Cancel Contract', description: 'Cancel this contract before it begins.' },
        ];
      case 'PENDING_FREELANCER_ACCEPTANCE':
        return [
          { value: 'ACTIVE', label: 'Accept Contract', description: 'Accept this contract and begin work.' },
          { value: 'CANCELLED', label: 'Decline Contract', description: 'Decline this contract.' },
        ];
      case 'ACTIVE':
        return [
          { value: 'WORK_SUBMITTED', label: 'Submit Work', description: 'Submit completed work for review.' },
          { value: 'CANCELLED', label: 'Cancel Contract', description: 'Cancel this contract before completion.' },
        ];
      case 'WORK_SUBMITTED':
        return [
          { value: 'COMPLETED', label: 'Approve Work', description: 'Approve the submitted work.' },
          { value: 'REVISIONS_REQUESTED', label: 'Request Revisions', description: 'Request changes to the submitted work.' },
        ];
      case 'REVISIONS_REQUESTED':
        return [
          { value: 'WORK_SUBMITTED', label: 'Resubmit Work', description: 'Submit revised work for review.' },
          { value: 'CANCELLED', label: 'Cancel Contract', description: 'Cancel this contract.' },
        ];
      case 'COMPLETED':
        return [
          { value: 'PAID', label: 'Mark as Paid', description: 'Mark this contract as paid.' },
          { value: 'REVISIONS_REQUESTED', label: 'Request Additional Changes', description: 'Request additional changes to the work.' },
        ];
      case 'PAID':
        return [
          // No status changes available from PAID state
        ];
      case 'DISPUTED':
        return [
          { value: 'ACTIVE', label: 'Resolve Dispute', description: 'Resolve the dispute and reactivate the contract.' },
          { value: 'CANCELLED', label: 'Cancel Contract', description: 'Cancel this disputed contract.' },
        ];
      case 'CANCELLED':
        return [
          { value: 'DRAFT', label: 'Revert to Draft', description: 'Revert this cancelled contract to draft status.' },
        ];
      default:
        return [];
    }
  };

  const availableStatuses = getAvailableStatuses();

  const handleStatusSelect = (status: ContractStatus) => {
    const statusInfo = getAvailableStatuses().find(s => s.value === status);
    if (statusInfo) {
      setConfirmDialog({
        open: true,
        status,
        statusInfo: {
          label: statusInfo.label,
          description: statusInfo.description,
        },
      });
    }
  };

  const handleConfirm = async () => {
    if (!confirmDialog.status) return;
    
    setIsSubmitting(true);
    try {
      await onStatusUpdate(confirmDialog.status);
      setConfirmDialog(prev => ({ ...prev, open: false }));
      setOpen(false);
    } catch (error) {
      console.error('Failed to update status:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (availableStatuses.length === 0) {
    return null;
  }

  return (
    <>
      <Button 
        variant="outline" 
        size="sm"
        className={className} 
        disabled={isUpdating}
        onClick={() => setOpen(true)}
      >
        {isUpdating ? (
          <>
            <Icon name="Loader2" className="mr-2 h-4 w-4 animate-spin" />
            Updating...
          </>
        ) : (
          'Update Status'
        )}
      </Button>

      {open && (
        <div 
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
          style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
          onClick={() => setOpen(false)}
        >
          <div 
            className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-lg min-w-[320px] sm:min-w-[400px] transform transition-all duration-200 scale-100"
            onClick={(e) => e.stopPropagation()}
            style={{
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1)'
            }}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Update Contract Status</h2>
              <button
                onClick={() => setOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors duration-200 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                aria-label="Close"
              >
                <Icon name="X" className="h-5 w-5" />
              </button>
            </div>
            
            {/* Content */}
            <div className="p-6">
              <div className="text-gray-600 dark:text-gray-300 text-sm mb-6">
                Current status: <ContractStatusBadge status={currentStatus} />
              </div>
            
              <div className="space-y-3">
                {availableStatuses.map((status) => (
                  <Button
                    key={status.value}
                    variant="outline"
                    className="w-full justify-start h-auto py-3"
                    onClick={() => handleStatusSelect(status.value as ContractStatus)}
                    disabled={isSubmitting}
                  >
                    <div className="text-left">
                      <div className="font-medium">{status.label}</div>
                      <div className="text-sm text-muted-foreground">
                        {status.description}
                      </div>
                    </div>
                  </Button>
                ))}
              </div>
            </div>
            
            {/* Footer */}
            <div className="bg-gray-50 dark:bg-gray-800/50 px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
              <Button 
                variant="outline" 
                onClick={() => setOpen(false)}
                disabled={isSubmitting}
                className="min-w-[120px] h-10 px-4"
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}

      <ConfirmDialog
        open={confirmDialog.open}
        title={`Confirm ${confirmDialog.statusInfo?.label || 'Status Update'}`}
        message={confirmDialog.statusInfo?.description || 'Are you sure you want to update the contract status?'}
        confirmText="Update Status"
        cancelText="Cancel"
        confirmVariant="default"
        onConfirm={handleConfirm}
        onCancel={() => setConfirmDialog(prev => ({ ...prev, open: false }))}
        isLoading={isSubmitting}
      />
    </>
  );
};

export default ContractStatusUpdate;
