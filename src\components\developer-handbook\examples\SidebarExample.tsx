import React, { useState } from "react";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { Button } from "@/components/ui/Button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import { UserRole } from "@/config/sidebar";
import { Container } from "@/components/layout";

export const SidebarExample: React.FC = () => {
  const [currentRole, setCurrentRole] = useState<UserRole>("FREELANCER");

  return (
    <DashboardLayout
      title="Sidebar Example"
      description="Example of the sidebar with different user roles"
      forceRole={currentRole}
    >
      <Container className="py-8 space-y-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">Sidebar Navigation</h1>
          <p className="text-muted-foreground">
            A responsive sidebar navigation component that adapts to different
            user roles.
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Role-based Navigation</CardTitle>
            <CardDescription>
              The sidebar automatically adjusts its navigation items based on
              the user&apos;s role.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Available Roles</h3>
              <div className="flex flex-wrap gap-2">
                {["CLIENT", "FREELANCER", "ADMIN"].map((role) => (
                  <Button
                    key={role}
                    variant={currentRole === role ? "default" : "outline"}
                    onClick={() => setCurrentRole(role as UserRole)}
                  >
                    {role}
                  </Button>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-lg font-medium">
                Current Role: {currentRole}
              </h3>
              <p className="text-muted-foreground">
                The sidebar updates automatically when you change the role
                above.
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Usage</CardTitle>
            <CardDescription>
              How to use the DashboardLayout with sidebar.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <pre className="bg-muted p-4 rounded-md overflow-x-auto text-sm">
              {`import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { UserRole } from '@/config/sidebar';

export default function MyPage() {
  const userRole: UserRole = 'FREELANCER';

  return (
    <DashboardLayout 
      title="Page Title"
      description="Page description"
    >
      {/* Your page content */}
      <div className="p-6">
        <h1>My Page Content</h1>
      </div>
    </DashboardLayout>
  );
}`}
            </pre>
          </CardContent>
        </Card>
      </Container>
    </DashboardLayout>
  );
};

export default SidebarExample;
