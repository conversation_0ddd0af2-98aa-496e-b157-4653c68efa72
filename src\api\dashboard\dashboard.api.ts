import { graphQLClient } from '../../lib/graphql/graphqlClient';
import { GET_CLIENT_DASHBOARD_DATA, GET_FREELANCER_DASHBOARD_DATA } from './dashboard.queries';
import type { GraphQLResponse } from './dashboard.types';

function handleApiError(operation: string, error: unknown): never {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  const errorDetails = {
    operation,
    error: error instanceof Error ? {
      name: error.name,
      message: error.message,
      stack: error.stack
    } : error,
    timestamp: new Date().toISOString()
  };
  
  console.error(`[${operation} Error]:`, JSON.stringify(errorDetails, null, 2));
  throw new Error(`Failed to ${operation.toLowerCase()}: ${errorMessage}`);
}

export const dashboardApi = {
  getClientDashboardData: async (clientId: string): Promise<{ getUser: GraphQLResponse }> => {
    const operation = 'getClientDashboardData';
    
    try {
      const response = await graph<PERSON>Client.execute<{ getUser: GraphQLResponse }>(
        GET_CLIENT_DASHBOARD_DATA,
        { id: clientId },
        { authMode: 'userPool' }
      );
      
      if (!response?.getUser) {
        const error = new Error('User data not found in response');
        console.error(`[${operation} Error]: No user data found in response`, {
          clientId,
          response,
          timestamp: new Date().toISOString()
        });
        throw error;
      }
      
      return { getUser: response.getUser };
    } catch (error) {
      return handleApiError('getClientDashboardData', error);
    }
  },

  getFreelancerDashboardData: async (freelancerId: string): Promise<GraphQLResponse> => {
    const operation = 'getFreelancerDashboardData';
    
    try {
      const response = await graphQLClient.execute<{ getUser: GraphQLResponse }>(
        GET_FREELANCER_DASHBOARD_DATA,
        { id: freelancerId },
        { authMode: 'userPool' }
      );
      
      if (!response?.getUser) {
        const error = new Error('User data not found in response');
        console.error(`[${operation} Error]: No user data found in response`, {
          freelancerId,
          response,
          timestamp: new Date().toISOString()
        });
        throw error;
      }
      
      return response.getUser;
    } catch (error) {
      return handleApiError('getFreelancerDashboardData', error);
    }
  },
};
