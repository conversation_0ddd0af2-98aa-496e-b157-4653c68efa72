import { gql } from '@apollo/client';

export const CREATE_JOB_CATEGORY = gql`
  mutation CreateJobCategory($input: CreateJobCategoryInput!) {
    createJobCategory(input: $input) {
      id
      name
      description
      isActive
      createdAt
      updatedAt
    }
  }
`;

export const UPDATE_JOB_CATEGORY = gql`
  mutation UpdateJobCategory($input: UpdateJobCategoryInput!) {
    updateJobCategory(input: $input) {
      id
      name
      description
      isActive
      updatedAt
    }
  }
`;

export const DELETE_JOB_CATEGORY = gql`
  mutation DeleteJobCategory($input: DeleteJobCategoryInput!) {
    deleteJobCategory(input: $input) {
      id
    }
  }
`;
