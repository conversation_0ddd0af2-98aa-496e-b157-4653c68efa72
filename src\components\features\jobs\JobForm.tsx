import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { CreateJobInput, JobCategory } from '@/types/features/jobs/job.types';
import { JobCategory as ApiJobCategory } from '@/types/features/job-categories/job-category.types';
import { jobCategoryService } from '@/api/job-categories/job-category.service';
import { Form, FormField, Input, Textarea, Select, Button, Loading, Card, CardContent, CardHeader, CardTitle } from '@/components/ui';
import { DatePicker } from '@/components/ui/DatePicker';
import { useAuth } from '@/lib/auth/AuthContext';



type JobFormData = {
  title: string;
  description: string;
  budget: number;
  category: JobCategory;
  deadline: Date | null;
};

const jobSchema = yup.object().shape({
  title: yup.string().required('Title is required').max(100, 'Title is too long'),
  description: yup.string().required('Description is required').max(5000, 'Description is too long'),
  budget: yup
    .number()
    .typeError('Budget must be a number')
    .required('Budget is required')
    .min(5, 'Minimum budget is $5')
    .max(1000000, 'Maximum budget is $1,000,000'),
  category: yup.string().required('Category is required'),
  deadline: yup.date().nullable().required('Deadline is required').min(new Date(), 'Deadline must be in the future'),
}) as yup.ObjectSchema<JobFormData>;

interface JobFormProps {
  initialData?: Partial<JobFormData>;
  clientId: string;
  onSubmit: (data: CreateJobInput) => Promise<void>;
  isSubmitting: boolean;
  submitButtonText?: string;
  submitButtonClassName?: string;
}

export const JobForm: React.FC<JobFormProps> = ({
  initialData,
  clientId,
  onSubmit,
  isSubmitting,
  submitButtonText = 'Post Job',
  submitButtonClassName = '',
}: JobFormProps) => {
  const { isAuthenticated } = useAuth();
  const [jobCategories, setJobCategories] = useState<ApiJobCategory[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(true);
  
  useEffect(() => {
    const loadJobCategories = async () => {
      try {
        setIsLoadingCategories(true);
        const response = await jobCategoryService.listJobCategories(undefined, 100);
        setJobCategories(response.items || []);
      } catch (error) {
        console.error('Error loading job categories:', error);
      } finally {
        setIsLoadingCategories(false);
      }
    };
    
    if (isAuthenticated) {
      loadJobCategories();
    }
  }, [isAuthenticated]);

  const form = useForm<JobFormData>({
    resolver: yupResolver(jobSchema),
    defaultValues: {
      title: initialData?.title || '',
      description: initialData?.description || '',
      budget: initialData?.budget || 0,
      category: initialData?.category || '',
      deadline: initialData?.deadline ? new Date(initialData.deadline) : null,
    },
  });

  const { register, handleSubmit, control, formState: { errors } } = form;

  const onSubmitForm = async (formData: JobFormData) => {
    if (!formData.deadline) {
      console.error('Deadline is required');
      return;
    }

    const jobData: CreateJobInput = {
      title: formData.title,
      description: formData.description,
      budget: formData.budget,
      category: formData.category,
      deadline: formData.deadline.toISOString(),
      clientId: clientId,
      skills: [],
    };
    
    try {
      await onSubmit(jobData);
    } catch (error) {
      console.error('Error submitting job:', error);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Job Details</CardTitle>
      </CardHeader>
      <CardContent>
        <Form onSubmit={handleSubmit(onSubmitForm)}>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <FormField
              label="Job Title"
              required
              error={errors.title?.message}
            >
              <Input
                {...register('title')}
                placeholder="e.g. Website Development"
                error={!!errors.title}
              />
            </FormField>

            <FormField
              label="Category"
              required
              error={errors.category?.message}
            >
              <Select
                {...register('category')}
                options={[
                  { value: '', label: 'Select a category' },
                  ...jobCategories.map(cat => ({ value: cat.id, label: cat.name }))
                ]}
                placeholder="Select a category"
                error={!!errors.category}
                disabled={isLoadingCategories}
              />
            </FormField>
          </div>

          <FormField
            label="Job Description"
            required
            error={errors.description?.message}
            hint="Provide a detailed description of the job requirements and expectations"
          >
            <Textarea
              {...register('description')}
              rows={6}
              placeholder="Describe the job in detail..."
              error={!!errors.description}
            />
          </FormField>

          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <FormField
              label="Budget"
              required
              error={errors.budget?.message}
              hint="Enter the total budget for this project in USD"
            >
              <Input
                type="number"
                {...register('budget')}
                placeholder="0.00"
                step="0.01"
                min="0"
                prefix="$"
                error={!!errors.budget}
                className="w-full"
              />
            </FormField>

            <FormField
              label="Deadline"
              required
              error={errors.deadline?.message}
              hint="When do you need this project completed?"
            >
              <Controller
                name="deadline"
                control={control}
                render={({ field }) => (
                  <DatePicker
                    value={field.value}
                    onChange={(date) => field.onChange(date || null)}
                    placeholder="Select deadline"
                    minDate={new Date()}
                    error={!!errors.deadline}
                  />
                )}
              />
            </FormField>
          </div>

          <div className="flex justify-end pt-4">
            <Button
              type="submit"
              disabled={isSubmitting}
              className={`min-w-[120px] ${submitButtonClassName}`}
            >
              {isSubmitting ? (
                <>
                  <Loading size="sm" className="mr-2" />
                  Submitting...
                </>
              ) : (
                submitButtonText
              )}
            </Button>
          </div>
        </Form>
      </CardContent>
    </Card>
  );
};

export default JobForm;
