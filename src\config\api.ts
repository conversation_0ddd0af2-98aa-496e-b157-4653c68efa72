export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'https://qj8altpvmi.execute-api.us-east-1.amazonaws.com/local',

  AUTH: {
    SIGNUP: '/auth/signup',
    VERIFY: '/confirm-user',
  },

  getUrl: (endpoint: string): string => {
    const base = API_CONFIG.BASE_URL.replace(/\/+$/, '');
    const path = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    return `${base}${path}`;
  }
};
