"use client";

import { useState, useCallback, useEffect, useMemo } from "react";
import {
  UIMessage,
  Conversation,
  MessagingUser,
} from "@/types/features/messaging/messaging.types";

type Message = UIMessage;
import { MessageThread } from "./components/MessageThread";
import { MessageInputBar } from "./components/MessageInputBar";
import { ConversationList } from "./components/ConversationList";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/Button";
import { Icon } from "@/components/ui/Icon";
import Image from "next/image";
import { ConnectionBasedMessaging } from "./ConnectionBasedMessaging";
import { UserRole } from "@/types/enums";

export interface MessagingContainerProps {
  currentUser: MessagingUser;
  currentUserRole: UserRole;
  initialConversations?: Conversation[];
  onSendMessage: (conversationId: string, content: string) => Promise<void>;
  onLoadMoreMessages: (
    conversationId: string,
    before: Date
  ) => Promise<Message[]>;
  onConversationSelect?: (conversationId: string) => void;
  onFileUpload?: (file: File) => Promise<string>;
  className?: string;
  showBackButton?: boolean;
  onBack?: () => void;
}

export function MessagingContainer({
  currentUser,
  currentUserRole,
  initialConversations = [],
  onSendMessage,
  onLoadMoreMessages,
  onConversationSelect,
  onFileUpload,
  className,
  showBackButton = false,
  onBack,
  jobId,
  otherUserId,
  otherUserRole,
}: MessagingContainerProps & {
  jobId?: string;
  otherUserId?: string;
  otherUserRole?: "client" | "freelancer";
}) {
  const [conversations, setConversations] =
    useState<Conversation[]>(initialConversations);
  const [selectedConversation, setSelectedConversation] =
    useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [isTyping, setIsTyping] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [hasAutoSelected, setHasAutoSelected] = useState(false);
  const [searchQuery] = useState("");
  const [showNewMessageForm, setShowNewMessageForm] = useState(false);

  const showConnectionBasedMessaging = useMemo(() => {
    return jobId && otherUserId && otherUserRole && !selectedConversation;
  }, [jobId, otherUserId, otherUserRole, selectedConversation]);

  const handleNewThreadCreated = useCallback(
    (conversationId: string) => {
      const newConversation = conversations.find(
        (c) => c.id === conversationId
      );
      if (newConversation) {
        setSelectedConversation(newConversation);
        setShowNewMessageForm(false);
      }
    },
    [conversations]
  );

  useEffect(() => {
    setConversations(initialConversations);
    setHasAutoSelected(false);
  }, [initialConversations]);

  const handleSelectConversation = useCallback(
    async (conversationId: string) => {
      const conversation = conversations.find((c) => c.id === conversationId);
      if (!conversation) return;

      setSelectedConversation(conversation);
      setIsLoading(true);

      try {
        const newMessages = await onLoadMoreMessages(
          conversationId,
          new Date()
        );
        setMessages(newMessages);
        setHasMore(newMessages.length >= 20);

        setConversations((prev) =>
          prev.map((c) =>
            c.id === conversationId ? { ...c, unreadCount: 0 } : c
          )
        );

        onConversationSelect?.(conversationId);
        setIsMobileMenuOpen(false);
      } catch (error) {
        console.error("Failed to load messages:", error);
      } finally {
        setIsLoading(false);
      }
    },
    [conversations, onLoadMoreMessages, onConversationSelect]
  );

  useEffect(() => {
    if (
      conversations.length > 0 &&
      !selectedConversation &&
      !hasAutoSelected &&
      !showConnectionBasedMessaging
    ) {
      setSelectedConversation(conversations[0]);
      setHasAutoSelected(true);
    }
  }, [
    conversations,
    selectedConversation,
    hasAutoSelected,
    showConnectionBasedMessaging,
  ]);

  const handleSendMessage = async (content: string) => {
    if (!selectedConversation || !content.trim()) return;

    const otherUser = selectedConversation.participants.find(
      (p) => p.id !== currentUser.id
    );
    const newMessage: UIMessage = {
      id: `temp-${Date.now()}`,
      content,
      senderId: currentUser.id,
      receiverId: otherUser?.id || "",
      conversationId: selectedConversation.id,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: "sending",
      type: "text",
      isOwn: true,
      sender: currentUser,
      receiver: otherUser || currentUser,
    };

    setMessages((prev) => [...prev, newMessage]);
    setIsSending(true);

    try {
      await onSendMessage(selectedConversation.id, content);

      setConversations((prev) =>
        prev.map((conv) => {
          if (conv.id === selectedConversation.id) {
            const updatedConv = { ...conv };
            updatedConv.lastMessage = {
              id: `temp-${Date.now()}`,
              content,
              senderId: currentUser.id,
              createdAt: new Date().toISOString(),
              isOwn: true,
              status: "sent",
              type: "text",
            };
            updatedConv.updatedAt = new Date().toISOString();
            return updatedConv;
          }
          return conv;
        })
      );
      setIsSending(false);
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  };

  const handleLoadMore = useCallback(async () => {
    if (!selectedConversation || isLoading || !hasMore || messages.length === 0)
      return;

    setIsLoading(true);
    try {
      const oldestMessage = messages[0];
      const newMessages = await onLoadMoreMessages(
        selectedConversation.id,
        new Date(oldestMessage.createdAt)
      );

      setMessages((prev) => [...newMessages, ...prev]);
      setHasMore(newMessages.length >= 20);
    } catch (error) {
      console.error("Failed to load more messages:", error);
    } finally {
      setIsLoading(false);
    }
  }, [selectedConversation, messages, isLoading, hasMore, onLoadMoreMessages]);

  useEffect(() => {
    const currentIds = conversations
      .map((c) => c.id)
      .sort()
      .join(",");
    const newIds = initialConversations
      .map((c) => c.id)
      .sort()
      .join(",");

    if (currentIds !== newIds) {
      setConversations(initialConversations);
    }
  }, [initialConversations, conversations]);

  useEffect(() => {
    if (conversations.length > 0 && !selectedConversation) {
      handleSelectConversation(conversations[0].id);
    }
  }, [conversations, selectedConversation, handleSelectConversation]);

  const otherUser = selectedConversation?.participants.find(
    (p) => p.id !== currentUser.id
  );

  type Participant = {
    id: string;
    name?: string;
    avatar?: string;
    role?: string;
    email?: string;
    isOnline?: boolean;
  };

  const uiOtherUser = otherUser
    ? {
        id: otherUser.id,
        name: otherUser.name || "Unknown",
        email: (otherUser as Participant).email || "",
        avatar: otherUser.avatar || "/default-avatar.png",
        role: (otherUser.role === "CLIENT" ? "CLIENT" : "FREELANCER") as
          | "CLIENT"
          | "FREELANCER",
        isOnline: (otherUser as Participant).isOnline || false,
      }
    : undefined;

  const filteredConversations = conversations.filter((conversation) => {
    if (!searchQuery) return true;
    const otherUser = conversation.participants.find(
      (p) => p.id !== currentUser.id
    );
    return otherUser?.name.toLowerCase().includes(searchQuery.toLowerCase());
  });

  if (showConnectionBasedMessaging) {
    return (
      <div
        className={cn(
          "flex flex-col h-full bg-white rounded-lg border border-gray-200 overflow-hidden p-4",
          className
        )}
      >
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Start a Conversation</h2>
          {showBackButton && onBack && (
            <Button variant="ghost" size="sm" onClick={onBack}>
              Back
            </Button>
          )}
        </div>

        <ConnectionBasedMessaging
          jobId={jobId!}
          otherUserId={otherUserId!}
          otherUserRole={otherUserRole!}
          onThreadCreated={handleNewThreadCreated}
          showAsCard={false}
        />
      </div>
    );
  }

  return (
    <div
      className={cn(
        "flex h-full bg-white rounded-lg border border-gray-200 overflow-hidden",
        className
      )}
    >
      {/* Mobile menu button */}
      <div className="md:hidden fixed bottom-4 right-4 z-50">
        <Button
          variant="default"
          size="icon"
          className="rounded-full w-14 h-14 shadow-lg"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        >
          {isMobileMenuOpen ? (
            <Icon name="X" className="h-6 w-6" />
          ) : (
            <Icon name="MessageSquarePlus" className="h-6 w-6" />
          )}
        </Button>
      </div>

      {/* Sidebar - Conversations */}
      <div
        className={cn(
          "absolute md:relative z-10 w-full max-w-xs bg-background border-r border-border h-full transition-transform duration-300 ease-in-out",
          "md:translate-x-0",
          isMobileMenuOpen ? "translate-x-0" : "-translate-x-full",
          "md:block",
          "shadow-lg md:shadow-none"
        )}
      >
        <div className="p-4 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-lg font-semibold">Messages</h2>
          {jobId && otherUserId && otherUserRole && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowNewMessageForm(!showNewMessageForm)}
            >
              New Message
            </Button>
          )}
        </div>

        {showNewMessageForm && jobId && otherUserId && otherUserRole && (
          <div className="p-4 border-b border-gray-100 bg-gray-50">
            <ConnectionBasedMessaging
              jobId={jobId}
              otherUserId={otherUserId}
              otherUserRole={otherUserRole}
              onThreadCreated={handleNewThreadCreated}
              showAsCard={false}
              buttonVariant="ghost"
              buttonText="Cancel"
              className="w-full pl-10 pr-4 py-2 bg-muted/50 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            />
          </div>
        )}

        <ConversationList
          conversations={filteredConversations}
          selectedConversationId={selectedConversation?.id}
          onSelectConversation={handleSelectConversation}
          currentUserId={currentUser.id}
          currentUserRole={currentUserRole}
          loading={isLoading && messages.length === 0}
        />
      </div>

      {/* Main chat area */}
      <div className="flex-1 flex flex-col h-full">
        {selectedConversation ? (
          <>
            {/* Chat header */}
            <div className="border-b border-border p-4 flex items-center justify-between bg-background/80 backdrop-blur-sm sticky top-0 z-10">
              <div className="flex items-center space-x-3">
                {showBackButton && (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={onBack}
                    className="md:hidden"
                  >
                    <Icon name="X" className="h-5 w-5" />
                  </Button>
                )}
                <div className="relative">
                  <div className="w-10 h-10 rounded-full bg-muted-foreground/10 flex items-center justify-center overflow-hidden">
                    {otherUser?.avatar ? (
                      <Image
                        src={otherUser.avatar}
                        alt={otherUser.name}
                        className="w-full h-full object-cover"
                        width={40}
                        height={40}
                        unoptimized={!otherUser.avatar.startsWith("/")}
                      />
                    ) : (
                      <span className="text-lg font-medium text-muted-foreground">
                        {otherUser?.name?.charAt(0).toUpperCase() || "?"}
                      </span>
                    )}
                  </div>
                  {uiOtherUser?.isOnline && (
                    <span className="absolute bottom-0 right-0 w-3 h-3 rounded-full bg-green-500 border-2 border-background"></span>
                  )}
                </div>
                <div>
                  <h3 className="font-medium">
                    {otherUser?.name || "Unknown User"}
                  </h3>
                  <p className="text-xs text-muted-foreground">
                    {uiOtherUser?.isOnline ? "Online" : "Offline"}
                  </p>
                </div>
              </div>
            </div>

            {/* Message thread */}
            <MessageThread
              messages={messages}
              currentUser={{
                id: currentUser.id,
                name: currentUser.name,
                email: currentUser.email || "",
                avatar: currentUser.avatar,
                role: currentUserRole === "CLIENT" ? "CLIENT" : "FREELANCER",
                isOnline: true,
              }}
              otherUser={uiOtherUser!}
              loading={isLoading}
              onLoadMore={handleLoadMore}
              hasMore={hasMore}
              isTyping={isTyping}
              className="flex-1"
            />

            {/* Message input */}
            <MessageInputBar
              onSend={handleSendMessage}
              onTyping={setIsTyping}
              disabled={isSending}
              onFileUpload={onFileUpload}
            />
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center text-muted-foreground">
            <p>Select a conversation to start messaging</p>
          </div>
        )}
      </div>
    </div>
  );
}
