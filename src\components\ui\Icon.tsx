import { LucideIcon, LucideProps } from "lucide-react";
import { forwardRef } from "react";
import * as Icons from "lucide-react";
import { cn } from "@/lib/utils";
import type { IconName } from "@/types/components/Icon";

export const iconSizes = {
  xs: "h-3 w-3",
  sm: "h-4 w-4",
  md: "h-5 w-5",
  lg: "h-6 w-6",
  xl: "h-8 w-8",
  "2xl": "h-10 w-10",
} as const;

const iconSizeClasses = iconSizes;

export type { IconName };

interface IconProps
  extends Omit<LucideProps, "ref" | "name" | "size" | "className"> {
  name: IconName;
  size?: keyof typeof iconSizeClasses;
  className?: string;
}

/**
 * A reusable Icon component that renders Lucide icons.
 * @example
 * <Icon name="Home" size="md" />
 */
const Icon = forwardRef<SVGSVGElement, IconProps>(
  ({ name, size = "md", className = "", ...props }, ref) => {
    const LucideIcon = Icons[name] as LucideIcon;

    if (!LucideIcon) {
      console.warn(`Icon "${name}" not found`);
      return null;
    }

    const sizeClass = iconSizeClasses[size] || iconSizeClasses.md;

    return (
      <LucideIcon
        ref={ref}
        className={cn(sizeClass, className)}
        {...props}
        aria-hidden="true"
      />
    );
  }
);

Icon.displayName = "Icon";

export { Icon };
export type { IconProps };
