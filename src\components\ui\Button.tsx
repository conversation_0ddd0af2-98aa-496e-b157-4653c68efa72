import React from 'react';
import { cn } from '@/lib/utils';
import type { ButtonProps } from '@/types/components/Button';

const buttonVariants = {
  variant: {
    default: 'bg-primary text-primary-foreground hover:bg-fern-green-600 focus-visible:ring-primary',
    destructive: 'bg-destructive text-destructive-foreground hover:bg-red-600 focus-visible:ring-destructive',
    outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground focus-visible:ring-ring',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-sage-300 focus-visible:ring-secondary',
    ghost: 'hover:bg-accent hover:text-accent-foreground focus-visible:ring-accent',
    link: 'text-primary underline-offset-4 hover:underline focus-visible:ring-primary',
  },
  size: {
  default: 'h-12 px-6 py-3',
  md: 'h-12 px-6 py-3',
  sm: 'h-10 rounded-md px-4 py-2',
  lg: 'h-14 rounded-lg px-8 py-4',
  icon: 'h-12 w-12',
  },
};

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'default', size = 'default', asChild = false, children, ...props }, ref) => {
    const getButtonStyles = () => {
      const baseStyles = {
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        whiteSpace: 'nowrap' as const,
        borderRadius: '8px',
        fontSize: '16px',
        fontWeight: '600',
        transition: 'all 0.2s ease-in-out',
        border: 'none',
        cursor: 'pointer',
      };

      const sizeStyles = {
  default: { height: '48px', padding: '12px 24px' },
  md: { height: '48px', padding: '12px 24px' },
        sm: { height: '40px', padding: '8px 16px' },
        lg: { height: '56px', padding: '16px 32px' },
        icon: { height: '48px', width: '48px', padding: '0' },
      };

      const variantStyles = {
        default: {
          backgroundColor: 'var(--primary)',
          color: 'var(--primary-foreground)',
        },
        destructive: {
          backgroundColor: 'var(--destructive)',
          color: 'var(--destructive-foreground)',
        },
        outline: {
          backgroundColor: 'var(--background)',
          color: 'var(--foreground)',
          border: '2px solid var(--input)',
        },
        secondary: {
          backgroundColor: 'var(--secondary)',
          color: 'var(--secondary-foreground)',
        },
        ghost: {
          backgroundColor: 'transparent',
          color: 'var(--foreground)',
        },
        link: {
          backgroundColor: 'transparent',
          color: 'var(--primary)',
          textDecoration: 'underline',
          textUnderlineOffset: '4px',
        },
      };

      return {
        ...baseStyles,
        ...sizeStyles[size],
        ...variantStyles[variant],
      };
    };

    const buttonProps = {
      style: getButtonStyles(),
      className: cn(
        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
        className
      ),
      ref,
      ...props,
    };

    if (asChild && React.Children.count(children) === 1) {
      const child = React.Children.only(children) as React.ReactElement<{
        className?: string;
        style?: React.CSSProperties;
        [key: string]: unknown;
      }>;
      
      return React.cloneElement(child, {
        ...buttonProps,
        className: cn(buttonProps.className, child.props?.className),
        style: {
          ...child.props?.style,
          ...buttonProps.style,
        },
      });
    }

    return (
      <button {...buttonProps}>
        {children}
      </button>
    );
  }
);

Button.displayName = 'Button';

export { Button, buttonVariants };
