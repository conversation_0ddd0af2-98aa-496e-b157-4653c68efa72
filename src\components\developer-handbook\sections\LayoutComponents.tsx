import React from "react";
import { ComponentCard } from "../examples/ComponentCard";
import { SidebarExample } from "../examples/SidebarExample";

export const LayoutComponents = () => {
  return (
    <div className="space-y-12">
      <h2 className="text-3xl font-bold tracking-tight">Layout Components</h2>

      <ComponentCard
        title="AppLayout"
        description="The main application layout that includes a header, sidebar, and content area."
        code={`import { AppLayout } from '@/components/layout/AppLayout';

function MyApp() {
  return (
    <AppLayout>
      {/* Your page content */}
    </AppLayout>
  );
}`}
        usage={`
import { AppLayout } from '@/components/layout/AppLayout';

export default function Page() {
  return (
    <AppLayout>
      <h1>Dashboard</h1>
      <p>Welcome to your dashboard</p>
    </AppLayout>
  );
}`}
      >
        <div className="p-4 border rounded-md bg-muted/20">
          <div className="h-8 bg-muted rounded mb-4"></div>
          <div className="flex gap-4">
            <div className="w-48 h-64 bg-muted rounded"></div>
            <div className="flex-1 h-64 bg-background rounded border">
              <div className="p-4">
                <h3 className="font-medium mb-2">Page Content</h3>
                <p className="text-sm text-muted-foreground">
                  Your page content goes here
                </p>
              </div>
            </div>
          </div>
        </div>
      </ComponentCard>

      <ComponentCard
        title="DashboardLayout"
        description="A specialized layout for dashboard pages with a sidebar and main content area."
        code={`import { DashboardLayout } from '@/components/layout/DashboardLayout';

function DashboardPage() {
  return (
    <DashboardLayout>
      {/* Dashboard content */}
    </DashboardLayout>
  );
}`}
        usage={`
import { DashboardLayout } from '@/components/layout/DashboardLayout';

export default function DashboardPage() {
  return (
    <DashboardLayout>
      <h1>Welcome to Dashboard</h1>
      {/* Dashboard widgets and content */}
    </DashboardLayout>
  );
}`}
      >
        <div className="p-4 border rounded-md bg-muted/20">
          <div className="h-12 bg-muted rounded mb-4"></div>
          <div className="flex gap-4">
            <div className="w-56 h-96 bg-muted/50 rounded">
              <div className="p-4">
                <h4 className="font-medium mb-4">Dashboard Menu</h4>
                <div className="space-y-2">
                  {["Overview", "Analytics", "Projects", "Settings"].map(
                    (item) => (
                      <div key={item} className="h-6 bg-muted rounded"></div>
                    )
                  )}
                </div>
              </div>
            </div>
            <div className="flex-1 h-96 bg-background rounded border p-4">
              <h3 className="font-medium mb-4">Dashboard Content</h3>
              <div className="grid grid-cols-2 gap-4">
                {[1, 2, 3, 4].map((item) => (
                  <div
                    key={item}
                    className="h-32 bg-muted/20 rounded border"
                  ></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </ComponentCard>

      <ComponentCard
        title="Container"
        description="A responsive container that centers your content and applies consistent horizontal padding."
        code={`import { Container } from '@/components/layout/Container';

function MyComponent() {
  return (
    <Container>
      {/* Your content */}
    </Container>
  );
}`}
        usage={`import { Container } from '@/components/layout/Container';

export default function Page() {
  return (
    <Container>
      <h1>Page Title</h1>
      <p>Your page content goes here, nicely contained with proper padding.</p>
    </Container>
  );
}`}
      >
        <div className="border rounded-md p-4">
          <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-muted/20 p-6 rounded text-center">
              <p>This content is wrapped in a Container component</p>
              <p className="text-sm text-muted-foreground">
                It has max-width and horizontal padding applied
              </p>
            </div>
          </div>
        </div>
      </ComponentCard>

      <ComponentCard
        title="ContentHeader"
        description="A consistent header component for page titles and actions."
        code={`import { ContentHeader } from '@/components/layout/ContentHeader';

function MyPage() {
  return (
    <ContentHeader 
      title="Page Title"
      description="Page description"
      actions={
        <Button>Action</Button>
      }
    />
  );
}`}
        usage={`import { ContentHeader } from '@/components/layout/ContentHeader';
import { Button } from '@/components/ui/Button';

export default function Page() {
  return (
    <div>
      <ContentHeader
        title="Users"
        description="Manage your users"
        actions={
          <Button>Add User</Button>
        }
      />
      {/* Page content */}
    </div>
  );
}`}
        props={[
          {
            name: "title",
            type: "string",
            default: "undefined",
            description: "The title of the page",
          },
          {
            name: "description",
            type: "string",
            default: "undefined",
            description: "Optional description text",
          },
          {
            name: "actions",
            type: "React.ReactNode",
            default: "null",
            description:
              "Action buttons or elements to display on the right side",
          },
        ]}
      >
        <div className="border rounded-md p-4">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="text-lg font-medium">Page Title</h3>
              <p className="text-sm text-muted-foreground">
                This is a page description
              </p>
            </div>
            <div className="h-9 w-20 bg-muted rounded"></div>
          </div>
        </div>
      </ComponentCard>

      <ComponentCard
        title="Sidebar Navigation"
        description="A responsive sidebar navigation component that adapts to different user roles."
        code={`import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { UserRole } from '@/config/sidebar';

export default function MyPage() {
  const userRole: UserRole = 'FREELANCER';

  return (
    <DashboardLayout 
      title="Page Title"
      description="Page description"
      forceRole={userRole}
    >
      <div className="p-6">
        <h1>My Page Content</h1>
      </div>
    </DashboardLayout>
  );
}`}
        usage={`
import { DashboardLayout } from '@/components/layout/DashboardLayout';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <DashboardLayout>
      {children}
    </DashboardLayout>
  );
}

import { UserRole } from '@/config/sidebar';

export default function DashboardPage() {
  const userRole: UserRole = 'FREELANCER';
  
  return (
    <>
      <h1>Dashboard</h1>
      {/* Your page content */}
    </>
  );
}`}
        props={[
          {
            name: "title",
            type: "string",
            default: "undefined",
            description: "The page title shown in the header (required)",
          },
          {
            name: "description",
            type: "string",
            default: "undefined",
            description: "Optional description shown below the title",
          },
          {
            name: "forceRole",
            type: "UserRole",
            default: "undefined",
            description: "Force a specific role for the sidebar (optional)",
          },
          {
            name: "children",
            type: "React.ReactNode",
            default: "undefined",
            description:
              "The page content to be rendered inside the layout (required)",
          },
        ]}
      >
        <SidebarExample />
      </ComponentCard>

      <ComponentCard
        title="Page Container"
        description="A container component that provides consistent padding and max-width for page content."
        code={`import { PageContainer } from '@/components/layout/PageContainer';

export default function MyPage() {
  return (
    <PageContainer>
      <h1>Page Title</h1>
      <p>Your page content here</p>
    </PageContainer>
  );
}`}
        usage={`
<PageContainer>
  <h1>Page Title</h1>
  <p>Your content here</p>
</PageContainer>

<PageContainer className="my-custom-class">
  {/* Content */}
</PageContainer>`}
        props={[
          {
            name: "children",
            default: "undefined",
            type: "React.ReactNode",
            description: "The content to be rendered inside the container",
          },
          {
            name: "className",
            default: "undefined",
            type: "string",
            description: "Additional CSS classes to apply to the container",
          },
        ]}
      >
        <div className="border rounded-md p-4">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-muted/20 p-6 rounded text-center">
              <p>This content is wrapped in a PageContainer</p>
              <p className="text-sm text-muted-foreground mt-2">
                It has max-width and responsive padding applied
              </p>
            </div>
          </div>
        </div>
      </ComponentCard>
    </div>
  );
};

export default LayoutComponents;
