import { Graph<PERSON>Error } from 'graphql';
import { ApiError as CommonApiError } from '@/types/common/api.types';
import { APIError as MessagingAPIError } from '@/types/features/messaging/messaging.types';

export enum ErrorCode {
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',

  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  SESSION_EXPIRED = 'SESSION_EXPIRED',

  NOT_FOUND = 'NOT_FOUND',
  ALREADY_EXISTS = 'ALREADY_EXISTS',
  CONFLICT = 'CONFLICT',

  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
}

export interface AppError extends Error {
  code: ErrorCode;
  statusCode?: number;
  details?: Record<string, unknown>;
  originalError?: unknown;
}

export class ValidationError extends Error implements AppError {
  code = ErrorCode.VALIDATION_ERROR;
  statusCode = 400;
  details: Record<string, string[]>;

  constructor(message: string, details: Record<string, string[]>) {
    super(message);
    this.name = 'ValidationError';
    this.details = details;
  }
}

export class UnauthorizedError extends Error implements AppError {
  code = ErrorCode.UNAUTHORIZED;
  statusCode = 401;

  constructor(message = 'Authentication required') {
    super(message);
    this.name = 'UnauthorizedError';
  }
}

export class ForbiddenError extends Error implements AppError {
  code = ErrorCode.FORBIDDEN;
  statusCode = 403;

  constructor(message = 'Insufficient permissions') {
    super(message);
    this.name = 'ForbiddenError';
  }
}

export class NotFoundError extends Error implements AppError {
  code = ErrorCode.NOT_FOUND;
  statusCode = 404;

  constructor(resource: string, id?: string | number) {
    super(id ? `${resource} with ID ${id} not found` : `${resource} not found`);
    this.name = 'NotFoundError';
  }
}

export const createError = (
  message: string,
  code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
  options: {
    statusCode?: number;
    details?: Record<string, unknown>;
    originalError?: unknown;
  } = {}
): AppError => {
  const error = new Error(message) as AppError;
  error.code = code;
  error.statusCode = options.statusCode || 500;
  error.details = options.details;
  error.originalError = options.originalError;
  return error;
};

export const handleApiError = (error: unknown): AppError => {
  if (error instanceof GraphQLError) {
    return createError(
      error.message,
      (error.extensions?.code as ErrorCode) || ErrorCode.INTERNAL_SERVER_ERROR,
      {
        statusCode: error.extensions?.statusCode as number || 500,
        details: error.extensions,
        originalError: error
      }
    );
  }

  if (error instanceof Error) {
    return createError(
      error.message,
      ErrorCode.UNKNOWN_ERROR,
      { originalError: error }
    );
  }

  if (typeof error === 'string') {
    return createError(error, ErrorCode.UNKNOWN_ERROR);
  }

  if (error && typeof error === 'object') {
    const apiError = error as CommonApiError | MessagingAPIError;
    return createError(
      apiError.message || 'Unknown API error',
      (apiError.code as ErrorCode) || ErrorCode.UNKNOWN_ERROR,
      {
        statusCode: 'statusCode' in apiError ? apiError.statusCode : 500,
        details: 'details' in apiError ? apiError.details : undefined,
        originalError: error
      }
    );
  }

  return createError('An unknown error occurred', ErrorCode.UNKNOWN_ERROR, {
    originalError: error
  });
};

export const handleError = (error: unknown, context?: string): AppError => {
  const appError = handleApiError(error);

  if (process.env.NODE_ENV === 'development' || typeof appError.statusCode === 'number' && appError.statusCode >= 500) {
    console.error(`[${context || 'Error'}]`, {
      message: appError.message,
      code: appError.code,
      statusCode: appError.statusCode,
      details: appError.details,
      originalError: appError.originalError,
      stack: appError.stack
    });
  }

  return appError;
};

export const isAppError = (error: unknown): error is AppError => {
  return (
    typeof error === 'object' &&
    error !== null &&
    'code' in error &&
    'message' in error
  );
};

export const toUserFriendlyMessage = (error: unknown): string => {
  if (!error) return 'An unknown error occurred';

  if (typeof error === 'string') return error;

  if (isAppError(error)) {
    switch (error.code) {
      case ErrorCode.UNAUTHORIZED:
        return 'Please log in to continue';
      case ErrorCode.FORBIDDEN:
        return 'You do not have permission to perform this action';
      case ErrorCode.NOT_FOUND:
        return 'The requested resource was not found';
      case ErrorCode.VALIDATION_ERROR:
        return 'Please check your input and try again';
      case ErrorCode.NETWORK_ERROR:
        return 'Unable to connect to the server. Please check your internet connection';
      default:
        return error.message || 'An unexpected error occurred';
    }
  }

  if (error instanceof Error) {
    return error.message || 'An unexpected error occurred';
  }

  return 'An unknown error occurred';
};
