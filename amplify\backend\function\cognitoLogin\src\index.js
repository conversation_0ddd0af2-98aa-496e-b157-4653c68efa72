const AWS = require('aws-sdk');
const cognito = new AWS.CognitoIdentityServiceProvider();

exports.handler = async (event) => {
  try {
    const body = JSON.parse(event.body);

    const { username, password } = body;

    const params = {
      AuthFlow: 'USER_PASSWORD_AUTH',
      ClientId: process.env.USER_POOL_CLIENT_ID,
      AuthParameters: {
        USERNAME: username,
        PASSWORD: password,
      },
    };

    const response = await cognito.initiateAuth(params).promise();

    if (response.ChallengeName === 'NEW_PASSWORD_REQUIRED') {
      const { newPassword } = body;
      if (!newPassword) {
        return {
          statusCode: 400,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Credentials': true
          },
          body: JSON.stringify({
            error: 'NEW_PASSWORD_REQUIRED challenge. Please provide newPassword in request body.',
            cognitoResponse: response
          }),
        };
      }
      const challengeResponse = await cognito.respondToAuthChallenge({
        ClientId: process.env.USER_POOL_CLIENT_ID,
        ChallengeName: 'NEW_PASSWORD_REQUIRED',
        Session: response.Session,
        ChallengeResponses: {
          USERNAME: username,
          NEW_PASSWORD: newPassword
        }
      }).promise();
      if (!challengeResponse.AuthenticationResult) {
        return {
          statusCode: 400,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Credentials': true
          },
          body: JSON.stringify({
            error: 'Failed to complete NEW_PASSWORD_REQUIRED challenge.',
            cognitoResponse: challengeResponse
          }),
        };
      }
      return {
        statusCode: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Credentials': true
        },
        body: JSON.stringify({
          accessToken: challengeResponse.AuthenticationResult.AccessToken,
          idToken: challengeResponse.AuthenticationResult.IdToken,
          refreshToken: challengeResponse.AuthenticationResult.RefreshToken,
          expiresIn: challengeResponse.AuthenticationResult.ExpiresIn,
          tokenType: challengeResponse.AuthenticationResult.TokenType
        }),
      };
    }

    if (!response.AuthenticationResult) {
      return {
        statusCode: 400,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Credentials': true
        },
        body: JSON.stringify({
          error: 'Authentication failed. No AuthenticationResult returned.',
          cognitoResponse: response
        }),
      };
    }

    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Credentials': true
      },
      body: JSON.stringify({
        accessToken: response.AuthenticationResult.AccessToken,
        idToken: response.AuthenticationResult.IdToken,
        refreshToken: response.AuthenticationResult.RefreshToken,
        expiresIn: response.AuthenticationResult.ExpiresIn,
        tokenType: response.AuthenticationResult.TokenType
      }),
    };
  } catch (error) {
    return {
      statusCode: 400,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Credentials': true
      },
      body: JSON.stringify({ error: error.message }),
    };
  }
};
