import React from 'react';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/Input';

export interface DatePickerProps {
  value?: Date | null;
  onChange: (date: Date | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  minDate?: Date;
  maxDate?: Date;
  className?: string;
  error?: boolean;
  showTimeSelect?: boolean;
}

export const DatePicker = ({
  value,
  onChange,
  placeholder = 'Select a date',
  disabled = false,
  minDate,
  maxDate,
  className,
  error = false,
  showTimeSelect = false,
}: DatePickerProps) => {
  const formatDateForInput = (date: Date | null | undefined): string => {
    if (!date) return '';
    return date.toISOString().slice(0, showTimeSelect ? 16 : 10);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    onChange(value ? new Date(value) : undefined);
  };

  return (
    <div className={cn('relative', className)}>
      <Input
        type={showTimeSelect ? 'datetime-local' : 'date'}
        value={formatDateForInput(value)}
        onChange={handleChange}
        min={minDate?.toISOString().slice(0, showTimeSelect ? 16 : 10)}
        max={maxDate?.toISOString().slice(0, showTimeSelect ? 16 : 10)}
        disabled={disabled}
        error={error}
        className="w-full"
        placeholder={placeholder}
      />
    </div>
  );
};

export default DatePicker;
