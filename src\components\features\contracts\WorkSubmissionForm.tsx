"use client";

import React, { useState, useRef } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Textarea } from "@/components/ui/Textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { useToast } from "@/components/ui/toast";
import { useAuth } from "@/lib/auth/AuthContext";
import {
  CreateWorkSubmissionDto,
  DeliverableStatus,
} from "@/types/features/contracts/contract.types";
import { CognitoUser } from "@/types/features/auth/auth.types";
import contractService from "@/api/contracts/contract.service";
import S3Service from "@/lib/s3/s3Service";
import { Icon } from "@/components/ui";

const workSubmissionSchema = yup.object({
  description: yup
    .string()
    .min(10, "Description must be at least 10 characters")
    .required("Description is required"),
  links: yup.array().of(yup.string().url("Must be a valid URL")).optional(),
});

type WorkSubmissionFormData = yup.InferType<typeof workSubmissionSchema>;

interface WorkSubmissionFormProps {
  contractId: string;
  onSubmissionSuccess?: () => void;
  className?: string;
}

export const WorkSubmissionForm: React.FC<WorkSubmissionFormProps> = ({
  contractId,
  onSubmissionSuccess,
  className = "",
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<
    Array<{ name: string; url: string; size: number }>
  >([]);
  const [links, setLinks] = useState<string[]>([""]);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { showToast } = useToast();
  const { user } = useAuth() as { user: CognitoUser | null };

  const form = useForm<WorkSubmissionFormData>({
    resolver: yupResolver(workSubmissionSchema as any),
    defaultValues: {
      description: "",
      links: [],
    },
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = form;

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    try {
      setIsUploading(true);
      const uploadPromises = Array.from(files).map(async (file) => {
        const fileName = `contracts/${contractId}/submissions/${Date.now()}-${
          file.name
        }`;
        const s3Key = await S3Service.uploadFile({
          file,
          fileName,
          contentType: file.type,
          level: "guest",
        });

        return {
          name: file.name,
          url: s3Key,
          size: file.size,
        };
      });

      const uploadedFileData = await Promise.all(uploadPromises);
      setUploadedFiles((prev) => [...prev, ...uploadedFileData]);

      showToast("Success", {
        description: `${uploadedFileData.length} file(s) uploaded successfully`,
        position: "top-right",
      });
    } catch (error) {
      console.error("Error uploading files:", error);
      showToast("Error", {
        description: "Failed to upload files. Please try again.",
        position: "top-right",
      });
    } finally {
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const removeFile = (index: number) => {
    setUploadedFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const addLinkField = () => {
    setLinks((prev) => [...prev, ""]);
  };

  const updateLink = (index: number, value: string) => {
    setLinks((prev) => prev.map((link, i) => (i === index ? value : link)));
  };

  const removeLink = (index: number) => {
    setLinks((prev) => prev.filter((_, i) => i !== index));
  };

  const onSubmit = async (data: WorkSubmissionFormData) => {
    try {
      setIsSubmitting(true);

      const validLinks = links.filter((link) => link.trim() !== "");

      if (!user) {
        showToast("Error", {
          description: "You must be logged in to submit work.",
          position: "top-right",
        });
        return;
      }

      const submissionData: CreateWorkSubmissionDto = {
        contractId,
        description: data.description,
        attachments: uploadedFiles.map((file) => file.url),
        links: validLinks.length > 0 ? validLinks : undefined,
        submittedAt: new Date().toISOString(),
        submittedById: user.attributes.sub || user.username,
        status: DeliverableStatus.SUBMITTED,
      };

      await contractService.submitWorkAndUpdateStatus(
        contractId,
        submissionData
      );

      showToast("Success", {
        description:
          "Work submitted successfully! The client will be notified.",
        position: "top-right",
      });

      reset();
      setUploadedFiles([]);
      setLinks([""]);

      onSubmissionSuccess?.();
    } catch (error) {
      console.error("Error submitting work:", error);
      showToast("Error", {
        description: "Failed to submit work. Please try again.",
        position: "top-right",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Icon name="Upload" className="h-5 w-5" />
          Submit Your Work
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Description */}
          <div>
            <label className="block text-sm font-medium mb-2">
              Work Description *
            </label>
            <Textarea
              {...register("description")}
              placeholder="Describe the work you've completed, any challenges faced, and how you've addressed the requirements..."
              className="min-h-[120px]"
              disabled={isSubmitting}
            />
            {errors.description && (
              <p className="text-sm text-red-600 mt-1">
                {errors.description.message}
              </p>
            )}
          </div>

          {/* File Upload */}
          <div>
            <label className="block text-sm font-medium mb-2">
              Attachments
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileUpload}
                multiple
                accept=".pdf,.doc,.docx,.xls,.xlsx,.txt,.zip,.rar,.jpg,.jpeg,.png,.gif"
                className="hidden"
                disabled={isSubmitting || isUploading}
              />
              <Button
                type="button"
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                disabled={isSubmitting || isUploading}
                className="mb-2"
              >
                <Icon name="Upload" className="mr-2 h-4 w-4" />
                {isUploading ? "Uploading..." : "Choose Files"}
              </Button>
              <p className="text-sm text-gray-500">
                Upload documents, images, or other files related to your work
              </p>
            </div>

            {/* Uploaded Files List */}
            {uploadedFiles.length > 0 && (
              <div className="mt-4 space-y-2">
                <h4 className="text-sm font-medium">Uploaded Files:</h4>
                {uploadedFiles.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 bg-gray-50 rounded"
                  >
                    <div className="flex items-center gap-2">
                      <Icon name="FileText" className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">{file.name}</span>
                      <span className="text-xs text-gray-500">
                        ({formatFileSize(file.size)})
                      </span>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(index)}
                      disabled={isSubmitting}
                    >
                      <Icon name="X" className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Links */}
          <div>
            <label className="block text-sm font-medium mb-2">
              Links (Optional)
            </label>
            <div className="space-y-2">
              {links.map((link, index) => (
                <div key={index} className="flex items-center gap-2">
                  <Icon
                    name="Link"
                    className="h-4 w-4 text-gray-500 flex-shrink-0"
                  />
                  <Input
                    type="url"
                    value={link}
                    onChange={(e) => updateLink(index, e.target.value)}
                    placeholder="https://example.com"
                    disabled={isSubmitting}
                    className="flex-1"
                  />
                  {links.length > 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeLink(index)}
                      disabled={isSubmitting}
                    >
                      <Icon name="Trash2" className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addLinkField}
                disabled={isSubmitting}
                className="mt-2"
              >
                <Icon name="Plus" className="mr-2 h-4 w-4" />
                Add Link
              </Button>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={isSubmitting || isUploading}
              className="bg-green-600 hover:bg-green-700"
            >
              {isSubmitting ? "Submitting..." : "Submit Work"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default WorkSubmissionForm;
