import { graphQLClient } from '@/lib/graphql/graphqlClient';
import { 
  GET_JOB_CATEGORY, 
  LIST_JOB_CATEGORIES, 
  LIST_SKILLS_BY_CATEGORY 
} from './job-category.queries';
import { 
  CREATE_JOB_CATEGORY, 
  UPDATE_JOB_CATEGORY, 
  DELETE_JOB_CATEGORY 
} from './job-category.mutations';
import type { 
  JobCategory, 
  CreateJobCategoryInput, 
  UpdateJobCategoryInput,
  DeleteJobCategoryInput,
  ModelJobCategoryFilterInput,
  JobCategoryConnection
} from '@/types/features/job-categories/job-category.types';
import type { Skill } from '@/types/features/skills/skill.types';

export const jobCategoryApi = {
  getJobCategory: async (id: string, includeSkills: boolean = false): Promise<JobCategory | null> => {
    const response = await graphQLClient.execute<{ getJobCategory: JobCategory | null }>(
      GET_JOB_CATEGORY,
      { 
        id,
        includeSkills
      },
      { authMode: 'userPool' }
    );
    return response.getJobCategory;
  },

  listSkillsByCategory: async (
    jobCategoryId: string,
    filter: any = {},
    limit?: number,
    nextToken?: string
  ): Promise<{ items: Skill[]; nextToken?: string }> => {
    const combinedFilter = {
      and: [
        { jobCategoryId: { eq: jobCategoryId } },
        ...(filter ? [filter] : [])
      ]
    };

    const response = await graphQLClient.execute<{ 
      listSkills: { 
        items: Skill[]; 
        nextToken?: string; 
      } 
    }>(
      LIST_SKILLS_BY_CATEGORY,
      { 
        filter: combinedFilter,
        limit,
        nextToken 
      },
      { authMode: 'userPool' }
    );
    return response.listSkills;
  },

  createJobCategory: async (input: CreateJobCategoryInput): Promise<JobCategory> => {
    const response = await graphQLClient.mutate<{ createJobCategory: JobCategory }>(
      CREATE_JOB_CATEGORY,
      { input },
      { authMode: 'userPool' }
    );
    return response.createJobCategory;
  },

  updateJobCategory: async (input: UpdateJobCategoryInput): Promise<JobCategory> => {
    const response = await graphQLClient.mutate<{ updateJobCategory: JobCategory }>(
      UPDATE_JOB_CATEGORY,
      { input },
      { authMode: 'userPool' }
    );
    return response.updateJobCategory;
  },

  deleteJobCategory: async (input: DeleteJobCategoryInput): Promise<void> => {
    await graphQLClient.mutate<{ deleteJobCategory: { id: string } }>(
      DELETE_JOB_CATEGORY,
      { input },
      { authMode: 'userPool' }
    );
  },

  listJobCategories: async (
    filter?: ModelJobCategoryFilterInput,
    limit?: number,
    nextToken?: string,
    includeSkills: boolean = false
  ): Promise<JobCategoryConnection> => {
    const response = await graphQLClient.execute<{ listJobCategories: JobCategoryConnection }>(
      LIST_JOB_CATEGORIES,
      { 
        filter, 
        limit, 
        nextToken,
        includeSkills 
      },
      { authMode: 'userPool' }
    );
    return response.listJobCategories;
  },

  searchJobCategories: async (searchTerm: string): Promise<JobCategory[]> => {
    const { items } = await jobCategoryApi.listJobCategories(undefined, 1000);
    const searchLower = searchTerm.toLowerCase();
    return items.filter(
      (category) =>
        category.name.toLowerCase().includes(searchLower) ||
        (category.description?.toLowerCase().includes(searchLower) ?? false)
    );
  },

  transformFilter: (filter: ModelJobCategoryFilterInput): Record<string, unknown> => {
    const transformed: Record<string, unknown> = {};

    if (filter.id) {
      transformed.id = filter.id;
    }

    if (filter.name) {
      transformed.name = filter.name;
    }

    if (filter.isActive !== undefined) {
      transformed.isActive = { eq: filter.isActive };
    }

    if (filter.and) {
      transformed.and = filter.and.map(jobCategoryApi.transformFilter);
    }

    if (filter.or) {
      transformed.or = filter.or.map(jobCategoryApi.transformFilter);
    }

    if (filter.not) {
      transformed.not = jobCategoryApi.transformFilter(filter.not);
    }

    return transformed;
  },
};

export default jobCategoryApi;
