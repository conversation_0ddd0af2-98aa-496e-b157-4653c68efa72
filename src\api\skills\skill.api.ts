import { graphQLClient } from '../../lib/graphql/graphqlClient';
import {
  Skill,
  CreateSkillInput,
  UpdateSkillInput,
  SkillFilter
} from '@/types/features/skills/skill.types';
import {
  CREATE_SKILL,
  UPDATE_SKILL,
  DELETE_SKILL
} from './skill.mutations';
import {
  GET_SKILL,
  LIST_SKILLS
} from './skill.queries';


export const skillApi = {
  async createSkill(input: CreateSkillInput): Promise<Skill | null > {
    const response = await graphQLClient.execute<{ createSkill: Skill | null }>(
      CREATE_SKILL,
      { input }
    );
    return response.createSkill;
  },

  async getSkill(id: string): Promise<Skill | null > {
    const response = await graphQLClient.execute<{ getSkill: Skill | null }>(
      GET_SKILL,
      { id }
    );
    return response.getSkill;
  },

  async updateSkill(input: UpdateSkillInput): Promise<Skill | null > {
    const response = await graphQLClient.execute<{ updateSkill: Skill | null }>(
      UPDATE_SKILL,
      { input }
    );
    return response.updateSkill;
  },

  async deleteSkill(id: string): Promise<void> {
    await graphQLClient.execute(
      DELETE_SKILL,
      { input: { id } }
    );
  },

  async listSkills(filter: SkillFilter = {}): Promise<{ items: Skill[]; nextToken?: string | undefined }> {
    const transformedFilter: any = {};

    if (filter.jobCategoryId) {
      transformedFilter.jobCategoryId = { eq: filter.jobCategoryId };
    }

    if (filter.isActive !== undefined) {
      transformedFilter.isActive = { eq: filter.isActive };
    }

    if (filter.searchTerm) {
      transformedFilter.or = [
        { name: { contains: filter.searchTerm } },
        { description: { contains: filter.searchTerm } }
      ];
    }

    const response = await graphQLClient.execute<{ listSkills: { items: Skill[]; nextToken?: string | undefined } }>(
      LIST_SKILLS,
      {
        filter: Object.keys(transformedFilter).length > 0 ? transformedFilter : undefined,
        limit: filter.limit || 50,
        nextToken: filter.nextToken
      }
    );

    return response.listSkills || { items: [], nextToken: undefined };;
  }
};