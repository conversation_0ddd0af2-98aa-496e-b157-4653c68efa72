import { gql } from '@apollo/client';

export const GET_SKILL = gql`
  query GetSkill($id: ID!) {
    getSkill(id: $id) {
      id
      name
      description
      isActive
      jobCategoryId
      createdAt
      updatedAt
    }
  }
`;

export const LIST_SKILLS = gql`
  query ListSkills($filter: ModelSkillFilterInput, $limit: Int, $nextToken: String) {
    listSkills(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        name
        description
        isActive
        jobCategoryId
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;