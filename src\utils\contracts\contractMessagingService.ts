import { UserRole } from '@/types/features/auth/auth.types';
import { ExtendedContract } from '@/types/features/contracts/contract.types';
import messageService from '@/api/messaging/message.service';

/**
 * Utility class for handling contract-based messaging functionality
 */
export class ContractMessagingService {
  private static instance: ContractMessagingService;

  private constructor() { }

  public static getInstance(): ContractMessagingService {
    if (!ContractMessagingService.instance) {
      ContractMessagingService.instance = new ContractMessagingService();
    }
    return ContractMessagingService.instance;
  }

  /**
   * Determines the messaging recipient for a contract
   */
  getContractMessagingRecipient(
    contract: ExtendedContract,
    currentUserId: string,
    currentUserRole: UserRole
  ): {
    recipientId: string;
    recipientData?: { id: string; name: string; email?: string; avatar?: string };
    isValidParty: boolean;
  } | null {
    const isClient = currentUserRole === UserRole.CLIENT && currentUserId === contract.clientId;
    const isFreelancer = currentUserRole === UserRole.FREELANCER && currentUserId === contract.freelancerId;

    if (!isClient && !isFreelancer) {
      return null;
    }

    const recipientId = isClient ? contract.freelancerId : contract.clientId;
    const recipientData = isClient ? contract.freelancer : contract.client;

    if (!recipientId) {
      return null;
    }

    return {
      recipientId,
      recipientData,
      isValidParty: true,
    };
  }

  /**
   * Creates or finds a conversation for a contract
   */
  async createOrFindContractConversation(
    contract: ExtendedContract,
    currentUserId: string,
    currentUserRole: UserRole
  ): Promise<string> {
    const recipient = this.getContractMessagingRecipient(contract, currentUserId, currentUserRole);

    if (!recipient) {
      throw new Error('You are not a party to this contract');
    }

    if (contract.jobId) {
      try {
        return await messageService.createConversation(
          contract.jobId,
          contract.clientId,
          contract.freelancerId
        );
      } catch (error) {
        console.error('Failed to create/find conversation:', error);
        throw new Error('Failed to create conversation for this contract');
      }
    }

    throw new Error('Contract does not have an associated job');
  }

  /**
   * Generates message navigation URL with proper parameters
   */
  generateContractMessageUrl(
    contract: ExtendedContract,
    currentUserId: string,
    currentUserRole: UserRole,
    conversationId?: string
  ): string {
    const recipient = this.getContractMessagingRecipient(contract, currentUserId, currentUserRole);

    if (!recipient) {
      throw new Error('Cannot generate message URL: user is not a party to this contract');
    }

    const params = new URLSearchParams();

    if (conversationId) {
      params.set('conversationId', conversationId);
    } else {
      params.set('recipientId', recipient.recipientId);

      if (contract.jobId) {
        params.set('jobId', contract.jobId);
      }

      if (recipient.recipientData?.name) {
        params.set('recipientName', recipient.recipientData.name);
      }
    }

    params.set('contractId', contract.id);
    params.set('contractTitle', contract.title);

    return `/messages?${params.toString()}`;
  }

  /**
   * Send an initial contract-related message
   */
  async sendContractMessage(
    contract: ExtendedContract,
    currentUserId: string,
    currentUserRole: UserRole,
    message: string
  ): Promise<void> {
    const conversationId = await this.createOrFindContractConversation(
      contract,
      currentUserId,
      currentUserRole
    );

    const recipient = this.getContractMessagingRecipient(contract, currentUserId, currentUserRole);

    if (!recipient) {
      throw new Error('Cannot send message: user is not a party to this contract');
    }

    await messageService.sendMessage(
      conversationId,
      currentUserId,
      recipient.recipientId,
      message
    );
  }

  /**
   * Complete messaging flow for a contract - create conversation and navigate
   */
  async initiateContractMessaging(
    contract: ExtendedContract,
    currentUserId: string,
    currentUserRole: UserRole
  ): Promise<string> {
    try {
      const conversationId = await this.createOrFindContractConversation(
        contract,
        currentUserId,
        currentUserRole
      );

      return this.generateContractMessageUrl(contract, currentUserId, currentUserRole, conversationId);
    } catch (error) {
      console.warn('Failed to create/find conversation, using fallback:', error);
      return this.generateContractMessageUrl(contract, currentUserId, currentUserRole);
    }
  }
}

export const contractMessagingService = ContractMessagingService.getInstance();