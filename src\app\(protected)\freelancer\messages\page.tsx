"use client";

import { useAuth } from "@/lib/auth/AuthContext";
import { useCallback, useEffect, useState } from "react";
import { toast } from "@/components/ui/toast";
import messageService from "@/api/messaging/message.service";
import { MessagingPage } from "@/components/features/messaging/MessagingPage";
import {
  MessagingUser,
  Conversation as UIConversation,
  UIMessage,
} from "@/types/features/messaging/messaging.types";
import { UserRole } from "@/types";

export default function FreelancerMessagesPage() {
  const { user, cognitoUserId } = useAuth();
  const isAuthLoading = !user;
  const [conversations, setConversations] = useState<UIConversation[]>([]);

  const fetchConversations = useCallback(async () => {
    if (!cognitoUserId) return;

    try {
      const data = await messageService.listMyConversations(cognitoUserId);
      setConversations(data);
    } catch (error) {
      console.error("Error fetching conversations:", error);
      toast.error("Failed to load conversations");
    }
  }, [cognitoUserId]);

  useEffect(() => {
    fetchConversations();
  }, [fetchConversations]);

  const handleSendMessage = async (
    conversationId: string,
    content: string
  ): Promise<void> => {
    if (!cognitoUserId) {
      toast.error("User not authenticated");
      return;
    }

    try {
      const conversation = conversations.find((c) => c.id === conversationId);
      if (!conversation) throw new Error("Conversation not found");

      const receiverId =
        conversation.clientId === cognitoUserId
          ? conversation.freelancerId
          : conversation.clientId;

      if (!receiverId) {
        toast.error("Unable to determine message receiver");
        return;
      }

      await messageService.sendMessage(
        conversationId,
        cognitoUserId,
        receiverId,
        content
      );

      await fetchConversations();
    } catch (error) {
      console.error("Error sending message:", error);
      toast.error("Failed to send message");
      throw error;
    }
  };

  const handleLoadMoreMessages = async (
    conversationId: string,
    before: Date
  ): Promise<UIMessage[]> => {
    if (!cognitoUserId) return [];

    try {
      const conversations = await messageService.listMyConversations(
        cognitoUserId
      );
      const conversation = conversations.find(
        (conv) => conv.id === conversationId
      );

      if (!conversation) {
        throw new Error("Conversation not found");
      }

      const filteredMessages = (conversation.messages || []).filter(
        (msg: any) => new Date(msg.createdAt) < before
      );

      return filteredMessages.map((msg: any) => ({
        id: msg.id,
        content: "messageText" in msg ? msg.messageText : msg.content || "",
        senderId: msg.senderId,
        receiverId: msg.receiverId || "",
        conversationId: msg.conversationId || conversationId,
        createdAt: msg.createdAt,
        updatedAt: msg.updatedAt || msg.createdAt,
        status: "delivered" as const,
        type: (msg.type === "image"
          ? "image"
          : msg.type === "file"
          ? "file"
          : "text") as "text" | "file" | "image",
        sender: {
          id: msg.senderId,
          name: msg.sender?.name || "Unknown",
          email: msg.sender?.email || "",
          role: (msg.sender as any)?.role || "FREELANCER",
          avatar: msg.sender?.avatar,
          isOnline: (msg.sender as any)?.isOnline || false,
        },
        receiver: {
          id: msg.receiverId || "",
          name: msg.receiver?.name || "Unknown",
          email: msg.receiver?.email || "",
          role: (msg.receiver as any)?.role || "CLIENT",
          avatar: msg.receiver?.avatar,
          isOnline: (msg.receiver as any)?.isOnline || false,
        },
        isOwn: msg.senderId === cognitoUserId,
        fileInfo: msg.fileInfo
          ? {
              name: msg.fileInfo.name || "file",
              type: msg.fileInfo.type || "application/octet-stream",
              size: msg.fileInfo.size || 0,
              url: msg.fileInfo.url || "",
            }
          : undefined,
      }));
    } catch (error) {
      console.error("Error loading more messages:", error);
      toast.error("Failed to load messages");
      return [];
    }
  };

  if (isAuthLoading || !user || !cognitoUserId) {
    return (
      <div className="h-full flex items-center justify-center">
        <p>Loading user data...</p>
      </div>
    );
  }

  const userAttributes = user.attributes || {};
  const currentUser: MessagingUser = {
    id: cognitoUserId,
    name:
      typeof userAttributes.name === "string"
        ? userAttributes.name
        : "Freelancer",
    email: typeof userAttributes.email === "string" ? userAttributes.email : "",
    avatar: Array.isArray(userAttributes.picture)
      ? userAttributes.picture[0]
      : typeof userAttributes.picture === "string"
      ? userAttributes.picture
      : "/default-avatar.png",
    role: UserRole.FREELANCER,
    isOnline: true,
  };

  return (
    <div className="h-[calc(100vh-8rem)]">
      <MessagingPage
        currentUser={currentUser}
        initialConversations={conversations}
        onSendMessage={handleSendMessage}
        onLoadMoreMessages={handleLoadMoreMessages}
        className="h-full"
        isSending={false}
      />
    </div>
  );
}
