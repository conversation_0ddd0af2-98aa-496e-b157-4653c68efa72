import { gql } from '@apollo/client';
import { graphQLClient } from '../../lib/graphql/graphqlClient';
import { createError, ErrorCode, isAppError } from '../../utils/errorHandler';
import type { JobProposal, CreateJobProposalInput, ProposalStatus } from '../../types/features/proposals/proposal.types';

const validateProposalInput = (input: CreateJobProposalInput): void => {
  const details: Record<string, string[]> = {};
  
  if (!input.jobId) {
    details.jobId = ['Job ID is required'];
  }

  if (!input.coverLetter?.trim()) {
    details.coverLetter = ['Cover letter is required'];
  }

  if (typeof input.bidAmount !== 'number' || input.bidAmount <= 0) {
    details.bidAmount = ['Bid amount must be a positive number'];
  }

  if (Object.keys(details).length > 0) {
    throw createError(
      'Validation failed',
      ErrorCode.VALIDATION_ERROR,
      { 
        statusCode: 400,
        details: { validation: details }
      }
    );
  }
};

const validateProposalId = (id: string, operation: string): void => {
  if (!id) {
    throw createError(
      `Proposal ID is required for ${operation}`,
      ErrorCode.VALIDATION_ERROR,
      { 
        statusCode: 400,
        details: { 
          validation: {
            id: [`Proposal ID is required for ${operation}`]
          }
        }
      }
    );
  }
};

const LIST_MY_PROPOSALS = gql`
  query ListMyProposals($freelancerId: ID!) {
    listProposals(filter: { freelancerId: { eq: $freelancerId } }) {
      items {
        id
        jobId
        freelancerId
        coverLetter
        bidAmount
        proposedRate
        status
        job {
          id
          title
          description
          budget
          category
          deadline
          status
          client {
            id
            name
            email
          }
        }
        createdAt
        updatedAt
      }
    }
  }
`;

const SUBMIT_PROPOSAL = gql`
  mutation CreateProposal($input: CreateJobProposalInput!) {
    createJobProposal(input: $input) {
      id
      jobId
      freelancerId
      coverLetter
      bidAmount
      proposedRate
      status
      createdAt
      updatedAt
    }
  }
`;

const UPDATE_PROPOSAL = gql`
  mutation UpdateProposal($input: UpdateJobProposalInput!) {
    updateJobProposal(input: $input) {
      id
      jobId
      freelancerId
      coverLetter
      bidAmount
      proposedRate
      status
      createdAt
      updatedAt
    }
  }
`;

const UPDATE_PROPOSAL_STATUS = gql`
  mutation UpdateProposalStatus($input: UpdateJobProposalInput!) {
    updateJobProposal(input: $input) {
      id
      status
      updatedAt
    }
  }
`;

const GET_JOB_PROPOSALS = gql`
  query GetJobProposals($jobId: ID!) {
    getJob(id: $jobId) {
      proposals {
        items {
          id
          jobId
          freelancerId
          coverLetter
          bidAmount
          proposedRate
          status
          freelancer {
            id
            name
            email
            profile {
              title
              skills
              experience
            }
          }
          createdAt
          updatedAt
        }
      }
    }
  }
`;

const HAS_SUBMITTED_PROPOSAL = gql`
  query HasSubmittedProposal($jobId: ID!, $freelancerId: ID!) {
    hasSubmittedProposal(jobId: $jobId, freelancerId: $freelancerId)
  }
`;

const GET_PROPOSAL = gql`
  query GetProposal($id: ID!) {
    getProposal(id: $id) {
      id
      jobId
      freelancerId
      coverLetter
      bidAmount
      proposedRate
      status
      createdAt
      updatedAt
      job {
        id
        title
        description
        budget
        category
        deadline
        status
        isRemote
        client {
          id
          name
          email
        }
      }
      freelancer {
        id
        name
        email
        profilePhoto
        bio
        skills
      }
    }
  }
`;

export const proposalService = {
  /**
   * List all proposals for a specific freelancer
   * @param freelancerId - The ID of the freelancer
   * @returns Promise<JobProposal[]> - Array of proposals
   * @throws {AppError} If freelancer ID is invalid or an error occurs
   */
  async listMyProposals(freelancerId: string): Promise<JobProposal[]> {
    try {
      if (!freelancerId) {
        throw createError(
          'Freelancer ID is required',
          ErrorCode.VALIDATION_ERROR,
          { 
            statusCode: 400,
            details: { 
              validation: {
                freelancerId: ['Freelancer ID is required']
              }
            }
          }
        );
      }

      const response = await graphQLClient.execute<{ 
        listProposals: { 
          items: JobProposal[];
          nextToken?: string;
        } 
      }>(
        LIST_MY_PROPOSALS,
        { freelancerId },
        { authMode: 'userPool' }
      );
      
      if (!response?.listProposals?.items) {
        throw createError(
          'Failed to fetch proposals',
          ErrorCode.INTERNAL_SERVER_ERROR,
          { statusCode: 500 }
        );
      }
      
      return response.listProposals.items;
    } catch (error) {
      if (isAppError(error)) {
        throw error;
      }
      throw createError(
        'Failed to fetch proposals',
        ErrorCode.INTERNAL_SERVER_ERROR,
        { 
          statusCode: 500,
          originalError: error as Error,
          details: { operation: 'listMyProposals' }
        }
      );
    }
  },

  /**
   * Submit a new job proposal
   * @param input - Proposal details
   * @returns Promise<JobProposal> - The created proposal
   * @throws {AppError} If input validation fails or submission fails
   */
  async submitProposal(input: CreateJobProposalInput): Promise<JobProposal> {
    try {
      validateProposalInput(input);

      const response = await graphQLClient.execute<{ createJobProposal: JobProposal }>(
        SUBMIT_PROPOSAL,
        { input },
        { authMode: 'userPool' }
      );

      if (!response?.createJobProposal) {
        throw createError(
          'Failed to submit proposal',
          ErrorCode.INTERNAL_SERVER_ERROR,
          { statusCode: 500 }
        );
      }

      return response.createJobProposal;
    } catch (error) {
      if (isAppError(error)) {
        throw error;
      }
      
      if (error instanceof Error && 'graphQLErrors' in error) {
        const gqlError = error as { graphQLErrors: Array<{ message: string; extensions?: { code?: string } }> };
        const errorMessage = gqlError.graphQLErrors[0]?.message || 'Failed to submit proposal';
        const errorCode = gqlError.graphQLErrors[0]?.extensions?.code as ErrorCode || ErrorCode.INTERNAL_SERVER_ERROR;
        
        throw createError(
          errorMessage,
          errorCode,
          { 
            statusCode: errorCode === ErrorCode.VALIDATION_ERROR ? 400 : 500,
            originalError: error,
            details: { operation: 'submitProposal' }
          }
        );
      }

      throw createError(
        'Failed to submit proposal',
        ErrorCode.INTERNAL_SERVER_ERROR,
        { 
          statusCode: 500,
          originalError: error as Error,
          details: { operation: 'submitProposal' }
        }
      );
    }
  },

  /**
   * Check if a freelancer has already submitted a proposal for a specific job
   * @param jobId - The ID of the job
   * @param freelancerId - The ID of the freelancer
   * @param job - Optional job object that might contain proposals
   * @returns Promise<boolean> - True if the freelancer has submitted a proposal
   * @throws {AppError} If an error occurs during the check
   */
  async hasSubmittedProposal(
    jobId: string,
    freelancerId: string,
    job?: {
      proposals?: {
        items: Array<{ freelancerId: string }>;
      };
    }
  ): Promise<boolean> {
    try {
      if (!jobId) {
        throw createError(
          'Job ID is required',
          ErrorCode.VALIDATION_ERROR,
          { statusCode: 400 }
        );
      }

      if (!freelancerId) {
        throw createError(
          'Freelancer ID is required',
          ErrorCode.VALIDATION_ERROR,
          { statusCode: 400 }
        );
      }

      if (job?.proposals?.items) {
        return job.proposals.items.some(
          (proposal) => proposal.freelancerId === freelancerId
        );
      }

      const response = await graphQLClient.execute<{ hasSubmittedProposal: boolean }>(
        HAS_SUBMITTED_PROPOSAL,
        { 
          jobId: jobId,
          freelancerId: freelancerId 
        },
        { authMode: 'userPool' }
      );
      
      if (typeof response?.hasSubmittedProposal !== 'boolean') {
        throw createError(
          'Invalid response when checking proposal submission',
          ErrorCode.VALIDATION_ERROR,
          { statusCode: 500 }
        );
      }
      
      return response.hasSubmittedProposal;
    } catch (error) {
      if (isAppError(error)) {
        throw error;
      }
      
      if (error instanceof Error && 'graphQLErrors' in error) {
        const gqlError = error as { graphQLErrors: Array<{ message: string; extensions?: { code?: string } }> };
        const errorMessage = gqlError.graphQLErrors[0]?.message || 'Failed to check proposal submission';
        const errorCode = gqlError.graphQLErrors[0]?.extensions?.code as ErrorCode || ErrorCode.UNKNOWN_ERROR;
        
        throw createError(
          errorMessage,
          errorCode,
          { 
            statusCode: errorCode === ErrorCode.VALIDATION_ERROR ? 400 : 500,
            originalError: error,
            details: { operation: 'hasSubmittedProposal' }
          }
        );
      }

      throw createError(
        'Failed to check proposal submission',
        ErrorCode.UNKNOWN_ERROR,
        { 
          statusCode: 500,
          originalError: error as Error,
          details: { operation: 'hasSubmittedProposal' }
        }
      );
    }
  },

  /**
   * Update proposal status
   * @param id - The ID of the proposal to update
   * @param status - The new status for the proposal
   * @returns Promise<JobProposal> - The updated proposal
   * @throws {AppError} If the update fails
   */
  async updateProposalStatus(
    id: string,
    status: ProposalStatus
  ): Promise<JobProposal> {
    try {
      validateProposalId(id, 'update proposal status');
      
      if (!status) {
        throw createError(
          'Status is required',
          ErrorCode.VALIDATION_ERROR,
          { 
            statusCode: 400,
            details: { 
              validation: {
                status: ['Status is required']
              }
            }
          }
        );
      }

      const response = await graphQLClient.execute<{ 
        updateJobProposal: JobProposal 
      }>(
        UPDATE_PROPOSAL_STATUS,
        { input: { id, status } },
        { authMode: 'userPool' }
      );

      if (!response?.updateJobProposal) {
        throw createError(
          'Failed to update proposal status',
          ErrorCode.INTERNAL_SERVER_ERROR,
          { statusCode: 500 }
        );
      }

      return response.updateJobProposal;
    } catch (error) {
      if (isAppError(error)) {
        throw error;
      }
      
      if (error instanceof Error && 'graphQLErrors' in error) {
        const gqlError = error as { graphQLErrors: Array<{ message: string; extensions?: { code?: string } }> };
        const errorMessage = gqlError.graphQLErrors[0]?.message || 'Failed to update proposal status';
        const errorCode = gqlError.graphQLErrors[0]?.extensions?.code as ErrorCode || ErrorCode.INTERNAL_SERVER_ERROR;
        
        throw createError(
          errorMessage,
          errorCode,
          { 
            statusCode: errorCode === ErrorCode.VALIDATION_ERROR ? 400 : 500,
            originalError: error,
            details: { operation: 'updateProposalStatus' }
          }
        );
      }

      throw createError(
        'Failed to update proposal status',
        ErrorCode.INTERNAL_SERVER_ERROR,
        { 
          statusCode: 500,
          originalError: error as Error,
          details: { operation: 'updateProposalStatus' }
        }
      );
    }
  },

  /**
   * Update a proposal's details
   * @param id - The ID of the proposal to update
   * @param input - The updated proposal data
   * @returns Promise<JobProposal> - The updated proposal
   */
  async updateProposal(
    id: string,
    input: { coverLetter: string; bidAmount: number }
  ): Promise<JobProposal> {
    try {
      validateProposalId(id, 'update proposal');
      
      if (!input.coverLetter?.trim()) {
        throw createError(
          'Cover letter is required',
          ErrorCode.VALIDATION_ERROR,
          { statusCode: 400 }
        );
      }

      if (typeof input.bidAmount !== 'number' || input.bidAmount <= 0) {
        throw createError(
          'Bid amount must be a positive number',
          ErrorCode.VALIDATION_ERROR,
          { statusCode: 400 }
        );
      }

      const response = await graphQLClient.execute<{ updateJobProposal: JobProposal }>(
        UPDATE_PROPOSAL,
        {
          input: {
            id,
            coverLetter: input.coverLetter,
            bidAmount: input.bidAmount
          }
        },
        { authMode: 'userPool' }
      );

      if (!response?.updateJobProposal) {
        throw createError(
          'Failed to update proposal',
          ErrorCode.INTERNAL_SERVER_ERROR,
          { statusCode: 500 }
        );
      }

      return response.updateJobProposal;
    } catch (error) {
      if (isAppError(error)) {
        throw error;
      }
      
      if (error instanceof Error && 'graphQLErrors' in error) {
        const gqlError = error as { graphQLErrors: Array<{ message: string; extensions?: { code?: string } }> };
        const errorMessage = gqlError.graphQLErrors[0]?.message || 'Failed to update proposal';
        const errorCode = gqlError.graphQLErrors[0]?.extensions?.code as ErrorCode || ErrorCode.INTERNAL_SERVER_ERROR;
        
        throw createError(
          errorMessage,
          errorCode,
          { 
            statusCode: errorCode === ErrorCode.VALIDATION_ERROR ? 400 : 500,
            originalError: error,
            details: { operation: 'updateProposal' }
          }
        );
      }

      throw createError(
        'Failed to update proposal',
        ErrorCode.UNKNOWN_ERROR,
        { 
          statusCode: 500,
          originalError: error as Error,
          details: { operation: 'updateProposal' }
        }
      );
    }
  },

  /**
   * Get all proposals for a specific job
   * @param jobId - The ID of the job
   * @returns Promise<JobProposal[]> - Array of proposals for the job
   */
  async getJobProposals(jobId: string): Promise<JobProposal[]> {
    try {
      if (!jobId) {
        throw createError(
          'Job ID is required',
          ErrorCode.VALIDATION_ERROR,
          { statusCode: 400 }
        );
      }

      const response = await graphQLClient.execute<{ getJob: { proposals: { items: JobProposal[] } } }>(
        GET_JOB_PROPOSALS,
        { jobId },
        { authMode: 'userPool' }
      );
      
      if (!response?.getJob?.proposals?.items) {
        throw createError(
          'Failed to fetch job proposals',
          ErrorCode.INTERNAL_SERVER_ERROR,
          { statusCode: 500 }
        );
      }
      
      return response.getJob.proposals.items;
    } catch (error) {
      if (isAppError(error)) {
        throw error;
      }
      
      if (error instanceof Error && 'graphQLErrors' in error) {
        const gqlError = error as { graphQLErrors: Array<{ message: string; extensions?: { code?: string } }> };
        const errorMessage = gqlError.graphQLErrors[0]?.message || 'Failed to fetch job proposals';
        const errorCode = gqlError.graphQLErrors[0]?.extensions?.code as ErrorCode || ErrorCode.INTERNAL_SERVER_ERROR;
        
        throw createError(
          errorMessage,
          errorCode,
          { 
            statusCode: errorCode === ErrorCode.VALIDATION_ERROR ? 400 : 500,
            originalError: error,
            details: { operation: 'getJobProposals' }
          }
        );
      }

      throw createError(
        'Failed to fetch job proposals',
        ErrorCode.UNKNOWN_ERROR,
        { 
          statusCode: 500,
          originalError: error as Error,
          details: { operation: 'getJobProposals' }
        }
      );
    }
  },

  /**
   * Get a single proposal by ID
   * @param id - The ID of the proposal to fetch
   * @returns Promise<JobProposal | null> - The proposal if found, null otherwise
   * @throws {AppError} If the operation fails
   */
  async getProposalById(id: string): Promise<JobProposal | null> {
    try {
      validateProposalId(id, 'get proposal by ID');

      const response = await graphQLClient.execute<{ getProposal: JobProposal | null }>(
        GET_PROPOSAL,
        { id },
        { authMode: 'userPool' }
      );
      
      if (!response?.getProposal) {
        return null;
      }
      
      return response.getProposal;
    } catch (error) {
      if (isAppError(error)) {
        throw error;
      }
      
      if (error instanceof Error && 'graphQLErrors' in error) {
        const gqlError = error as { graphQLErrors: Array<{ message: string; extensions?: { code?: string } }> };
        const errorMessage = gqlError.graphQLErrors[0]?.message || 'Failed to fetch proposal';
        const errorCode = gqlError.graphQLErrors[0]?.extensions?.code as ErrorCode || ErrorCode.INTERNAL_SERVER_ERROR;
        
        throw createError(
          errorMessage,
          errorCode,
          { 
            statusCode: errorCode === ErrorCode.NOT_FOUND ? 404 : 500,
            originalError: error,
            details: { operation: 'getProposalById' }
          }
        );
      }

      throw createError(
        'Failed to fetch proposal',
        ErrorCode.UNKNOWN_ERROR,
        { 
          statusCode: 500,
          originalError: error as Error,
          details: { operation: 'getProposalById' }
        }
      );
    }
  },
};

export default proposalService;
