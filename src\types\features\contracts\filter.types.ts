import { ContractStatus } from './contract.types';

/**
 * Filter conditions for querying contracts
 * This is used for GraphQL filter input
 */
export interface ContractFilterCondition {
  eq?: string | number | boolean | ContractStatus;
  ne?: string | number | boolean | ContractStatus;
  le?: number | string;
  lt?: number | string;
  ge?: number | string;
  gt?: number | string;
  contains?: string;
  notContains?: string;
  between?: [number, number] | [string, string];
  in?: (string | number)[];
  notIn?: (string | number)[];
  attributeExists?: boolean;
  attributeType?: 'string' | 'number' | 'boolean' | 'list' | 'map' | 'null';
  size?: { size: number };
  and?: ContractFilterCondition[];
  or?: ContractFilterCondition[];
  not?: ContractFilterCondition;
}

/**
 * Date range filter for contract queries
 */
export interface DateRangeFilter {
  startDate?: string;
  endDate?: string;
  from?: string;
  to?: string;
}

/**
 * Transformed contract filters for GraphQL queries
 */
export interface TransformedContractFilters {
  id?: ContractFilterCondition;
  clientId?: ContractFilterCondition;
  freelancerId?: ContractFilterCondition;
  jobId?: ContractFilterCondition;
  status?: ContractFilterCondition;
  startDate?: ContractFilterCondition;
  endDate?: ContractFilterCondition;
  createdAt?: ContractFilterCondition;
  updatedAt?: ContractFilterCondition;
  title?: ContractFilterCondition;
  and?: ContractFilterCondition[];
  or?: ContractFilterCondition[];
}

/**
 * Input filters for contract listings
 */
export interface ContractFilters {
  id?: string;
  clientId?: string;
  freelancerId?: string;
  jobId?: string;
  status?: ContractStatus | ContractStatus[];
  dateRange?: DateRangeFilter;
  startDate?: string | DateRangeFilter;
  endDate?: string | DateRangeFilter;
  searchTerm?: string;
  limit?: number;
  nextToken?: string;
}
