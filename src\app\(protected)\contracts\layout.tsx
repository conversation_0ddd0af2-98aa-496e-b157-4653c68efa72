'use client';

import { useAuth } from '@/lib/auth/AuthContext';
import { useEffect, useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Loading } from '@/components/ui';
import { ErrorBoundary } from '@/components/ErrorBoundary';

export default function ContractsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { loading, isInitialized } = useAuth();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted || loading || !isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Loading size="lg" />
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <DashboardLayout
        title="Contracts"
        description="Manage your contracts and agreements"
      >
        {children}
      </DashboardLayout>
    </ErrorBoundary>
  );
}
