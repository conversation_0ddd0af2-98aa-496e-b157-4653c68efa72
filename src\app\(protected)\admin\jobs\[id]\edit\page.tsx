"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";
import { jobService } from "@/api/jobs/job.service";
import {
  UpdateJobInput,
  Job,
  JobStatus,
  JobType,
} from "@/types/features/jobs/job.types";
import { Icon } from "@/components/ui/Icon";
import { ContentHeader } from "@/components/layout/ContentHeader";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Textarea } from "@/components/ui/Textarea";
import { useToast } from "@/components/ui/toast";
import { jobCategoryService } from "@/api/job-categories/job-category.service";
import { JobCategory } from "@/types/features/job-categories/job-category.types";

const jobStatuses = [
  { value: JobStatus.OPEN, label: "Open" },
  { value: JobStatus.IN_PROGRESS, label: "In Progress" },
  { value: JobStatus.COMPLETED, label: "Completed" },
  { value: JobStatus.CANCELLED, label: "Cancelled" },
];

const jobTypes = [
  { value: JobType.FIXED_PRICE, label: "Fixed Price" },
  { value: JobType.HOURLY, label: "Hourly" },
];

export default function EditJobPage() {
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const router = useRouter();
  const params = useParams();
  const { showToast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [job, setJob] = useState<Job | null>(null);
  const [jobCategories, setJobCategories] = useState<JobCategory[]>([]);

  const [formData, setFormData] = useState<UpdateJobInput>({
    id: params.id as string,
    title: "",
    description: "",
    budget: 0,
    type: JobType.FIXED_PRICE,
    status: JobStatus.OPEN,
    skills: [],
    category: "",
    deadline: "",
    isRemote: false,
  });

  useEffect(() => {
    const loadData = async () => {
      if (!isAuthenticated || !params.id) return;

      try {
        setIsLoading(true);

        const [jobData, categoriesResponse] = await Promise.all([
          jobService.getJob(params.id as string),
          jobCategoryService.listJobCategories(undefined, 100),
        ]);

        setJob(jobData);
        setJobCategories(categoriesResponse.items || []);

        setFormData({
          id: jobData.id,
          title: jobData.title,
          description: jobData.description,
          budget: jobData.budget,
          type: jobData.type || JobType.FIXED_PRICE,
          status: jobData.status,
          skills: jobData.skills || [],
          category: jobData.category,
          deadline: jobData.deadline || "",
          isRemote: jobData.isRemote || false,
        });
      } catch (err) {
        console.error("Error loading job:", err);
        setError("Failed to load job data.");
        showToast("Error", {
          description: "Failed to load job data. Please try again.",
          position: "top-right",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [isAuthenticated, params.id, showToast]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsSubmitting(true);
      setError(null);

      if (!formData.title || !formData.title.trim()) {
        setError("Job title is required.");
        return;
      }

      if (!formData.description || !formData.description.trim()) {
        setError("Job description is required.");
        return;
      }

      if (!formData.budget || formData.budget <= 0) {
        setError("Job budget must be greater than 0.");
        return;
      }

      await jobService.updateJob(formData);

      showToast("Success", {
        description: "Job updated successfully",
        position: "top-right",
      });

      router.push("/admin/jobs");
    } catch (err) {
      console.error("Error updating job:", err);
      const errorMessage =
        err instanceof Error
          ? err.message
          : "Failed to update job. Please try again.";
      setError(errorMessage);
      showToast("Error", {
        description: errorMessage,
        position: "top-right",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    router.push("/admin/jobs");
  };

  const handleSkillsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const skillsText = e.target.value;
    const skillsArray = skillsText
      .split(",")
      .map((skill) => skill.trim())
      .filter((skill) => skill.length > 0);
    setFormData({ ...formData, skills: skillsArray });
  };

  if (
    !authLoading &&
    (!isAuthenticated || user?.attributes?.["custom:role"] !== "ADMIN")
  ) {
    router.push("/login");
    return null;
  }

  if (authLoading || !isAuthenticated || isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Icon name="Loader2" size="xl" className="animate-spin text-blue-500" />
      </div>
    );
  }

  if (!job) {
    return (
      <div className="w-full max-w-6xl mx-auto p-4 sm:p-6">
        <div className="text-center py-8">
          <Icon
            name="AlertCircle"
            size="xl"
            className="mx-auto text-red-500 mb-4"
          />
          <h2 className="text-lg font-semibold text-gray-900 mb-2">
            Job Not Found
          </h2>
          <p className="text-gray-600 mb-4">
            The job you&apos;re looking for doesn&apos;t exist.
          </p>
          <Button onClick={() => router.push("/admin/jobs")}>
            Back to Jobs
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-4 sm:p-6">
      <ContentHeader
        title="Edit Job"
        subtitle="Update the job details below."
        breadcrumbs={[
          { label: "Dashboard", href: "/admin/dashboard" },
          { label: "Jobs", href: "/admin/jobs" },
          { label: job.title, href: `/admin/jobs/${job.id}` },
          { label: "Edit", current: true },
        ]}
        showBackButton={true}
      />

      {error && (
        <div className="rounded-md bg-red-50 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <Icon name="XCircle" size="md" className="text-red-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      <div className="bg-card rounded-lg shadow-sm p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Job Title *
              </label>
              <Input
                type="text"
                value={formData.title || ""}
                onChange={(e) =>
                  setFormData({ ...formData, title: e.target.value })
                }
                placeholder="Enter job title"
                required
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Budget ($) *
              </label>
              <Input
                type="number"
                value={formData.budget || ""}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    budget: parseFloat(e.target.value) || 0,
                  })
                }
                placeholder="Enter budget amount"
                required
                min="1"
                step="0.01"
                className="w-full"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description *
            </label>
            <Textarea
              value={formData.description || ""}
              onChange={(e) =>
                setFormData({ ...formData, description: e.target.value })
              }
              placeholder="Enter job description"
              rows={6}
              className="w-full"
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category
              </label>
              <select
                value={formData.category || ""}
                onChange={(e) =>
                  setFormData({ ...formData, category: e.target.value })
                }
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
              >
                <option value="">Select a category</option>
                {jobCategories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Job Type
              </label>
              <select
                value={formData.type || JobType.FIXED_PRICE}
                onChange={(e) =>
                  setFormData({ ...formData, type: e.target.value as JobType })
                }
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
              >
                {jobTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                value={formData.status || JobStatus.OPEN}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    status: e.target.value as JobStatus,
                  })
                }
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
              >
                {jobStatuses.map((status) => (
                  <option key={status.value} value={status.value}>
                    {status.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Skills (comma separated)
              </label>
              <Input
                type="text"
                value={formData.skills?.join(", ") || ""}
                onChange={handleSkillsChange}
                placeholder="e.g. React, Node.js, TypeScript"
                className="w-full"
              />
              <p className="mt-1 text-sm text-gray-500">
                Enter skills separated by commas
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Deadline (optional)
              </label>
              <Input
                type="date"
                value={formData.deadline ? formData.deadline.split("T")[0] : ""}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    deadline: e.target.value
                      ? new Date(e.target.value).toISOString()
                      : "",
                  })
                }
                className="w-full"
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="isRemote"
              checked={formData.isRemote || false}
              onChange={(e) =>
                setFormData({ ...formData, isRemote: e.target.checked })
              }
              className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
            />
            <label
              htmlFor="isRemote"
              className="text-sm font-medium text-gray-700"
            >
              Remote work allowed
            </label>
          </div>

          <div className="flex justify-end space-x-3 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
              className="min-w-[100px]"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={
                isSubmitting ||
                !formData.title ||
                !formData.title.trim() ||
                !formData.description ||
                !formData.description.trim()
              }
              className="min-w-[100px]"
            >
              {isSubmitting ? (
                <>
                  <Icon
                    name="Loader2"
                    size="sm"
                    className="animate-spin mr-2"
                  />
                  Updating...
                </>
              ) : (
                <>
                  <Icon name="Check" size="sm" className="mr-2" />
                  Update Job
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
