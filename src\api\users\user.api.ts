import { graphQLClient } from '../../lib/graphql/graphqlClient';
import { GET_USER, LIST_USERS, LIST_USER_PROFILES } from './user.queries';
import { CREATE_USER, UPDATE_USER, DELETE_USER } from './user.mutations';
import type { 
  User, 
  CreateUserInput, 
  UpdateUserInput,
  UserFilter 
} from '../../types/features/user';

export const userApi = {
  getUserById: async (id: string) => {
    const response = await graphQLClient.execute<{ getUser: User | null }>(
      GET_USER,
      { id },
      { authMode: 'userPool' }
    );
    return response.getUser;
  },

  createUser: async (input: CreateUserInput) => {
    const response = await graphQLClient.mutate<{ createUser: User }>(
      CREATE_USER,
      { input }
    );
    return response.createUser;
  },

  updateUser: async (input: UpdateUserInput & { condition?: any }) => {
    const { condition, ...updateInput } = input;
    const variables: any = { input: updateInput };
    
    if (condition) {
      variables.condition = condition;
    }
    
    const response = await graphQLClient.mutate<{ updateUser: User }>(
      UPDATE_USER,
      variables
    );
    
    if (!response.updateUser) {
      throw new Error('Failed to update user: No data returned from API');
    }
    
    return response.updateUser;
  },

  deleteUser: async (id: string) => {
    await graphQLClient.mutate<{ deleteUser: { id: string } }>(
      DELETE_USER,
      { input: { id } },
      { authMode: 'userPool' }
    );
  },

  listUsers: async (filter?: UserFilter) => {
    const response = await graphQLClient.query<{ listUsers: { items: User[]; nextToken?: string } }>(
      LIST_USERS,
      {
        filter: filter ? userApi.transformFilter(filter) : undefined,
        limit: filter?.limit,
        nextToken: filter?.nextToken
      }
    );
    return response?.listUsers || { items: [], nextToken: undefined };
  },

  listUserProfiles: async (filter?: UserFilter) => {
    const response = await graphQLClient.query<{ listUserProfiles: { items: User[]; nextToken?: string } }>(
      LIST_USER_PROFILES,
      {
        filter: filter ? userApi.transformFilter(filter) : undefined,
        limit: filter?.limit,
        nextToken: filter?.nextToken
      }
    );
    return response?.listUserProfiles || { items: [], nextToken: undefined };
  },

  transformFilter(filter: UserFilter): Record<string, unknown> {
    const transformed: Record<string, unknown> = {};
    
    if (filter.role) {
      transformed.role = { eq: filter.role };
    }
    
    if (filter.email) {
      transformed.email = { eq: filter.email };
    }
    
    if (filter.searchTerm) {
      transformed.or = [
        { name: { contains: filter.searchTerm } },
        { email: { contains: filter.searchTerm } },
        { bio: { contains: filter.searchTerm } }
      ];
    }
    
    if (filter.skills?.length) {
      transformed.skills = { contains: filter.skills[0] };
    }
    
    return transformed;
  }
};
