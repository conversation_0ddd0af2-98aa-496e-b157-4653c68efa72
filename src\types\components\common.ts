import { ReactNode } from 'react';

export type SizeVariant = 'sm' | 'md' | 'lg' | 'icon';
export type ButtonVariant = 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
export type LoadingVariant = 'spinner' | 'dots';

export interface BaseProps {
  /** Additional CSS classes */
  className?: string;
}

export interface LoadableProps {
  /** Whether the component is in a loading state */
  isLoading?: boolean;
  /** Loading spinner size */
  loadingSize?: SizeVariant;
  /** Loading spinner variant */
  loadingVariant?: LoadingVariant;
}

export interface WithChildren {
  /** Component content */
  children: ReactNode;
}

export interface WithError {
  /** Whether the component has an error */
  error?: boolean;
}
