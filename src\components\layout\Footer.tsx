import React from "react";
import Link from "next/link";
import { Container } from "./Container";
import { cn } from "@/lib/utils";
import { Icon } from "@/components/ui/Icon";

export interface FooterLink {
  name: string;
  href: string;
}

export interface FooterSection {
  title: string;
  links: FooterLink[];
}

export interface FooterProps {
  sections?: FooterSection[];
  className?: string;
  showSocial?: boolean;
}

const defaultSections: FooterSection[] = [
  {
    title: "Platform",
    links: [
      { name: "Find Jobs", href: "/jobs" },
      { name: "Find Freelancers", href: "/freelancers" },
      { name: "How it Works", href: "/how-it-works" },
      { name: "Pricing", href: "/pricing" },
    ],
  },
  {
    title: "Support",
    links: [
      { name: "Help Center", href: "/help" },
      { name: "Contact Us", href: "/contact" },
      { name: "Community", href: "/community" },
      { name: "Trust & Safety", href: "/safety" },
    ],
  },
  {
    title: "Company",
    links: [
      { name: "About Us", href: "/about" },
      { name: "Careers", href: "/careers" },
      { name: "Press", href: "/press" },
      { name: "Blog", href: "/blog" },
    ],
  },
  {
    title: "Legal",
    links: [
      { name: "Privacy Policy", href: "/privacy" },
      { name: "Terms of Service", href: "/terms" },
      { name: "Cookie Policy", href: "/cookies" },
      { name: "GDPR", href: "/gdpr" },
    ],
  },
];

const socialLinks = [
  {
    name: "Twitter",
    href: "#",
    icon: <Icon name="Twitter" size="md" />,
  },
  {
    name: "LinkedIn",
    href: "#",
    icon: <Icon name="Linkedin" size="md" />,
  },
  {
    name: "GitHub",
    href: "#",
    icon: <Icon name="Github" size="md" />,
  },
];

const Footer: React.FC<FooterProps> = ({
  sections = defaultSections,
  className,
  showSocial = true,
}) => {
  return (
    <footer className={cn("border-t bg-background", className)}>
      <Container>
        <div className="py-12 lg:py-16">
          <div className="grid grid-cols-2 gap-8 lg:grid-cols-4">
            {sections.map((section) => (
              <div key={section.title}>
                <h3 className="text-sm font-semibold text-foreground">
                  {section.title}
                </h3>
                <ul className="mt-4 space-y-3">
                  {section.links.map((link) => (
                    <li key={link.name}>
                      <Link
                        href={link.href}
                        className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                      >
                        {link.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          <div className="mt-12 border-t pt-8">
            <div className="flex flex-col items-center justify-between lg:flex-row">
              <div className="flex items-center space-x-2">
                <div className="h-6 w-6 rounded bg-primary flex items-center justify-center">
                  <span className="text-primary-foreground font-bold text-xs">
                    MV
                  </span>
                </div>
                <span className="text-sm font-medium text-foreground">
                  MyVillage
                </span>
              </div>

              {showSocial && (
                <div className="mt-4 flex space-x-6 lg:mt-0">
                  {socialLinks.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      className="text-muted-foreground hover:text-foreground transition-colors"
                    >
                      <span className="sr-only">{item.name}</span>
                      {item.icon}
                    </Link>
                  ))}
                </div>
              )}
            </div>

            <div className="mt-8 text-center lg:text-left">
              <p className="text-sm text-muted-foreground">
                © {new Date().getFullYear()} MyVillage. All rights reserved.
              </p>
            </div>
          </div>
        </div>
      </Container>
    </footer>
  );
};

export { Footer };
