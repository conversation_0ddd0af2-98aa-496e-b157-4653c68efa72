'use client';
import * as React from 'react';
import toast, { Toaster as HotToaster } from 'react-hot-toast';
import { cn } from '@/lib/utils';

export const showToast = {
  success: (message: string, options?: any) => toast.success(message, options),
  error: (message: string, options?: any) => toast.error(message, options),
  loading: (message: string, options?: any) => toast.loading(message, options),
  custom: (jsx: string | React.ReactElement, options?: any) => toast.custom(jsx, options),
  promise: toast.promise,
  dismiss: toast.dismiss,
  remove: toast.remove,
};

export { default as toast } from 'react-hot-toast';

export function useToast() {
  return {
    toast: showToast,
    showToast: (message: string, options?: {
      type?: 'success' | 'error' | 'loading';
      description?: string;
      duration?: number;
      position?: 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right';
    }) => {
      const { type = 'success', description, ...restOptions } = options || {};
      
      const displayMessage = description ? `${message}\n${description}` : message;
      
      switch (type) {
        case 'success':
          return toast.success(displayMessage, restOptions);
        case 'error':
          return toast.error(displayMessage, restOptions);
        case 'loading':
          return toast.loading(displayMessage, restOptions);
        default:
          return toast(displayMessage, restOptions);
      }
    }
  };
}

export function Toaster({ 
  position = 'top-right',
  duration = 4000,
  className,
  ...props 
}: {
  position?: 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right';
  duration?: number;
  className?: string;
  [key: string]: any;
}) {
  return (
    <HotToaster
      position={position}
      toastOptions={{
        duration,
        className: cn(
          'bg-background text-foreground border border-border shadow-lg',
          className
        ),
        style: {
          borderRadius: '8px',
          padding: '12px 16px',
        },
        success: {
          className: cn(
            'bg-green-50 text-green-900 border-green-200',
            className
          ),
          iconTheme: {
            primary: '#16a34a',
            secondary: '#dcfce7',
          },
        },
        error: {
          className: cn(
            'bg-red-50 text-red-900 border-red-200',
            className
          ),
          iconTheme: {
            primary: '#dc2626',
            secondary: '#fef2f2',
          },
        },
        loading: {
          className: cn(
            'bg-blue-50 text-blue-900 border-blue-200',
            className
          ),
          iconTheme: {
            primary: '#2563eb',
            secondary: '#eff6ff',
          },
        },
      }}
      {...props}
    />
  );
}

export const ToastProvider = ({ children }: { children: React.ReactNode }) => children;
export const ToastViewport = () => null;
