/* Amplify Params - DO NOT EDIT
	API_MYVILLAGEFREELANCE_GRAPHQLAPIENDPOINTOUTPUT
	API_MYVILLAGEFREELANCE_GRAPHQLAPIIDOUTPUT
	API_MYVILLAGEFREELANCE_GRAPHQLAPIKEYOUTPUT
	AUTH_MYVILLAGEFREELANCEB6232A5F_USERPOOLID
	ENV
	REGION
Amplify Params - DO NOT EDIT */
const {
  CognitoIdentityProviderClient,
  AdminSetUserPasswordCommand,
  AdminGetUserCommand,
  AdminAddUserToGroupCommand,
  ConfirmSignUpCommand
} = require('@aws-sdk/client-cognito-identity-provider');
const { DynamoDB } = require('@aws-sdk/client-dynamodb');
const { marshall } = require('@aws-sdk/util-dynamodb');
const { v4: uuidv4 } = require('uuid');

const cognitoClient = new CognitoIdentityProviderClient({ 
  region: process.env.REGION || 'us-east-1' 
});

const ddb = new DynamoDB({
  region: process.env.REGION || 'us-east-1'
});

const USER_TABLE = process.env.API_MYVILLAGEFREELANCE_USERTABLE_NAME || `User-${process.env.API_MYVILLAGEFREELANCE_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;

exports.handler = async (event) => {

  const userPoolId = process.env.AUTH_MYVILLAGEFREELANCEB6232A5F_USERPOOLID;
  const clientId = process.env.AUTH_MYVILLAGEFREELANCEB6232A5F_USERPOOLWEBCLIENTID;
  let username, password, verificationCode, role;
  

  if (event.body) {
    try {
      const body = JSON.parse(event.body);
      username = body.username || body.email;
      password = body.password;
      verificationCode = body.verificationCode || body.code;
      role = body.role;
    } catch (e) {
      return {
        statusCode: 400,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Headers": "Content-Type",
          "Access-Control-Allow-Methods": "POST, OPTIONS"
        },
        body: JSON.stringify({ error: 'Invalid request body' })
      };
    }
  } else {
    username = event.username || event.email;
    password = event.password;
    verificationCode = event.verificationCode || event.code;
    role = event.role;
  }

  if (!username || !password || !verificationCode) {
    return {
      statusCode: 400,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      },
      body: JSON.stringify({ error: 'username, password, and verificationCode are required' })
    };
  }

  try {
    const confirmCmd = new ConfirmSignUpCommand({
      ClientId: clientId,
      Username: username,
      ConfirmationCode: verificationCode,
    });
    await cognitoClient.send(confirmCmd);

    const setPwdCmd = new AdminSetUserPasswordCommand({
      UserPoolId: userPoolId,
      Username: username,
      Password: password,
      Permanent: true
    });
    await cognitoClient.send(setPwdCmd);

    if (role) {
      const groupName = role || process.env.DEFAULT_USER_GROUP;
      const addToGroupCmd = new AdminAddUserToGroupCommand({
        UserPoolId: userPoolId,
        Username: username,
        GroupName: groupName,
      });
      await cognitoClient.send(addToGroupCmd);
    }

    const getUserCmd = new AdminGetUserCommand({
      UserPoolId: userPoolId,
      Username: username
    });
    const result = await cognitoClient.send(getUserCmd);

    const userAttributes = {};
    
    result.UserAttributes.forEach(attr => {
      userAttributes[attr.Name] = attr.Value;
    });
    
    const userId = userAttributes.sub || username;
    const userData = {
      id: userId,
      email: username,
      name: userAttributes.name || '',
      role: role || 'CLIENT',
      cognitoId: userId,
      profilePhoto: userAttributes.picture || null,
      bio: userAttributes.bio || null,
      skills: []
    };
    
    userData.role = userData.role.toUpperCase();
    
    const now = new Date().toISOString();
    const userName = userData.name || username.split('@')[0];
    
    const updateUserParams = {
      TableName: USER_TABLE,
      Key: marshall({ id: userId }),
      ExpressionAttributeNames: {
        '#NM': 'name',
        '#EM': 'email',
        '#RL': 'role',
        '#CI': 'cognitoId',
        '#ST': 'status',
        '#CT': 'createdAt',
        '#UT': 'updatedAt',
        '#PV': 'phoneNumberVerified',
        '#IS': 'isSignup',
        '#CF': 'confirmationStatus',
        '#BIO': 'bio',
        '#PHOTO': 'profilePhoto',
        '#SKILLS': 'skills'
      },
      ExpressionAttributeValues: marshall({
        ':nm': userName,
        ':em': username,
        ':rl': (role || 'FREELANCER').toUpperCase(),
        ':ci': userId,
        ':st': 'active',
        ':ct': now,
        ':ut': now,
        ':pv': true,
        ':is': true,
        ':cf': 'CONFIRMED',
        ':bio': userData.bio || null,
        ':photo': userData.profilePhoto || null,
        ':skills': userData.skills || []
      }),
      UpdateExpression: 'SET #NM = :nm, #EM = :em, #RL = :rl, #CI = :ci, #ST = :st, #CT = :ct, #UT = :ut, #PV = :pv, #IS = :is, #CF = :cf, #BIO = :bio, #PHOTO = :photo, #SKILLS = :skills',
      ReturnValues: 'ALL_NEW'
    };

    try {
      await ddb.updateItem(updateUserParams);
    } catch (error) {
      console.error('Error creating/updating user in DynamoDB:', {
        error: error.message,
        stack: error.stack
      });
      throw error;
    }

    return {
      statusCode: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      },
      body: JSON.stringify({
        message: 'User verified and confirmed successfully',
        userStatus: result.UserStatus,
        email: username
      })
    };
  } catch (err) {
    console.error("Error confirming user:", err);

    let errorMessage = 'Verification failed';
    let statusCode = 500;

    if (err.name === 'CodeMismatchException') {
      errorMessage = 'Invalid verification code';
      statusCode = 400;
    } else if (err.name === 'ExpiredCodeException') {
      errorMessage = 'Verification code has expired';
      statusCode = 400;
    } else if (err.name === 'UserNotFoundException') {
      errorMessage = 'User not found';
      statusCode = 404;
    }

    return {
      statusCode: statusCode,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      },
      body: JSON.stringify({ error: errorMessage, details: err.message })
    };
  }
};
