"use client";

import React, { useState, useEffect } from 'react';
import { Contract, ContractStatus } from '@/types/features/contracts/contract.types';
import { ContractCard } from './ContractCard';
import { Button } from '@/components/ui/Button';
import { Skeleton } from '@/components/ui/Skeleton';
import { useToast } from '@/components/ui/toast';
import { contractService } from '@/api/contracts/contract.service';

const ITEMS_PER_PAGE = 5;

interface ContractsListProps {
  filters?: {
    status?: ContractStatus;
    clientId?: string;
    freelancerId?: string;
  };
  onContractSelect?: (contract: Contract) => void;
  className?: string;
}

export const ContractsList: React.FC<ContractsListProps> = ({
  filters = {},
  onContractSelect,
  className = '',
}) => {
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [nextToken, setNextToken] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const { showToast } = useToast();
  
  const loadContracts = React.useCallback(async (loadMore = false) => {
    try {
      setLoading(true);
      
      const result = await contractService.listContracts(
        { ...filters },
        ITEMS_PER_PAGE,
        loadMore ? nextToken || undefined : undefined
      );
      
      setContracts(prev => loadMore ? [...prev, ...result.items] : result.items);
      setNextToken(result.nextToken || null);
      setHasMore(!!result.nextToken);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to load contracts');
      setError(error);
      showToast('Error', {
        description: 'Failed to load contracts. Please try again.',
        position: "top-right",
      });
    } finally {
      setLoading(false);
    }
  }, [filters, nextToken, showToast]);

  useEffect(() => {
    loadContracts(false);
  }, [filters, loadContracts]);

  const loadMore = async () => {
    if (!nextToken || loading) return;
    await loadContracts(true);
  };

  const handleContractClick = (contract: Contract) => {
    if (onContractSelect) {
      onContractSelect(contract);
    }
  };

  if (error) {
    return (
      <div className="p-4 text-center text-red-500">
        Error loading contracts: {error.message}
      </div>
    );
  }

  if (loading && contracts.length === 0) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Skeleton key={i} className="h-32 w-full rounded-lg" />
        ))}
      </div>
    );
  }

  if (contracts.length === 0) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium">No contracts found</h3>
        <p className="text-muted-foreground mt-1">
          {filters.status
            ? `You don't have any ${filters.status.toLowerCase()} contracts.`
            : 'Get started by creating your first contract.'}
        </p>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-1">
        {contracts.map((contract) => (
          <div 
            key={contract.id} 
            onClick={() => handleContractClick(contract)}
            className="cursor-pointer hover:opacity-90 transition-opacity"
          >
            <ContractCard 
              contract={contract} 
              onView={() => handleContractClick(contract)}
            />
          </div>
        ))}
      </div>

      {hasMore && (
        <div className="flex justify-center mt-6">
          <Button 
            variant="outline" 
            onClick={loadMore}
            disabled={loading}
          >
            {loading ? 'Loading...' : 'Load More'}
          </Button>
        </div>
      )}
    </div>
  );
};

export default ContractsList;
