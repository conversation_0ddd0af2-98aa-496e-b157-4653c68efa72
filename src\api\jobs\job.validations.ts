import { CreateJobInput, UpdateJobInput } from '@/types/features/jobs/job.types';
import { CreateJobProposalInput } from '@/types/features/proposals/proposal.types';
import { ErrorCode } from '@/utils/errorHandler';

export const validateJobInput = (input: CreateJobInput | UpdateJobInput): void => {
  const errors: Record<string, string[]> = {};

  if (!input.title?.trim()) {
    errors.title = ['Job title is required'];
  } else if (input.title.length > 200) {
    errors.title = ['Title must be less than 200 characters'];
  }

  if ('budget' in input && input.budget !== undefined) {
    if (typeof input.budget !== 'number' || isNaN(input.budget)) {
      errors.budget = ['Budget must be a valid number'];
    } else if (input.budget < 0) {
      errors.budget = ['Budget must be a positive number'];
    } else if (input.budget > 1000000) {
      errors.budget = ['Budget must be less than 1,000,000'];
    }
  }

  if ('description' in input && input.description && input.description.length > 5000) {
    errors.description = ['Description must be less than 5000 characters'];
  }

  if (Object.keys(errors).length > 0) {
    throw {
      message: 'Invalid job input',
      code: ErrorCode.VALIDATION_ERROR,
      details: { errors },
      statusCode: 400
    };
  }
};

export const validateProposalInput = (input: CreateJobProposalInput): void => {
  const errors: Record<string, string[]> = {};

  if (!input.jobId?.trim()) {
    errors.jobId = ['Job ID is required'];
  }

  if (input.bidAmount !== undefined) {
    if (typeof input.bidAmount !== 'number' || isNaN(input.bidAmount)) {
      errors.bidAmount = ['Bid amount must be a valid number'];
    } else if (input.bidAmount < 0) {
      errors.bidAmount = ['Bid amount must be a positive number'];
    } else if (input.bidAmount > 1000000) {
      errors.bidAmount = ['Bid amount must be less than 1,000,000'];
    }
  }

  if (input.coverLetter && input.coverLetter.length > 2000) {
    errors.coverLetter = ['Cover letter must be less than 2000 characters'];
  }

  if (input.estimatedTime) {
    const estimatedTimeNum = parseInt(input.estimatedTime, 10);
    if (isNaN(estimatedTimeNum) || estimatedTimeNum < 1) {
      errors.estimatedTime = ['Estimated time must be at least 1 day'];
    }
  }

  if (Object.keys(errors).length > 0) {
    throw {
      message: 'Invalid proposal input',
      code: ErrorCode.VALIDATION_ERROR,
      details: { errors },
      statusCode: 400
    };
  }
};

export const validateJobFilter = (filter: any): void => {
  const errors: Record<string, string[]> = {};

  if (filter?.status && !['OPEN', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'].includes(filter.status)) {
    errors.status = ['Invalid status value'];
  }

  if (filter?.budgetMin !== undefined) {
    const min = Number(filter.budgetMin);
    if (isNaN(min) || min < 0) {
      errors.budgetMin = ['Minimum budget must be a positive number'];
    }
  }

  if (filter?.budgetMax !== undefined) {
    const max = Number(filter.budgetMax);
    if (isNaN(max) || max < 0) {
      errors.budgetMax = ['Maximum budget must be a positive number'];
    }
  }

  if (filter?.budgetMin !== undefined && filter?.budgetMax !== undefined) {
    const min = Number(filter.budgetMin);
    const max = Number(filter.budgetMax);
    if (min > max) {
      errors.budgetRange = ['Minimum budget cannot be greater than maximum budget'];
    }
  }

  if (filter?.limit && (isNaN(Number(filter.limit)) || Number(filter.limit) < 1 || Number(filter.limit) > 100)) {
    errors.limit = ['Limit must be between 1 and 100'];
  }

  if (Object.keys(errors).length > 0) {
    throw {
      message: 'Invalid filter parameters',
      code: ErrorCode.VALIDATION_ERROR,
      details: { errors },
      statusCode: 400
    };
  }
};
