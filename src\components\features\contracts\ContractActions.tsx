import React, { useState } from "react";
import { Button } from "@/components/ui/Button";
import { useToast } from "@/components/ui/toast";
import {
  ContractStatus,
  Contract,
  ExtendedContract,
  ContractWorkflowActions,
} from "@/types/features/contracts/contract.types";
import { JobStatus } from "@/types/features/jobs/job.types";
import { UserRole } from "@/types/features/auth/auth.types";
import {
  PaymentError,
  PaymentMethod,
} from "@/types/features/payments/payment.types";
import contractService from "@/api/contracts/contract.service";
import { PaymentDialog } from "@/components/features/payments/PaymentDialog";
import { Icon } from "@/components/ui";

interface ContractActionsProps {
  contract: Contract | ExtendedContract;
  userRole: UserRole;
  userId: string;
  onStatusUpdate?: (newStatus: ContractStatus) => void;
  onJobStatusUpdate?: (newStatus: JobStatus) => void;
  className?: string;
}

export const ContractActions: React.FC<ContractActionsProps> = ({
  contract,
  userRole,
  userId,
  onStatusUpdate,
  onJobStatusUpdate,
  className = "",
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);
  const { showToast } = useToast();

  const isClient = userRole === UserRole.CLIENT && userId === contract.clientId;
  const isFreelancer =
    userRole === UserRole.FREELANCER && userId === contract.freelancerId;

  const getAvailableActions = (): ContractWorkflowActions => {
    const actions: ContractWorkflowActions = {
      canAccept: false,
      canReject: false,
      canSubmitWork: false,
      canApproveWork: false,
      canRequestRevisions: false,
      canCancel: false,
      canMarkPaid: false,
    };

    switch (contract.status) {
      case ContractStatus.PENDING_FREELANCER_ACCEPTANCE:
        if (isFreelancer) {
          actions.canAccept = true;
          actions.canReject = true;
        }
        break;

      case ContractStatus.ACTIVE:
        if (isFreelancer) {
          actions.canSubmitWork = true;
        }
        if (isClient || isFreelancer) {
          actions.canCancel = true;
        }
        break;

      case ContractStatus.WORK_SUBMITTED:
        if (isClient) {
          actions.canApproveWork = true;
          actions.canRequestRevisions = true;
        }
        break;

      case ContractStatus.REVISIONS_REQUESTED:
        if (isFreelancer) {
          actions.canSubmitWork = true;
        }
        if (isClient || isFreelancer) {
          actions.canCancel = true;
        }
        break;

      case ContractStatus.COMPLETED:
        if (isClient) {
          actions.canMarkPaid = true;
        }
        break;
    }

    return actions;
  };

  const actions = getAvailableActions();

  const handleAcceptContract = async () => {
    try {
      setIsLoading(true);

      await contractService.updateContractStatus(
        contract.id,
        ContractStatus.ACTIVE
      );

      if (contract.jobId) {
        await contractService.updateJobStatus(
          contract.jobId,
          JobStatus.IN_PROGRESS
        );
        onJobStatusUpdate?.(JobStatus.IN_PROGRESS);
      }

      onStatusUpdate?.(ContractStatus.ACTIVE);

      showToast("Success", {
        description:
          "Contract accepted successfully! You can now start working.",
        position: "top-right",
      });
    } catch (error) {
      console.error("Error accepting contract:", error);
      showToast("Error", {
        description: "Failed to accept contract. Please try again.",
        position: "top-right",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRejectContract = async () => {
    try {
      setIsLoading(true);

      await contractService.updateContractStatus(
        contract.id,
        ContractStatus.CANCELLED
      );

      if (contract.jobId) {
        await contractService.updateJobStatus(contract.jobId, JobStatus.OPEN);
        onJobStatusUpdate?.(JobStatus.OPEN);
      }

      onStatusUpdate?.(ContractStatus.CANCELLED);

      showToast("Success", {
        description: "Contract declined successfully.",
        position: "top-right",
      });
    } catch (error) {
      console.error("Error rejecting contract:", error);
      showToast("Error", {
        description: "Failed to decline contract. Please try again.",
        position: "top-right",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelContract = async () => {
    try {
      setIsLoading(true);

      await contractService.updateContractStatus(
        contract.id,
        ContractStatus.CANCELLED
      );

      if (contract.jobId) {
        await contractService.updateJobStatus(
          contract.jobId,
          JobStatus.CANCELLED
        );
        onJobStatusUpdate?.(JobStatus.CANCELLED);
      }

      onStatusUpdate?.(ContractStatus.CANCELLED);

      showToast("Success", {
        description: "Contract cancelled successfully.",
        position: "top-right",
      });
    } catch (error) {
      console.error("Error cancelling contract:", error);
      showToast("Error", {
        description: "Failed to cancel contract. Please try again.",
        position: "top-right",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleApproveWork = async () => {
    try {
      setIsLoading(true);

      await contractService.updateContractStatus(
        contract.id,
        ContractStatus.COMPLETED
      );

      if (contract.jobId) {
        await contractService.updateJobStatus(
          contract.jobId,
          JobStatus.COMPLETED
        );
        onJobStatusUpdate?.(JobStatus.COMPLETED);
      }

      onStatusUpdate?.(ContractStatus.COMPLETED);

      showToast("Success", {
        description: "Work approved! Contract is now completed.",
        position: "top-right",
      });
    } catch (error) {
      console.error("Error approving work:", error);
      showToast("Error", {
        description: "Failed to approve work. Please try again.",
        position: "top-right",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRequestRevisions = async () => {
    try {
      setIsLoading(true);

      await contractService.updateContractStatus(
        contract.id,
        ContractStatus.REVISIONS_REQUESTED
      );

      onStatusUpdate?.(ContractStatus.REVISIONS_REQUESTED);

      showToast("Success", {
        description: "Revision request sent to freelancer.",
        position: "top-right",
      });
    } catch (error) {
      console.error("Error requesting revisions:", error);
      showToast("Error", {
        description: "Failed to request revisions. Please try again.",
        position: "top-right",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleMarkPaid = () => {
    setShowPaymentDialog(true);
  };

  const handlePaymentSuccess = async () => {
    try {
      setIsLoading(true);

      const amount = "budget" in contract ? contract.budget : 0;

      await contractService.createPayment(
        contract.id,
        amount,
        PaymentMethod.STRIPE
      );

      await contractService.updateContractStatus(
        contract.id,
        ContractStatus.PAID
      );

      onStatusUpdate?.(ContractStatus.PAID);

      showToast("Success", {
        description: "Payment processed successfully!",
        position: "top-right",
      });

      setShowPaymentDialog(false);
    } catch (error) {
      console.error("Error updating payment status:", error);
      showToast("Error", {
        description:
          "Payment was processed but failed to update contract status.",
        position: "top-right",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handlePaymentError = (error: PaymentError) => {
    console.error("Payment error:", error);
    showToast("Error", {
      description: error.message || "Payment failed. Please try again.",
      position: "top-right",
    });
  };

  if (!isClient && !isFreelancer) {
    return null;
  }

  return (
    <>
      <div className={`flex flex-wrap gap-2 ${className}`}>
        {actions.canAccept && (
          <Button
            onClick={handleAcceptContract}
            size="sm"
            disabled={isLoading}
            className="bg-green-600 hover:bg-green-700"
          >
            <Icon name="CheckCircle" className="mr-2 h-4 w-4" />
            Accept Contract
          </Button>
        )}

        {actions.canReject && (
          <Button
            size="sm"
            variant="outline"
            onClick={handleRejectContract}
            disabled={isLoading}
          >
            <Icon name="XCircle" className="mr-2 h-4 w-4" />
            Decline Contract
          </Button>
        )}

        {actions.canSubmitWork && (
          <Button
            size="sm"
            onClick={() => {
              showToast("Info", {
                description:
                  "Use the work submission form below to submit your work.",
                position: "top-right",
              });
            }}
            disabled={isLoading}
          >
            <Icon name="Upload" className="mr-2 h-4 w-4" />
            Submit Work
          </Button>
        )}

        {actions.canApproveWork && (
          <Button
            size="sm"
            onClick={handleApproveWork}
            disabled={isLoading}
            className="bg-green-600 hover:bg-green-700"
          >
            <Icon name="ThumbsUp" className="mr-2 h-4 w-4" />
            Approve Work
          </Button>
        )}

        {actions.canRequestRevisions && (
          <Button
            size="sm"
            variant="outline"
            onClick={handleRequestRevisions}
            disabled={isLoading}
          >
            <Icon name="MessageSquare" className="mr-2 h-4 w-4" />
            Request Changes
          </Button>
        )}

        {actions.canCancel && (
          <Button
            size="sm"
            variant="destructive"
            onClick={handleCancelContract}
            disabled={isLoading}
          >
            <Icon name="Ban" className="mr-2 h-4 w-4" />
            Cancel Contract
          </Button>
        )}

        {actions.canMarkPaid && (
          <Button
            size="sm"
            onClick={handleMarkPaid}
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Icon name="DollarSign" className="mr-2 h-4 w-4" />
            Process Payment
          </Button>
        )}
      </div>

      {/* Payment Dialog */}
      <PaymentDialog
        isOpen={showPaymentDialog}
        onClose={() => setShowPaymentDialog(false)}
        contract={contract}
        clientId={userId}
        clientData={{
          id: userId,
          name: (contract as ExtendedContract)?.client?.name || "Client",
          email:
            (contract as ExtendedContract)?.client?.email ||
            "<EMAIL>",
        }}
        onPaymentSuccess={handlePaymentSuccess}
        onPaymentError={handlePaymentError}
      />
    </>
  );
};

export default ContractActions;
