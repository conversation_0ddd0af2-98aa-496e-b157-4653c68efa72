"use client";

import React, { useState, useCallback } from "react";
import { Controller, useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Textarea } from "@/components/ui/Textarea";
import { Form, FormField } from "@/components/ui/Form";
import { Select } from "@/components/ui/Select";
import {
  Contract,
  ContractStatus,
  ContractType,
  CreateContractDto,
} from "@/types/features/contracts/contract.types";
import { contractService } from "@/api/contracts/contract.service";
import { useToast } from "@/components/ui/toast";
import { Icon } from "@/components/ui/Icon";
import { cn } from "@/lib/utils";
import DatePicker from "@/components/ui/DatePicker";

class FormErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("Form Error Boundary:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <h3 className="text-red-800 font-medium">Something went wrong</h3>
          <p className="text-red-700 text-sm mt-1">
            We&apos;re having trouble loading the form. Please refresh the page
            or try again later.
          </p>
        </div>
      );
    }

    return this.props.children;
  }
}

type FormState = "idle" | "submitting" | "success" | "error";

const contractFormSchema = yup.object().shape({
  title: yup
    .string()
    .required("Title is required")
    .min(5, "Title must be at least 5 characters")
    .max(100, "Title cannot exceed 100 characters"),
  description: yup
    .string()
    .required("Description is required")
    .min(20, "Description must be at least 20 characters")
    .max(5000, "Description cannot exceed 5000 characters"),
  type: yup
    .string()
    .required("Contract type is required")
    .oneOf(Object.values(ContractType), "Invalid contract type"),
  terms: yup
    .string()
    .required("Terms are required")
    .min(20, "Terms must be at least 20 characters")
    .max(10000, "Terms cannot exceed 10000 characters"),
  startDate: yup
    .date()
    .required("Start date is required")
    .min(new Date(), "Start date cannot be in the past"),
  endDate: yup
    .date()
    .optional()
    .min(yup.ref("startDate"), "End date must be after start date"),
  budget: yup
    .number()
    .typeError("Budget must be a number")
    .positive("Budget must be greater than 0")
    .max(1000000, "Budget cannot exceed $1,000,000")
    .required("Budget is required"),
  clientId: yup.string().required("Client is required"),
  freelancerId: yup.string().required("Freelancer is required"),
  jobId: yup.string().optional(),
  proposalId: yup.string().optional(),
});

type ContractFormValues = yup.InferType<typeof contractFormSchema> & {
  endDate?: Date | null;
};

interface ContractFormProps {
  initialData?: Partial<Contract> & { jobId?: string; proposalId?: string };
  onSubmit: (data: CreateContractDto) => Promise<void>;
  isLoading?: boolean;
  submitButtonText?: string;
  className?: string;
}

export const ContractForm: React.FC<ContractFormProps> = ({
  initialData,
  onSubmit,
  isLoading = false,
  submitButtonText = "Create Contract",
  className = "",
}) => {
  const [formState, setFormState] = useState<FormState>("idle");
  const [formError, setFormError] = useState<string | null>(null);
  const { showToast } = useToast();

  const isSubmitting = formState === "submitting" || isLoading;

  const form = useForm<ContractFormValues>({
    resolver: yupResolver(contractFormSchema as any),
    defaultValues: {
      title: initialData?.title || "",
      description: initialData?.description || "",
      type: initialData?.type || ContractType.FIXED_PRICE,
      terms: initialData?.terms || "",
      clientId: initialData?.clientId || "",
      freelancerId: initialData?.freelancerId || "",
      startDate: initialData?.startDate
        ? new Date(initialData.startDate)
        : new Date(),
      endDate: initialData?.endDate ? new Date(initialData.endDate) : undefined,
      budget: initialData?.budget || 0,
    },
  });

  const {
    register,
    control,
    formState: { errors },
    watch,
  } = form;
  const startDate = watch("startDate");

  const handleSubmit = async (formData: ContractFormValues) => {
    if (isSubmitting) return;

    setFormState("submitting");
    setFormError(null);

    try {
      const shouldSendToFreelancer = !!(
        initialData?.jobId &&
        initialData?.proposalId &&
        initialData?.freelancerId
      );

      const contractData: CreateContractDto = {
        jobId: initialData?.jobId || "",
        proposalId: initialData?.proposalId || "",
        title: formData.title.trim(),
        description: formData.description.trim(),
        type: formData.type as ContractType,
        status: shouldSendToFreelancer
          ? ContractStatus.PENDING_FREELANCER_ACCEPTANCE
          : ContractStatus.DRAFT,
        terms: formData.terms.trim(),
        startDate: formData.startDate.toISOString(),
        endDate: formData.endDate?.toISOString(),
        budget: parseFloat(formData.budget.toFixed(2)),
        clientId: formData.clientId,
        freelancerId: formData.freelancerId,
      };

      await onSubmit(contractData);
      setFormState("success");

      showToast(
        shouldSendToFreelancer
          ? "Contract sent to freelancer for review"
          : "Contract saved successfully",
        { position: "top-right" }
      );
    } catch (error) {
      setFormState("error");
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to submit contract. Please try again.";

      setFormError(errorMessage);
      showToast(errorMessage, { position: "top-right" });

      console.error("Contract submission error:", {
        error,
        context: { initialData },
        timestamp: new Date().toISOString(),
      });
    }
  };

  const handleSaveDraft = useCallback(async () => {
    if (isSubmitting) return;

    setFormState("submitting");
    setFormError(null);

    try {
      const formValues = form.getValues();
      if (!initialData?.jobId || !initialData?.proposalId) {
        throw new Error("Job ID and Proposal ID are required to save a draft");
      }

      const draftData: CreateContractDto = {
        ...formValues,
        title: formValues.title.trim(),
        description: formValues.description.trim(),
        terms: formValues.terms.trim(),
        type: formValues.type as ContractType,
        startDate: formValues.startDate.toISOString(),
        endDate: formValues.endDate?.toISOString(),
        budget: parseFloat(formValues.budget.toFixed(2)),
        jobId: initialData.jobId,
        proposalId: initialData.proposalId,
        status: ContractStatus.DRAFT,
      };

      await contractService.createContract(draftData);
      setFormState("success");

      showToast("Draft saved successfully", { position: "top-right" });
    } catch (error) {
      setFormState("error");
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to save draft. Please try again.";

      setFormError(errorMessage);
      showToast(errorMessage, { position: "top-right" });

      console.error("Draft save error:", {
        error,
        context: { initialData },
        timestamp: new Date().toISOString(),
      });
    }
  }, [form, initialData, isSubmitting, showToast]);

  return (
    <FormErrorBoundary>
      <Form onSubmit={form.handleSubmit(handleSubmit)}>
        <div className={cn("space-y-6 relative", className)}>
          {/* Loading overlay */}
          {isSubmitting && (
            <div className="absolute inset-0 bg-white/50 backdrop-blur-sm z-10 flex items-center justify-center rounded-lg">
              <div className="flex flex-col items-center gap-2">
                <Icon
                  name="Loader2"
                  className="h-8 w-8 animate-spin text-primary"
                />
                <p className="text-sm text-muted-foreground">
                  {formState === "submitting" ? "Saving..." : "Processing..."}
                </p>
              </div>
            </div>
          )}

          {/* Form-wide error alert */}
          {formState === "error" && formError && (
            <div className="p-4 mb-4 text-sm text-red-700 bg-red-100 rounded-lg">
              <div className="font-medium">Error</div>
              <p className="mt-1">{formError}</p>
            </div>
          )}

          {/* Success message */}
          {formState === "success" && (
            <div className="p-4 mb-4 text-sm text-green-700 bg-green-100 rounded-lg">
              <div className="font-medium">Success!</div>
              <p className="mt-1">
                {initialData?.status === ContractStatus.DRAFT
                  ? "Draft saved successfully."
                  : "Contract submitted successfully!"}
              </p>
            </div>
          )}

          {/* Form fields */}
          <div className="space-y-4">
            <FormField
              label="Contract Title"
              required
              error={errors.title?.message as string}
            >
              <Input
                placeholder="e.g., Website Development Contract"
                {...register("title")}
              />
            </FormField>

            <FormField
              label="Description"
              required
              error={errors.description?.message as string}
            >
              <Textarea
                placeholder="Describe the work to be performed..."
                className="min-h-[120px]"
                {...register("description")}
              />
            </FormField>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                label="Contract Type"
                required
                error={errors.type?.message as string}
              >
                <Select
                  {...register("type")}
                  options={[
                    { value: ContractType.FIXED_PRICE, label: "Fixed Price" },
                    { value: ContractType.HOURLY, label: "Hourly" },
                  ]}
                  placeholder="Select contract type"
                  className="w-full"
                />
              </FormField>

              <FormField
                label="Budget"
                required
                error={errors.budget?.message as string}
              >
                <Input
                  type="number"
                  placeholder="Enter budget"
                  {...register("budget")}
                />
              </FormField>

              <FormField
                label="Start Date"
                required
                error={errors.startDate?.message as string}
              >
                <Controller
                  name="startDate"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      value={field.value}
                      onChange={field.onChange}
                      placeholder="Select start date"
                      minDate={new Date()}
                    />
                  )}
                />
              </FormField>

              <FormField
                label="End Date (Optional)"
                error={errors.endDate?.message as string}
              >
                <Controller
                  name="endDate"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      value={field.value || undefined}
                      onChange={field.onChange}
                      placeholder="Select end date (optional)"
                      minDate={startDate}
                    />
                  )}
                />
              </FormField>
            </div>

            <FormField
              label="Terms & Conditions"
              error={errors.terms?.message as string}
            >
              <Textarea
                placeholder="Specify the terms and conditions of this contract..."
                className="min-h-[150px] font-mono text-sm"
                {...register("terms")}
              />
              <p className="text-sm text-muted-foreground mt-2">
                Be clear about payment terms, revision policies, ownership
                rights, and any other important conditions.
              </p>
            </FormField>
          </div>

          {/* Form actions */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              disabled={isSubmitting}
              onClick={handleSaveDraft}
            >
              <Icon name="Save" className="mr-2 h-4 w-4" />
              {isSubmitting ? "Saving..." : "Save Draft"}
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Icon name="Loader2" className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                submitButtonText
              )}
            </Button>
          </div>
        </div>
      </Form>
    </FormErrorBoundary>
  );
};

export default ContractForm;
