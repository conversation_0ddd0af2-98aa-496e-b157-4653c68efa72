import React from 'react';
import { Card, CardContent } from '@/components/ui/Card';
import { Icon } from '@/components/ui/Icon';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { formatDistanceToNow } from 'date-fns';
import { Job } from '@/types/features/jobs/job.types';
import { JobStatus } from '@/types/enums';
import type { IconName } from '@/types/components/Icon';

export interface JobSummaryCardProps {
  /** Job data to display */
  job: Job;
  /** Callback for edit action */
  onEdit?: () => void;
  /** Callback for delete action */
  onDelete?: () => void;
  /** Loading state for edit action */
  editLoading?: boolean;
  /** Loading state for delete action */
  deleteLoading?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Loading state */
  loading?: boolean;
}

export const JobSummaryCard: React.FC<JobSummaryCardProps> = ({
  job,
  onEdit,
  onDelete,
  editLoading = false,
  deleteLoading = false,
  className = '',
  loading = false,
}) => {
  const getStatusBadgeVariant = (status: JobStatus): string => {
    const statusColors: Record<JobStatus, string> = {
      [JobStatus.OPEN]: 'bg-green-100 text-green-800 border-green-200',
      [JobStatus.IN_PROGRESS]: 'bg-blue-100 text-blue-800 border-blue-200',
      [JobStatus.COMPLETED]: 'bg-purple-100 text-purple-800 border-purple-200',
      [JobStatus.CANCELLED]: 'bg-red-100 text-red-800 border-red-200',
    };
    return statusColors[status] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const getStatusIcon = (status: JobStatus): IconName => {
    const statusIcons: Record<JobStatus, IconName> = {
      [JobStatus.OPEN]: 'Briefcase',
      [JobStatus.IN_PROGRESS]: 'Clock',
      [JobStatus.COMPLETED]: 'CheckCircle',
      [JobStatus.CANCELLED]: 'XCircle',
    };
    return statusIcons[status] || 'Briefcase';
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="flex items-start gap-6">
              <div className="h-16 w-16 bg-gray-300 rounded-lg"></div>
              <div className="flex-1 space-y-3">
                <div className="h-7 bg-gray-300 rounded w-3/4"></div>
                <div className="h-5 bg-gray-300 rounded w-1/2"></div>
                <div className="flex gap-2">
                  <div className="h-6 bg-gray-300 rounded w-20"></div>
                  <div className="h-6 bg-gray-300 rounded w-24"></div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardContent className="p-6">
        <div className="flex items-start gap-6">
          {/* Job Icon */}
          <div className="flex-shrink-0 h-16 w-16 flex items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 text-white shadow-lg">
            <Icon name="Briefcase" size="lg" />
          </div>

          {/* Job Info */}
          <div className="flex-1 space-y-4">
            {/* Title and Category */}
            <div className="space-y-2">
              <h1 className="text-2xl font-bold text-foreground">{job.title}</h1>
              <div className="flex items-center gap-2 text-muted-foreground">
                <Icon name="FolderOpen" size="sm" />
                <span className="text-base capitalize">
                  {job.category?.replace(/[_-]/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Other'}
                </span>
              </div>
            </div>

            {/* Badges and Info */}
            <div className="flex items-center gap-3 flex-wrap">
              {/* Status Badge */}
              <Badge className={`${getStatusBadgeVariant(job.status)} border font-medium px-3 py-1.5`}>
                <Icon name={getStatusIcon(job.status)} size="xs" className="mr-2" />
                {job.status.replace('_', ' ')}
              </Badge>

              {/* Budget Badge */}
              {job.budget && (
                <Badge className="bg-emerald-100 text-emerald-800 border-emerald-200 border px-3 py-1.5">
                  <Icon name="DollarSign" size="xs" className="mr-1" />
                  {job.budget}
                </Badge>
              )}

              {/* Remote Badge */}
              {job.isRemote && (
                <Badge className="bg-blue-100 text-blue-800 border-blue-200 border px-3 py-1.5">
                  <Icon name="MapPin" size="xs" className="mr-2" />
                  Remote
                </Badge>
              )}

              {/* Created Date Badge */}
              <Badge variant="outline" className="border-gray-300 text-gray-700 px-3 py-1.5">
                <Icon name="Calendar" size="xs" className="mr-2" />
                Posted {formatDistanceToNow(new Date(job.createdAt), { addSuffix: true })}
              </Badge>
            </div>
          </div>

          {/* Quick Actions */}
          {(onEdit || onDelete) && (
            <div className="flex items-start gap-2">
              {onEdit && (
                <Button
                  onClick={onEdit}
                  disabled={editLoading || deleteLoading}
                  size="sm"
                  variant="outline"
                  className="p-2"
                >
                  {editLoading ? (
                    <Icon name="Loader2" size="sm" className="animate-spin" />
                  ) : (
                    <Icon name="Edit" size="sm" />
                  )}
                </Button>
              )}
              
              {onDelete && (
                <Button
                  onClick={onDelete}
                  disabled={editLoading || deleteLoading}
                  size="sm"
                  variant="destructive"
                  className="p-2"
                >
                  {deleteLoading ? (
                    <Icon name="Loader2" size="sm" className="animate-spin" />
                  ) : (
                    <Icon name="Trash2" size="sm" />
                  )}
                </Button>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default JobSummaryCard;