import { ReactNode } from 'react';

export interface LoadingProps {
  /** Size of the loading indicator */
  size?: 'sm' | 'md' | 'lg';
  /** Visual variant of the loading indicator */
  variant?: 'spinner' | 'dots' | 'pulse';
  /** Additional CSS classes */
  className?: string;
  /** Whether to take up the full viewport */
  fullScreen?: boolean;
  /** Optional message to display with the loading indicator */
  message?: string | ReactNode;
  /** Color of the loading indicator */
  color?: 'primary' | 'secondary' | 'white' | 'muted' | string;
  /** Whether to show a semi-transparent overlay */
  withOverlay?: boolean;
  /** Additional styles */
  style?: React.CSSProperties;
}

export interface LoadingOverlayProps {
  /** Whether the overlay is visible */
  isLoading: boolean;
  /** Content to be displayed behind the loading overlay */
  children: ReactNode;
  /** Additional CSS classes */
  className?: string;
  /** Optional message to display with the loading indicator */
  message?: string | ReactNode;
  /** Visual variant of the loading indicator */
  variant?: 'spinner' | 'dots' | 'pulse';
  /** Opacity of the overlay (0-1) */
  opacity?: number;
  /** Z-index of the overlay */
  zIndex?: number;
}
