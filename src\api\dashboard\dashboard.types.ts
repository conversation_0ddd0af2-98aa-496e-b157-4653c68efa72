export interface Job {
  id: string;
  title: string;
  description: string;
  status: string;
  budget: number;
  deadline?: string;
  isRemote?: boolean;
  skills?: string[];
  createdAt?: string;
  updatedAt?: string;
  proposals?: {
    items: Array<{
      id: string;
      status: string;
      bidAmount: number;
      coverLetter?: string;
      freelancer: {
        id: string;
        name: string;
        email: string;
        profilePhoto?: string;
        skills?: string[];
      };
    }>;
  };
}

export interface Deliverable {
  id: string;
  title: string;
  description: string;
  status: string;
  dueDate: string;
  submittedAt: string;
  feedback: string;
  rating: number;
  paymentStatus: string;
}

export interface Payment {
  id: string;
  amount: number;
  status: string;
  paymentDate: string;
  paymentMethod: string;
  transactionId: string;
}

export interface Contract {
  id: string;
  title: string;
  status: string;
  startDate: string;
  endDate?: string;
  budget: number;
  freelancer?: {
    id: string;
    name: string;
    email: string;
    profilePhoto?: string;
    skills?: string[];
  };
  client?: {
    id: string;
    name: string;
    email: string;
    profilePhoto?: string;
  };
  deliverables?: {
    items: Deliverable[];
  };
  payments?: {
    items: Payment[];
  };
  job?: {
    id: string;
    title: string;
    description: string;
  };
}

export interface Proposal {
  id: string;
  status: string;
  bidAmount: number;
  coverLetter?: string;
  createdAt: string;
  job: {
    id: string;
    title: string;
    description: string;
    budget: number;
    client: {
      id: string;
      name: string;
      email: string;
      profilePhoto?: string;
    };
  };
  freelancer?: {
    id: string;
    name: string;
    email: string;
    profilePhoto?: string;
    skills?: string[];
  };
}

export interface AdminDashboardData {
  totalJobs: number;
  openJobs: number;
  inProgressJobs: number;
  completedJobs: number;
  totalSkills: number;
  activeSkills: number;
  inactiveSkills: number;
  totalProposals: number;
  recentJobs: Array<{
    id: string;
    title: string;
    status: string;
    createdAt: string;
    client: {
      name: string;
    };
    budget?: number;
  }>;
  recentSkills: Array<{
    id: string;
    name: string;
    isActive: boolean;
    category: string;
    createdAt: string;
  }>;
  totalUsers?: number;
  totalClients?: number;
  totalFreelancers?: number;
  totalAdmins?: number;
  newUsersThisMonth?: number;
  newJobsThisMonth?: number;
  totalJobBudget?: number;
  averageJobBudget?: number;
  cancelledJobs?: number;
  totalJobCategories?: number;
}

export interface GraphQLResponse {
  id: string;
  name: string;
  email: string;
  role: string;
  profilePhoto?: string;
  bio?: string;
  skills?: string[];
  jobs?: {
    items: Job[];
  };
  jobsByClientId?: {
    items: Job[];
  };
  contractsByClientId?: {
    items: Contract[];
  };
  proposals?: {
    items: Proposal[];
  };
  proposalsByFreelancerId?: {
    items: Proposal[];
  };
  clientContracts?: {
    items: Contract[];
  };
  freelancerContracts?: {
    items: Contract[];
  };
  contractsByFreelancerId?: {
    items: Contract[];
  };
  [key: string]: any;
}

