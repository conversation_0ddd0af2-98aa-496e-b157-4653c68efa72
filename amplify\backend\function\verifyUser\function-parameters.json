{"permissions": {"api": {"myvillagefreelance": ["Query", "Mutation", "Subscription"]}, "auth": {"myvillagefreelanceb6232a5f": ["create", "read", "update", "delete"]}}, "lambdaLayers": [], "environmentVariableList": [{"cloudFormationParameterName": "userPoolId", "environmentVariableName": "USER_POOL_ID"}, {"cloudFormationParameterName": "authMyvillagefreelanceb6232A5FUserpoolwebclientid", "environmentVariableName": "AUTH_MYVILLAGEFREELANCEB6232A5F_USERPOOLWEBCLIENTID"}]}