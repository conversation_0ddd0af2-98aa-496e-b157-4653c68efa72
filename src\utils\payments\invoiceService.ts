import { Payment } from '@/types/features/payments/payment.types';
import { Contract } from '@/types/features/contracts/contract.types';

export interface InvoiceData {
  invoiceNumber: string;
  paymentId: string;
  contractId: string;
  transactionId: string;
  amount: number;
  currency: string;
  paidAt: string;
  paymentMethod: string;
  description: string;
  clientData?: {
    id: string;
    name?: string;
    email?: string;
  };
  freelancerData?: {
    id: string;
    name?: string;
    email?: string;
  };
  contractData?: {
    title?: string;
    description?: string;
  };
}

export interface ReceiptData extends InvoiceData {
  receiptNumber: string;
  status: 'PAID' | 'FAILED' | 'CANCELLED' | 'REFUNDED';
}

/**
 * Service for generating payment receipts and invoices
 */
export class InvoiceService {
  private static instance: InvoiceService;
  
  private constructor() {}
  
  public static getInstance(): InvoiceService {
    if (!InvoiceService.instance) {
      InvoiceService.instance = new InvoiceService();
    }
    return InvoiceService.instance;
  }

  /**
   * Generate invoice number based on payment ID and timestamp
   */
  generateInvoiceNumber(paymentId: string): string {
    const timestamp = Date.now().toString(36).toUpperCase();
    const shortPaymentId = paymentId.slice(-8).toUpperCase();
    return `INV-${timestamp}-${shortPaymentId}`;
  }

  /**
   * Generate receipt number based on transaction ID
   */
  generateReceiptNumber(transactionId: string): string {
    const timestamp = Date.now().toString(36).toUpperCase();
    const shortTransactionId = transactionId.slice(-8).toUpperCase();
    return `RCP-${timestamp}-${shortTransactionId}`;
  }

  /**
   * Create invoice data from payment and contract information
   */
  createInvoiceData(
    payment: Payment,
    contract?: Contract,
    clientData?: { id: string; name?: string; email?: string },
    freelancerData?: { id: string; name?: string; email?: string }
  ): InvoiceData {
    return {
      invoiceNumber: this.generateInvoiceNumber(payment.id),
      paymentId: payment.id,
      contractId: payment.contractId,
      transactionId: payment.transactionId || '',
      amount: payment.amount,
      currency: payment.currency || 'USD',
      paidAt: payment.paidAt || new Date().toISOString(),
      paymentMethod: payment.method || 'STRIPE',
      description: payment.description || `Payment for contract ${payment.contractId}`,
      clientData,
      freelancerData,
      contractData: contract ? {
        title: contract.title,
        description: contract.description
      } : undefined
    };
  }

  /**
   * Create receipt data from payment information
   */
  createReceiptData(
    payment: Payment,
    transactionId: string,
    contract?: Contract,
    clientData?: { id: string; name?: string; email?: string },
    freelancerData?: { id: string; name?: string; email?: string }
  ): ReceiptData {
    const invoiceData = this.createInvoiceData(payment, contract, clientData, freelancerData);
    
    return {
      ...invoiceData,
      receiptNumber: this.generateReceiptNumber(transactionId),
      transactionId,
      status: payment.status as 'PAID' | 'FAILED' | 'CANCELLED' | 'REFUNDED'
    };
  }

  /**
   * Generate HTML receipt for email or display
   */
  generateReceiptHTML(receiptData: ReceiptData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Payment Receipt - ${receiptData.receiptNumber}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; color: #333; }
          .header { border-bottom: 2px solid #007bff; padding-bottom: 20px; margin-bottom: 30px; }
          .company-name { font-size: 24px; font-weight: bold; color: #007bff; }
          .receipt-title { font-size: 20px; margin-top: 10px; }
          .receipt-info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
          .payment-details { margin: 20px 0; }
          .amount { font-size: 24px; font-weight: bold; color: #28a745; }
          .status { padding: 5px 10px; border-radius: 15px; color: white; display: inline-block; }
          .status.paid { background-color: #28a745; }
          .status.failed { background-color: #dc3545; }
          .status.cancelled { background-color: #6c757d; }
          .footer { margin-top: 40px; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="company-name">MyVillage Freelance</div>
          <div class="receipt-title">Payment Receipt</div>
        </div>
        
        <div class="receipt-info">
          <p><strong>Receipt Number:</strong> ${receiptData.receiptNumber}</p>
          <p><strong>Invoice Number:</strong> ${receiptData.invoiceNumber}</p>
          <p><strong>Date:</strong> ${new Date(receiptData.paidAt).toLocaleDateString()}</p>
          <p><strong>Status:</strong> <span class="status ${receiptData.status.toLowerCase()}">${receiptData.status}</span></p>
        </div>
        
        <div class="payment-details">
          <h3>Payment Details</h3>
          <p><strong>Amount:</strong> <span class="amount">$${receiptData.amount.toFixed(2)} ${receiptData.currency}</span></p>
          <p><strong>Payment Method:</strong> ${receiptData.paymentMethod}</p>
          <p><strong>Transaction ID:</strong> ${receiptData.transactionId}</p>
          <p><strong>Description:</strong> ${receiptData.description}</p>
        </div>
        
        ${receiptData.contractData ? `
        <div class="contract-details">
          <h3>Contract Information</h3>
          <p><strong>Contract ID:</strong> ${receiptData.contractId}</p>
          <p><strong>Title:</strong> ${receiptData.contractData.title || 'N/A'}</p>
          ${receiptData.contractData.description ? `<p><strong>Description:</strong> ${receiptData.contractData.description}</p>` : ''}
        </div>
        ` : ''}
        
        ${receiptData.clientData || receiptData.freelancerData ? `
        <div class="parties">
          <h3>Transaction Parties</h3>
          ${receiptData.clientData ? `<p><strong>Client:</strong> ${receiptData.clientData.name || receiptData.clientData.id}</p>` : ''}
          ${receiptData.freelancerData ? `<p><strong>Freelancer:</strong> ${receiptData.freelancerData.name || receiptData.freelancerData.id}</p>` : ''}
        </div>
        ` : ''}
        
        <div class="footer">
          <p>Thank you for using MyVillage Freelance!</p>
          <p>This is an automated receipt. For questions, please contact our support team.</p>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Store receipt data (this would typically save to database or file storage)
   */
  async storeReceipt(receiptData: ReceiptData): Promise<string> {
    try {
      // In a real implementation, you would:
      // 1. Save receipt to database
      // 2. Generate PDF
      // 3. Store in cloud storage (S3)
      // 4. Return download URL
      
      console.log('Receipt generated:', {
        receiptNumber: receiptData.receiptNumber,
        paymentId: receiptData.paymentId,
        amount: receiptData.amount
      });
      
      return `/api/receipts/${receiptData.receiptNumber}`;
    } catch (error) {
      console.error('Error storing receipt:', error);
      throw new Error('Failed to store receipt');
    }
  }

  /**
   * Generate and store receipt for a successful payment
   */
  async generatePaymentReceipt(
    payment: Payment,
    transactionId: string,
    contract?: Contract,
    clientData?: { id: string; name?: string; email?: string },
    freelancerData?: { id: string; name?: string; email?: string }
  ): Promise<{ receiptData: ReceiptData; receiptUrl: string }> {
    const receiptData = this.createReceiptData(
      payment,
      transactionId,
      contract,
      clientData,
      freelancerData
    );
    
    const receiptUrl = await this.storeReceipt(receiptData);
    
    return { receiptData, receiptUrl };
  }
}

export const invoiceService = InvoiceService.getInstance();