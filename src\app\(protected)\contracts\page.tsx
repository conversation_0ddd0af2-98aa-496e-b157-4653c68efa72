"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState, useCallback } from "react";
import Link from "next/link";
import { formatDistanceToNow } from "date-fns";

import { useAuth } from "@/lib/auth/AuthContext";
import { Button } from "@/components/ui/Button";
import useToaster from "@/hooks/useToaster";
import contractService from "@/api/contracts/contract.service";
import {
  Contract,
  ContractStatus,
  ContractType,
} from "@/types/features/contracts/contract.types";
import { Table } from "@/components/ui/Table";
import { Card, CardContent } from "@/components/ui/Card";
import { ContentHeader } from "@/components/layout/ContentHeader";
import { Badge } from "@/components/ui/Badge";
import { Icon } from "@/components/ui/Icon";
import { TableData as BaseTableData } from "@/types/components/Table";

type TableData = Contract & BaseTableData;

const ITEMS_PER_PAGE = 5;

const statusOptions = [
  { value: "all", label: "All Contracts" },
  { value: ContractStatus.ACTIVE, label: "Active" },
  { value: ContractStatus.DRAFT, label: "Pending" },
  { value: ContractStatus.COMPLETED, label: "Completed" },
  { value: ContractStatus.DISPUTED, label: "Disputed" },
  { value: ContractStatus.CANCELLED, label: "Cancelled" },
];

const getStatusBadgeVariant = (status: ContractStatus) => {
  switch (status) {
    case ContractStatus.ACTIVE:
      return "success";
    case ContractStatus.DRAFT:
      return "warning";
    case ContractStatus.COMPLETED:
      return "outline";
    case ContractStatus.DISPUTED:
      return "destructive";
    case ContractStatus.CANCELLED:
      return "secondary";
    default:
      return "default";
  }
};

const getContractTypeLabel = (type: ContractType) => {
  switch (type) {
    case ContractType.FIXED_PRICE:
      return "Fixed Price";
    case ContractType.HOURLY:
      return "Hourly";
    default:
      return type;
  }
};

export default function ContractsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, isAuthenticated } = useAuth();
  const { showError } = useToaster();

  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [contracts, setContracts] = useState<TableData[]>([]);
  const [totalItems, setTotalItems] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<ContractStatus | "all">(
    () => (searchParams.get("status") as ContractStatus) || "all"
  );
  const [sortBy, setSortBy] = useState<{
    field: string;
    direction: "asc" | "desc";
  }>(() => {
    const sortParam = searchParams.get("sort");
    if (sortParam) {
      const [field, direction] = sortParam.split(":");
      return { field, direction: direction as "asc" | "desc" };
    }
    return { field: "createdAt", direction: "desc" };
  });
  const [mounted, setMounted] = useState(false);
  const [showFilters, setShowFilters] = useState(false);

  const memoizedShowToast = useCallback(
    (message: string) => {
      showError(message);
    },
    [showError]
  );

  const columns = [
    {
      header: "Contract",
      accessor: "title",
      cell: (value: unknown, contract: TableData) => (
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0 h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
            <Icon name="FileText" className="h-5 w-5 text-primary" />
          </div>
          <div className="min-w-0 flex-1">
            <p className="text-sm font-medium truncate">
              {contract.title ||
                contract.job?.title ||
                `Contract #${contract.id?.substring(0, 8) || ""}`}
            </p>
            <p className="text-xs text-muted-foreground truncate">
              {contract.job?.title ? contract.job.title : ""}
            </p>
          </div>
        </div>
      ),

      sortable: true,
      onSort: () => handleSort("title"),
      sortDirection: sortBy.field === "title" ? sortBy.direction : undefined,
    },
    {
      header: "Type",
      accessor: "type",
      cell: (value: unknown, contract: TableData) => (
        <div className="text-sm">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {contract.type ? getContractTypeLabel(contract.type) : "N/A"}
          </span>
        </div>
      ),
      sortable: true,
      onSort: () => handleSort("type"),
      sortDirection: sortBy.field === "type" ? sortBy.direction : undefined,
    },
    {
      header: "Amount",
      accessor: "budget",
      cell: (value: unknown, contract: TableData) => (
        <div className="text-sm font-medium">
          {contract.budget
            ? `$${Number(contract.budget).toLocaleString()}`
            : "TBD"}
          <span className="text-xs text-muted-foreground block">
            {contract.type === "HOURLY"
              ? "per hour"
              : contract.type === "FIXED_PRICE"
              ? "fixed price"
              : "amount"}
          </span>
        </div>
      ),
      sortable: true,
      onSort: () => handleSort("budget"),
      sortDirection: sortBy.field === "budget" ? sortBy.direction : undefined,
    },
    {
      header: "Status",
      accessor: "status",
      cell: (value: unknown, contract: TableData) => (
        <Badge
          variant={getStatusBadgeVariant(contract.status)}
          className="capitalize"
        >
          {contract.status.toLowerCase()}
        </Badge>
      ),
      sortable: true,
      onSort: () => handleSort("status"),
      sortDirection: sortBy.field === "status" ? sortBy.direction : undefined,
    },
    {
      header: "Created",
      accessor: "createdAt",
      cell: (value: unknown, contract: TableData) => (
        <div className="text-sm text-muted-foreground">
          {formatDistanceToNow(new Date(contract.createdAt), {
            addSuffix: true,
          })}
        </div>
      ),
      sortable: true,
      onSort: () => handleSort("createdAt"),
      sortDirection:
        sortBy.field === "createdAt" ? sortBy.direction : undefined,
    },
    {
      header: "Actions",
      accessor: "id",
      cell: (value: unknown, contract: TableData) => {
        const onView = (e: React.MouseEvent) => {
          e.stopPropagation();
          router.push(`/contracts/${contract.id}`);
        };

        return (
          <div className="flex space-x-2">
            <Button
              size="sm"
              variant="ghost"
              onClick={onView}
              className="h-8 px-2 py-1"
              title="View contract details"
            >
              <Icon name="Eye" className="h-4 w-4 mr-1" />
            </Button>
          </div>
        );
      },
      className: "text-right",
    },
  ];

  const fetchContracts = useCallback(async () => {
    if (!user) return;

    setIsRefreshing(true);
    setError(null);

    try {
      const page = parseInt(searchParams.get("page") || "1");
      const status = statusFilter === "all" ? undefined : statusFilter;

      const response = await contractService.listContracts(
        { status },
        ITEMS_PER_PAGE,
        undefined
      );

      let allContracts = response.items || [];

      // Apply additional client-side filtering if needed
      if (statusFilter !== "all") {
        allContracts = allContracts.filter(
          (contract) => contract.status === statusFilter
        );
      }

      // Apply client-side sorting
      allContracts = [...allContracts].sort((a, b) => {
        if (sortBy.field === "createdAt") {
          const dateA = new Date(a.createdAt).getTime();
          const dateB = new Date(b.createdAt).getTime();
          return sortBy.direction === "asc" ? dateA - dateB : dateB - dateA;
        }
        // Add additional sorting logic for other fields if needed
        return 0;
      });

      const totalItems = allContracts.length;
      const totalPages = Math.ceil(totalItems / ITEMS_PER_PAGE) || 1;

      const validPage = Math.min(Math.max(1, page), totalPages);

      const startIndex = (validPage - 1) * ITEMS_PER_PAGE;
      const endIndex = Math.min(startIndex + ITEMS_PER_PAGE, totalItems);
      const paginatedContracts = allContracts.slice(startIndex, endIndex);

      setContracts(paginatedContracts as TableData[]);
      setTotalItems(totalItems);
      setCurrentPage(validPage);

      if (page !== validPage) {
        const params = new URLSearchParams(searchParams);
        params.set("page", validPage.toString());
        router.push(`?${params.toString()}`, { scroll: false });
      }
    } catch (err) {
      console.error("Error fetching contracts:", err);
      const errorMessage = "Failed to load contracts. Please try again.";
      setError(errorMessage);
      memoizedShowToast(errorMessage);
      setContracts([]);
      setTotalItems(0);
      setCurrentPage(1);
      if (searchParams.get("page") !== "1") {
        const params = new URLSearchParams(searchParams);
        params.set("page", "1");
        router.push(`?${params.toString()}`, { scroll: false });
      }
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [user, statusFilter, sortBy, searchParams, router, memoizedShowToast]);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (user) {
      fetchContracts();
    }
  }, [user, statusFilter, sortBy, searchParams, fetchContracts]);

  const handlePageChange = (page: number) => {
    if (page !== currentPage) {
      const params = new URLSearchParams(searchParams);
      params.set("page", page.toString());
      router.push(`?${params.toString()}`, { scroll: false });
    }
  };

  const handleSort = (field: string) => {
    setSortBy((prev) => ({
      field,
      direction:
        prev.field === field && prev.direction === "asc" ? "desc" : "asc",
    }));
  };

  const handleStatusFilterChange = (value: string) => {
    // Cast the value to ContractStatus | 'all' since we know it comes from our statusOptions
    setStatusFilter(value as ContractStatus | 'all');
    const params = new URLSearchParams(searchParams);
    params.set("page", "1");
    router.push(`?${params.toString()}`, { scroll: false });
  };

  const handleSortChange = useCallback((value: string) => {
    const [field, direction] = value.split("_");
    setSortBy({
      field,
      direction: direction as "asc" | "desc",
    });
  }, []);

  useEffect(() => {
    if (mounted && !isAuthenticated) {
      router.push("/login");
    }
  }, [mounted, isAuthenticated, router]);

  if (!mounted || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Icon name="Loader2" size="xl" className="animate-spin text-primary" />
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="h-10 w-64 bg-muted/30 rounded-lg animate-pulse mb-4" />
        <div className="h-10 bg-muted/30 rounded-lg animate-pulse mb-4" />
        {[...Array(5)].map((_, i) => (
          <div key={i} className="h-16 bg-muted/30 rounded-lg animate-pulse" />
        ))}
      </div>
    );
  }

  return (
    <div className="w-full max-w-7xl mx-auto p-4 sm:p-6 space-y-6">
      <div className="flex justify-between items-center mb-6">
        <ContentHeader
          title="My Contracts"
          subtitle="Manage your contracts and agreements"
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Contracts", current: true },
          ]}
        />
        <div className="flex items-center space-x-3">
          <Button
            size="sm"
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Icon name="Filter" size="sm" />
            <span>Filters</span>
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        {showFilters && (
          <Card className="mb-6">
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-muted-foreground mb-1">
                    Status
                  </label>
                  <select
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    value={statusFilter}
                    onChange={(e) => handleStatusFilterChange(e.target.value)}
                  >
                    {statusOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-muted-foreground mb-1">
                    Type
                  </label>
                  <select
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    value={statusFilter}
                  >
                    <option value="all">All Types</option>
                    <option value="FIXED_PRICE">Fixed Price</option>
                    <option value="HOURLY">Hourly</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-muted-foreground mb-1">
                    Sort By
                  </label>
                  <select
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    value={sortByValue}
                    onChange={(e) => handleSortChange(e.target.value)}
                  >
                    <option value="createdAt_desc">Newest</option>
                    <option value="createdAt_asc">Oldest</option>
                    <option value="title_asc">Title (A-Z)</option>
                    <option value="title_desc">Title (Z-A)</option>
                    <option value="budget_asc">Budget (Low to High)</option>
                    <option value="budget_desc">Budget (High to Low)</option>
                  </select>
                </div>
              </div>
              <div className="mt-4 flex justify-end space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    setStatusFilter("all");
                    setSortByValue("createdAt_desc");
                    setSortBy({ field: "createdAt", direction: "desc" });
                  }}
                >
                  Reset
                </Button>
                <Button
                  size="sm"
                  onClick={() => {
                    fetchContracts();
                    setShowFilters(false);
                  }}
                >
                  Apply Filters
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <Card>
        <CardContent className="p-0">
          {error ? (
            <div className="p-6 text-center">
              <p className="text-destructive mb-4">{error}</p>
              <Button
                variant="outline"
                onClick={fetchContracts}
                disabled={isRefreshing}
              >
                {isRefreshing ? "Retrying..." : "Retry"}
              </Button>
            </div>
          ) : contracts.length > 0 ? (
            <Table
              data={contracts}
              columns={columns}
              emptyState={{
                title: "No contracts found",
                description:
                  statusFilter === "all"
                    ? "You don't have any contracts yet. Get started by creating a new contract."
                    : `No ${statusFilter.toLowerCase()} contracts found. Try adjusting your filters.`,
                icon: "FileText" as const,
                action: (
                  <div className="space-x-3">
                    <Button onClick={() => router.push("/contracts/new")}>
                      <Icon name="Plus" className="mr-2 h-4 w-4" />
                      Create Contract
                    </Button>
                    {statusFilter !== "all" && (
                      <Button
                        variant="outline"
                        onClick={() => setStatusFilter("all")}
                      >
                        Clear Filters
                      </Button>
                    )}
                  </div>
                ),
              }}
              isLoading={isLoading}
              pagination={{
                enabled: true,
                currentPage,
                totalItems,
                pageSize: ITEMS_PER_PAGE,
                totalPages: Math.ceil(totalItems / ITEMS_PER_PAGE),
                onPageChange: handlePageChange,
                showFirstLast: true,
                showPrevNext: true,
                maxVisiblePages: 5,
              }}
            />
          ) : (
            <div className="p-8 text-center">
              <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-muted">
                <Icon
                  name="FileText"
                  className="h-6 w-6 text-muted-foreground"
                />
              </div>
              <h3 className="mt-4 text-sm font-medium">No contracts found</h3>
              <p className="mt-1 text-sm text-muted-foreground">
                {statusFilter === "all"
                  ? "Get started by creating a new contract."
                  : `No ${statusOptions
                      .find((opt) => opt.value === statusFilter)
                      ?.label.toLowerCase()} contracts found.`}
              </p>
              <div className="mt-6">
                <Button asChild>
                  <Link href="/contracts/new">
                    <Icon name="Plus" className="mr-2 h-4 w-4" />
                    New Contract
                  </Link>
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
