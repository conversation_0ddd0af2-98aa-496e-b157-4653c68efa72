import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/Card';
import { Icon } from '@/components/ui/Icon';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { User } from '@/types/features/user/user.types';

export interface BiographySkillsCardProps {
  /** User data to display */
  user: User;
  /** Whether to show add skill button for admin editing */
  showAddSkill?: boolean;
  /** Callback for add skill action */
  onAddSkill?: () => void;
  /** Additional CSS classes */
  className?: string;
  /** Loading state */
  loading?: boolean;
}

export const BiographySkillsCard: React.FC<BiographySkillsCardProps> = ({
  user,
  showAddSkill = false,
  onAddSkill,
  className = '',
  loading = false,
}) => {
  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Icon name="FileText" size="sm" />
            Biography & Skills
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="animate-pulse space-y-4">
            <div className="space-y-2">
              <div className="h-4 bg-gray-300 rounded w-1/4"></div>
              <div className="h-20 bg-gray-300 rounded"></div>
            </div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-300 rounded w-1/4"></div>
              <div className="flex gap-2">
                <div className="h-6 bg-gray-300 rounded w-16"></div>
                <div className="h-6 bg-gray-300 rounded w-20"></div>
                <div className="h-6 bg-gray-300 rounded w-18"></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Icon name="FileText" size="sm" />
          Biography & Skills
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Biography Section */}
        {user.bio ? (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Icon name="Quote" size="sm" className="text-blue-500" />
              <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
                About
              </h4>
            </div>
            <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
              <p className="text-foreground leading-relaxed text-sm">{user.bio}</p>
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Icon name="Quote" size="sm" className="text-gray-400" />
              <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
                About
              </h4>
            </div>
            <div className="p-4 bg-gray-50 rounded-lg border border-gray-200 text-center">
              <Icon name="FileText" size="md" className="mx-auto text-gray-400 mb-2" />
              <p className="text-muted-foreground text-sm">No biography available</p>
            </div>
          </div>
        )}

        {/* Skills Section */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Icon name="Zap" size="sm" className="text-purple-500" />
              <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
                Skills {user.skills && user.skills.length > 0 && (
                  <span className="text-purple-600">({user.skills.length})</span>
                )}
              </h4>
            </div>
            {showAddSkill && onAddSkill && (
              <Button
                onClick={onAddSkill}
                size="sm"
                variant="outline"
                className="text-xs h-7 px-2"
              >
                <Icon name="Plus" size="xs" className="mr-1" />
                Add Skill
              </Button>
            )}
          </div>

          {user.skills && user.skills.length > 0 ? (
            <div className="space-y-4">
              {/* Skills Chips */}
              <div className="flex flex-wrap gap-2">
                {user.skills.map((skill, index) => (
                  <Badge 
                    key={index} 
                    className="bg-purple-100 text-purple-800 border-purple-200 border px-3 py-1.5 font-medium hover:bg-purple-200 transition-colors"
                  >
                    <Icon name="Zap" size="xs" className="mr-1" />
                    {skill}
                  </Badge>
                ))}
              </div>
            </div>
          ) : (
            <div className="p-4 bg-gray-50 rounded-lg border border-gray-200 text-center">
              <Icon name="Zap" size="md" className="mx-auto text-gray-400 mb-2" />
              <p className="text-muted-foreground text-sm mb-2">No skills listed</p>
              {showAddSkill && onAddSkill && (
                <Button
                  onClick={onAddSkill}
                  size="sm"
                  variant="outline"
                  className="text-xs"
                >
                  <Icon name="Plus" size="xs" className="mr-1" />
                  Add First Skill
                </Button>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default BiographySkillsCard;