# Contributing to MyVillage Freelance Frontend

Welcome! We're excited to have you contribute to our project. This document provides guidelines for contributing to the codebase, with a focus on TypeScript best practices.

## TypeScript Guidelines

### Type vs Interface

- Use `type` for:
  - Union types (e.g., `type Status = 'active' | 'inactive'`)
  - Intersection types (e.g., `type Combined = A & B`)
  - Simple object types that don't need extension
  - Type aliases for primitives

- Use `interface` for:
  - Public API definitions
  - Declaration merging (when needed)
  - Extending existing types (e.g., `interface Extended extends Base`)

### Type Safety

- Avoid `any` type - always prefer specific types
- Use `unknown` instead of `any` when the type is truly unknown
- Use type guards to narrow down types when needed
- Prefer readonly properties when the value shouldn't change after initialization

### Naming Conventions

- Types should be in PascalCase (e.g., `UserProfile`, `ApiResponse`)
- Use descriptive names that reflect the purpose of the type
- Suffix DTOs with `Dto` (e.g., `CreateUserDto`)
- Suffix enum types with `Enum` (e.g., `UserRoleEnum`)

### Documentation

- Add JSDoc comments for all public types and interfaces
- Document complex types with examples when necessary
- Use `@deprecated` for types that should no longer be used
- Document any non-obvious type decisions

### API Types

- Use consistent response types (e.g., `ApiResponse<T>`)
- Document all API endpoints with request/response types
- Handle error responses with proper error types

## Code Style

- Use 2 spaces for indentation
- Use single quotes for strings
- Always include semicolons
- Sort imports alphabetically
- Keep lines under 100 characters

## Commit Messages

Follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### Types:
- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation only changes
- `style`: Changes that do not affect the meaning of the code
- `refactor`: A code change that neither fixes a bug nor adds a feature
- `perf`: A code change that improves performance
- `test`: Adding missing tests or correcting existing tests
- `chore`: Changes to the build process or auxiliary tools

## Pull Requests

1. Fork the repository and create your branch from `main`
2. Make your changes following the guidelines above
3. Add tests for your changes
4. Ensure all tests pass
5. Update the documentation as needed
6. Submit a pull request with a clear description of your changes

## Code Review

- All pull requests must be reviewed by at least one maintainer
- Address all review comments before merging
- Keep pull requests focused on a single feature or fix
- Update documentation and tests with your changes

## Development Setup

1. Install dependencies:
   ```bash
   npm install
   ```

2. Start the development server:
   ```bash
   npm run dev
   ```

3. Run tests:
   ```bash
   npm test
   ```

## License

By contributing, you agree that your contributions will be licensed under the project's [LICENSE](LICENSE) file.
