import crypto from 'crypto';
import { StripeWebhookEvent, PaymentIntentMetadata } from '@/types/features/payments/payment.types';

/**
 * Utility for validating and processing Stripe webhooks securely
 */
export class WebhookValidator {
  private static instance: WebhookValidator;

  private constructor() { }

  public static getInstance(): WebhookValidator {
    if (!WebhookValidator.instance) {
      WebhookValidator.instance = new WebhookValidator();
    }
    return WebhookValidator.instance;
  }

  /**
   * Validates webhook signature from Stripe
   */
  validateWebhookSignature(
    payload: string,
    signature: string,
    secret: string,
    tolerance: number = 300
  ): boolean {
    try {
      const elements = signature.split(',');
      let timestamp: number | undefined;
      let signatureHash: string | undefined;

      for (const element of elements) {
        const [key, value] = element.split('=');
        if (key === 't') {
          timestamp = parseInt(value, 10);
        } else if (key === 'v1') {
          signatureHash = value;
        }
      }

      if (!timestamp || !signatureHash) {
        console.error('Invalid webhook signature format');
        return false;
      }

      const currentTime = Math.floor(Date.now() / 1000);
      if (Math.abs(currentTime - timestamp) > tolerance) {
        console.error('Webhook timestamp outside tolerance');
        return false;
      }

      const signedPayload = `${timestamp}.${payload}`;
      const expectedSignature = crypto
        .createHmac('sha256', secret)
        .update(signedPayload, 'utf8')
        .digest('hex');

      return crypto.timingSafeEqual(
        Buffer.from(signatureHash, 'hex'),
        Buffer.from(expectedSignature, 'hex')
      );
    } catch (error) {
      console.error('Error validating webhook signature:', error);
      return false;
    }
  }

  /**
   * Validates webhook event structure
   */
  validateWebhookEvent(event: any): event is StripeWebhookEvent {
    return (
      event &&
      typeof event === 'object' &&
      typeof event.id === 'string' &&
      typeof event.type === 'string' &&
      typeof event.data === 'object' &&
      event.data.object &&
      typeof event.created === 'number' &&
      typeof event.livemode === 'boolean'
    );
  }

  /**
   * Extracts and validates payment intent metadata
   */
  extractPaymentMetadata(paymentIntent: any): PaymentIntentMetadata | null {
    try {
      if (!paymentIntent || !paymentIntent.metadata) {
        return null;
      }

      const metadata = paymentIntent.metadata;

      if (!metadata.contractId || !metadata.clientId) {
        console.error('Missing required metadata fields:', metadata);
        return null;
      }

      return {
        contractId: metadata.contractId,
        clientId: metadata.clientId,
        freelancerId: metadata.freelancerId,
        clientName: metadata.clientName,
        clientEmail: metadata.clientEmail,
        retryAttempt: metadata.retryAttempt,
        originalAmount: metadata.originalAmount,
      };
    } catch (error) {
      console.error('Error extracting payment metadata:', error);
      return null;
    }
  }

  /**
   * Validates payment intent amount against expected amount
   */
  validatePaymentAmount(
    paymentIntent: any,
    expectedAmount: number,
    tolerance: number = 1
  ): boolean {
    if (!paymentIntent || typeof paymentIntent.amount !== 'number') {
      return false;
    }

    const actualAmount = paymentIntent.amount;
    const expectedAmountCents = Math.round(expectedAmount * 100);

    return Math.abs(actualAmount - expectedAmountCents) <= tolerance;
  }

  /**
   * Sanitizes webhook event for logging
   */
  sanitizeWebhookEvent(event: StripeWebhookEvent): any {
    const sanitized = JSON.parse(JSON.stringify(event));

    if (sanitized.data && sanitized.data.object) {
      const obj = sanitized.data.object;

      if (obj.payment_method && obj.payment_method.card) {
        obj.payment_method.card = {
          brand: obj.payment_method.card.brand,
          last4: obj.payment_method.card.last4,
          exp_month: '**',
          exp_year: '****',
        };
      }

      if (obj.customer) {
        if (typeof obj.customer === 'string') {
          obj.customer = 'cus_****';
        } else if (obj.customer.email) {
          obj.customer.email = this.maskEmail(obj.customer.email);
        }
      }

      if (obj.charges && obj.charges.data) {
        obj.charges.data = obj.charges.data.map((charge: any) => ({
          ...charge,
          source: charge.source ? { last4: charge.source.last4, brand: charge.source.brand } : null,
        }));
      }
    }

    return sanitized;
  }

  /**
   * Masks email address for logging
   */
  private maskEmail(email: string): string {
    const [username, domain] = email.split('@');
    if (!domain) return '****@****';

    const maskedUsername = username.length > 2
      ? username.substring(0, 2) + '*'.repeat(username.length - 2)
      : '*'.repeat(username.length);

    return `${maskedUsername}@${domain}`;
  }

  /**
   * Gets webhook event processing priority
   */
  getEventPriority(eventType: string): 'high' | 'medium' | 'low' {
    const highPriorityEvents = [
      'payment_intent.succeeded',
      'payment_intent.payment_failed',
      'payment_intent.canceled',
      'charge.dispute.created',
    ];

    const mediumPriorityEvents = [
      'payment_intent.created',
      'payment_intent.requires_action',
      'charge.succeeded',
      'charge.failed',
    ];

    if (highPriorityEvents.includes(eventType)) {
      return 'high';
    } else if (mediumPriorityEvents.includes(eventType)) {
      return 'medium';
    } else {
      return 'low';
    }
  }

  /**
   * Checks if webhook event is duplicate
   */
  isDuplicateEvent(eventId: string, processedEvents: Set<string>): boolean {
    return processedEvents.has(eventId);
  }

  /**
   * Validates webhook event timing
   */
  isEventTimely(event: StripeWebhookEvent, maxAgeMinutes: number = 30): boolean {
    const eventTime = event.created * 1000;
    const currentTime = Date.now();
    const maxAge = maxAgeMinutes * 60 * 1000;

    return (currentTime - eventTime) <= maxAge;
  }
}

export const webhookValidator = WebhookValidator.getInstance();

/**
 * Middleware-style webhook processor
 */
export class WebhookProcessor {
  private processedEvents = new Set<string>();
  private validator = WebhookValidator.getInstance();

  /**
   * Process webhook with full validation pipeline
   */
  async processWebhook(
    payload: string,
    signature: string,
    secret: string,
    handler: (event: StripeWebhookEvent) => Promise<void>
  ): Promise<{ success: boolean; error?: string }> {
    try {
      if (!this.validator.validateWebhookSignature(payload, signature, secret)) {
        return { success: false, error: 'Invalid webhook signature' };
      }

      const event = JSON.parse(payload);
      if (!this.validator.validateWebhookEvent(event)) {
        return { success: false, error: 'Invalid webhook event structure' };
      }

      if (this.validator.isDuplicateEvent(event.id, this.processedEvents)) {
        console.log(`Duplicate webhook event ignored: ${event.id}`);
        return { success: true };
      }

      if (!this.validator.isEventTimely(event)) {
        return { success: false, error: 'Webhook event too old' };
      }

      await handler(event);

      this.processedEvents.add(event.id);

      if (this.processedEvents.size > 1000) {
        const eventsArray = Array.from(this.processedEvents);
        this.processedEvents = new Set(eventsArray.slice(-1000));
      }

      return { success: true };
    } catch (error) {
      console.error('Webhook processing error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}