import { HTMLAttributes } from 'react';

export interface BadgeProps extends HTMLAttributes<HTMLDivElement> {
  /** Visual style variant */
  variant?: 'default' | 'secondary' | 'destructive' | 'outline' | 'success' | 'warning' | 'info';
  /** Size of the badge */
  size?: 'sm' | 'md' | 'lg';
  /** Whether the badge should have rounded corners */
  rounded?: boolean;
  /** Whether the badge should be a dot */
  dot?: boolean;
  /** Color of the badge (overrides variant color) */
  color?: string;
  /** Background color of the badge (overrides variant background) */
  bgColor?: string;
  /** Custom class name for the dot */
  dotClassName?: string;
  /** Whether the badge should be interactive (hover/focus states) */
  interactive?: boolean;
}
