/**
 * Animation presets using Tailwind CSS classes
 * These classes can be directly applied to your components
 */

export const animations = {
  fadeIn: 'opacity-0 animate-fade-in',
  slideUp: 'translate-y-4 opacity-0 animate-slide-up',
  hoverScale: 'transition-transform duration-300 hover:scale-[1.02]',
  hoverScaleSm: 'transition-transform duration-300 hover:scale-[1.01]',
  linkHover: 'relative after:absolute after:bottom-0 after:left-0 after:h-px after:w-0 after:bg-current after:transition-all after:duration-300 hover:after:w-full',
  cardHover: 'transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5',
  buttonPress: 'active:scale-95 transition-transform',
  staggerContainer: 'space-y-4',
  staggerChild: 'opacity-0 animate-fade-in-up',
};

/**
 * Animation delay utility for staggered animations
 * @param index - The index of the item
 * @param step - The delay step in ms (default: 100ms)
 * @returns Object with CSS properties for the animation
 */
export const staggerDelay = (index: number, step = 100) => ({
  animationDelay: `${index * step}ms`,
});

/**
 * Intersection Observer options for scroll animations
 */
export const observerOptions = {
  root: null,
  rootMargin: '0px',
  threshold: 0.1,
};
