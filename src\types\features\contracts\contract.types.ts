import { Job } from '../jobs/job.types';
import { Proposal } from '../proposals/proposal.types';
import { UserRole } from '../auth/auth.types';

export type {
  Payment,
  PaymentStatus,
  PaymentMethod,
  PaymentSchedule,
  PaymentScheduleStatus
} from '../payments/payment.types';

export interface ValidationResult {
  isValid: boolean;
  message?: string;
  code?: string;
}

export interface ContractValidationContext {
  contract: Contract | ExtendedContract;
  userRole: UserRole;
  userId: string;
}

export enum ContractStatus {
  DRAFT = 'DRAFT',
  PENDING_FREELANCER_ACCEPTANCE = 'PENDING_FREELANCER_ACCEPTANCE',
  ACTIVE = 'ACTIVE',
  WORK_SUBMITTED = 'WORK_SUBMITTED',
  REVISIONS_REQUESTED = 'REVISIONS_REQUESTED',
  COMPLETED = 'COMPLETED',
  PAID = 'PAID',
  CANCELLED = 'CANCELLED',
  DISPUTED = 'DISPUTED'
}

export enum ContractType {
  FIXED_PRICE = 'FIXED_PRICE',
  HOURLY = 'HOURLY'
}

export interface Contract {
  id: string;
  jobId: string;
  job?: Job;
  proposalId: string;
  proposal?: Proposal;
  clientId: string;
  freelancerId: string;
  title: string;
  description: string;
  type: ContractType;
  status: ContractStatus;
  terms: string;
  startDate: string;
  endDate?: string;
  budget: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateContractDto {
  jobId: string;
  proposalId: string;
  title: string;
  description: string;
  type: ContractType;
  status: ContractStatus;
  terms: string;
  startDate: string;
  endDate?: string;
  budget: number;
  clientId: string;
  freelancerId: string;
}

export interface UpdateContractDto {
  status?: ContractStatus;
  endDate?: string;
  terms?: string;
}

export type ApiResponse<T> = {
  data: T;
  message?: string;
  success: boolean;
};

export type PaginatedResponse<T> = {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
};

export type ContractResponse = ApiResponse<Contract>;
export type ContractsResponse = PaginatedResponse<Contract>;

export interface ContractCardProps {
  contract: Contract;
  onView?: (contract: Contract) => void;
  onUpdateStatus?: (contractId: string, status: ContractStatus) => void;
  className?: string;
}

/**
 * Date range filter for contract queries
 */
export interface DateRangeFilter {
  /** Start date in ISO format */
  from?: string;
  /** End date in ISO format */
  to?: string;
  /** Whether the range is inclusive of the end date */
  inclusive?: boolean;
  /** Whether to include null/undefined values */
  includeNull?: boolean;
}

export interface ContractFilters {
  status?: ContractStatus;
  type?: ContractType;
  clientId?: string;
  freelancerId?: string;
  jobId?: string;
  proposalId?: string;
  startDate?: DateRangeFilter;
  endDate?: DateRangeFilter;
  startDateFrom?: string;
  startDateTo?: string;
  endDateFrom?: string;
  endDateTo?: string;
}

/**
 * Transformed contract filters for GraphQL queries
 */
export interface TransformedContractFilters {
  id?: { eq: string };
  status?: { eq: ContractStatus };
  type?: { eq: ContractType };
  clientId?: { eq: string };
  freelancerId?: { eq: string };
  jobId?: { eq: string };
  proposalId?: { eq: string };
  startDate?: {
    between?: [string, string];
    le?: string;
    lt?: string;
    ge?: string;
    gt?: string;
  };
  endDate?: {
    between?: [string, string];
    le?: string;
    lt?: string;
    ge?: string;
    gt?: string;
    attributeExists?: boolean;
  };
  and?: Array<TransformedContractFilters>;
  or?: Array<TransformedContractFilters>;
}

export interface ContractSearchParams {
  status?: ContractStatus;
  type?: ContractType;
  clientId?: string;
  freelancerId?: string;
  startDateFrom?: string;
  startDateTo?: string;
  endDateFrom?: string;
  endDateTo?: string;
  page?: number;
  limit?: number;
  sortBy?: 'startDate' | 'endDate' | 'createdAt' | 'budget';
  sortOrder?: 'asc' | 'desc';
}

export interface ContractJob {
  id: string;
  title: string;
  description?: string;
  budget?: number;
  type?: string;
  status?: string;
  clientId?: string;
  freelancerId?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface TimeEntry {
  id: string;
  date: string;
  hours: number;
  description?: string;
  status: 'pending' | 'approved' | 'rejected';
}

export interface ActivityLog {
  id: string;
  action: string;
  details?: string;
  userName: string;
  timestamp: string;
}

export interface ExtendedContract extends Omit<Contract, 'type' | 'status' | 'startDate' | 'endDate' | 'job' | 'client' | 'freelancer' | 'proposal' | 'activityLog' | 'timeEntries'> {
  type: ContractType;
  status: ContractStatus;
  startDate: string;
  endDate?: string;
  title: string;
  description: string;
  amount: number;
  hourlyRate?: number;
  hoursPerWeek?: number;
  duration?: number;
  paymentSchedule?: string;
  scopeOfWork?: string;
  paymentTerms?: string;
  job?: ContractJob;
  client?: {
    id: string;
    name: string;
    email?: string;
    avatar?: string;
  };
  freelancer?: {
    id: string;
    name: string;
    email?: string;
    avatar?: string;
  };
  proposal?: {
    id: string;
    coverLetter?: string;
  };
  activityLog?: ActivityLog[];
  timeEntries?: TimeEntry[];
  hoursWorked?: number;
  hoursThisWeek?: number;
}

export type TabItem = {
  id: string;
  label: string;
  content: React.ReactNode;
};

export type ContractPageProps = {
  contract: ExtendedContract;
  onStatusUpdate?: (status: ContractStatus) => Promise<void>;
  onDownload?: () => Promise<void>;
  onMessage?: () => void;
};

export enum DeliverableStatus {
  PENDING = 'PENDING',
  SUBMITTED = 'SUBMITTED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED'
}

export interface Deliverable {
  id: string;
  contractId: string;
  title: string;
  description?: string;
  dueDate?: string;
  status: DeliverableStatus;
  attachments?: string[];
  submissionNotes?: string;
  reviewNotes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface WorkSubmission {
  id: string;
  contractId: string;
  description: string;
  attachments?: string[];
  links?: string[];
  submittedAt: string;
  reviewedAt?: string;
  status: DeliverableStatus;
  reviewNotes?: string;
  submittedById: string;
  reviewedById?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateWorkSubmissionDto {
  contractId: string;
  description: string;
  attachments?: string[];
  links?: string[];
  submittedAt: string;
  submittedById: string;
  status: DeliverableStatus;
}

export interface ReviewWorkSubmissionDto {
  id: string;
  status: DeliverableStatus.APPROVED | DeliverableStatus.REJECTED;
  reviewNotes?: string;
}

export interface CreateDeliverableDto {
  contractId: string;
  title: string;
  description?: string;
  dueDate?: string;
}

export interface ContractWorkflowActions {
  canAccept: boolean;
  canReject: boolean;
  canSubmitWork: boolean;
  canApproveWork: boolean;
  canRequestRevisions: boolean;
  canCancel: boolean;
  canMarkPaid: boolean;
}
