# MyVillage Freelance Platform - Manual Test Cases

## Overview
This document contains comprehensive manual test cases for the MyVillage freelance platform. The platform consists of three main user roles: Admin, Client, and Freelancer, each with specific functionality and access controls.

## System Architecture
- **Frontend**: Next.js 14 with TypeScript
- **Authentication**: AWS Cognito with role-based access control
- **State Management**: React Context + Custom Hooks
- **UI Framework**: TailwindCSS with custom components
- **API**: GraphQL with AWS AppSync

---

## Test Cases

| Test Case ID | Module/Feature | Pre-conditions | Test Steps | Expected Result | Priority |
|--------------|----------------|---------------|------------|-----------------|----------|
| **AUTHENTICATION & AUTHORIZATION** |
| TC-001 | User Login - Valid Credentials | User account exists with valid credentials | 1. Navigate to login page<br>2. Enter valid email and password<br>3. Click "Sign In" button | User is authenticated and redirected to role-specific dashboard (Admin → /admin/dashboard, Client → /client/dashboard, Freelancer → /freelancer/dashboard) | High |
| TC-002 | User Login - Invalid Credentials | User account exists | 1. Navigate to login page<br>2. Enter invalid email or password<br>3. Click "Sign In" button | Error message displayed: "Invalid credentials" or similar, user remains on login page | High |
| TC-003 | User Login - Non-existent Account | No account exists for email | 1. Navigate to login page<br>2. Enter non-existent email and any password<br>3. Click "Sign In" button | Error message displayed indicating user not found, login fails | High |
| TC-004 | User Registration - Valid Data | No existing account with email | 1. Navigate to signup page<br>2. Fill name, email, password, confirm password<br>3. Select user role (Client/Freelancer)<br>4. Click "Sign Up" | User account created, verification email sent, redirected to confirmation page | High |
| TC-005 | User Registration - Duplicate Email | Account already exists with email | 1. Navigate to signup page<br>2. Enter existing email address<br>3. Fill other required fields<br>4. Click "Sign Up" | Error message displayed: "User already exists" or similar | High |
| TC-006 | User Registration - Password Validation | No constraints | 1. Navigate to signup page<br>2. Enter weak password (less than 8 chars, no special chars)<br>3. Fill other fields<br>4. Click "Sign Up" | Password validation error displayed with requirements | Medium |
| TC-007 | Role-based Route Access - Admin | Admin user logged in | 1. Login as admin user<br>2. Navigate to /admin/users<br>3. Verify page loads<br>4. Try accessing /client/dashboard | Admin pages accessible, client/freelancer pages redirect to admin dashboard or show unauthorized | High |
| TC-008 | Role-based Route Access - Client | Client user logged in | 1. Login as client user<br>2. Navigate to /client/dashboard<br>3. Try accessing /admin/users<br>4. Try accessing /freelancer/dashboard | Client pages accessible, admin/freelancer pages redirect or show unauthorized | High |
| TC-009 | Role-based Route Access - Freelancer | Freelancer user logged in | 1. Login as freelancer user<br>2. Navigate to /freelancer/dashboard<br>3. Try accessing /admin/users<br>4. Try accessing /client/dashboard | Freelancer pages accessible, admin/client pages redirect or show unauthorized | High |
| TC-010 | User Logout | User logged in | 1. Click on user menu/profile dropdown<br>2. Click "Logout" or "Sign Out"<br>3. Verify redirect to login page | User session cleared, redirected to login page, cannot access protected routes | High |
| **USER MANAGEMENT (ADMIN)** |
| TC-011 | View Users List | Admin logged in | 1. Navigate to /admin/users<br>2. Verify user list loads<br>3. Check pagination controls | User list displayed with name, email, role, status, created date, action buttons | High |
| TC-012 | Filter Users by Role | Admin logged in, users exist with different roles | 1. Navigate to /admin/users<br>2. Click "Filters" button<br>3. Select specific role (Admin/Client/Freelancer)<br>4. Click "Apply Filters" | Only users with selected role displayed in list | Medium |
| TC-013 | Filter Users by Status | Admin logged in, users with different statuses exist | 1. Navigate to /admin/users<br>2. Open filters<br>3. Select status (Active/Inactive/Suspended)<br>4. Apply filters | Only users with selected status displayed | Medium |
| TC-014 | Search Users | Admin logged in, multiple users exist | 1. Navigate to /admin/users<br>2. Enter search term in search box<br>3. Verify filtering occurs | Users matching search term displayed (by name or email) | Medium |
| TC-015 | Create New User | Admin logged in | 1. Navigate to /admin/users<br>2. Click "Add User" button<br>3. Fill required fields (name, email, role)<br>4. Click "Create User" | New user created successfully, redirected to user list with success message | High |
| TC-016 | Edit User Details | Admin logged in, user exists | 1. Navigate to /admin/users<br>2. Click "Edit" icon for a user<br>3. Modify user name and email<br>4. Click "Update User" | User details updated successfully, changes reflected in user list | High |
| TC-017 | View User Details | Admin logged in, user exists | 1. Navigate to /admin/users<br>2. Click "View" icon for a user<br>3. Verify user details page loads | User details displayed including profile info, role, status, join date | Medium |
| TC-018 | Delete User | Admin logged in, user exists | 1. Navigate to /admin/users<br>2. Click "Delete" icon for a user<br>3. Confirm deletion in dialog<br>4. Verify user removal | Confirmation dialog appears, user deleted on confirmation, removed from list | High |
| TC-019 | User List Pagination | Admin logged in, more than 5 users exist | 1. Navigate to /admin/users<br>2. Verify pagination controls<br>3. Navigate to next page<br>4. Verify page parameter in URL | Pagination works correctly, URL updates with page parameter | Medium |
| **JOB MANAGEMENT** |
| TC-020 | Create Job Posting - Client | Client logged in | 1. Navigate to /client/jobs<br>2. Click "Post New Job"<br>3. Fill job title, description, budget<br>4. Select skills and category<br>5. Submit form | Job created successfully with status "OPEN", visible in job listings | High |
| TC-021 | View Job Details - Public | Any user (logged in/out) | 1. Navigate to job listing page<br>2. Click on a job title<br>3. Verify job details page loads | Job details displayed with title, description, budget, skills, client info, proposal count | High |
| TC-022 | Edit Job Posting - Client Owner | Client logged in, owns the job | 1. Navigate to client jobs<br>2. Click "Edit" on owned job<br>3. Modify job details<br>4. Save changes | Job updated successfully, changes reflected in job listing | High |
| TC-023 | Edit Job Posting - Non-Owner | Client logged in, doesn't own job | 1. Try to access edit URL for job owned by another client | Access denied or redirected, cannot edit other client's jobs | High |
| TC-024 | Delete Job Posting | Client logged in, owns job with no accepted proposals | 1. Navigate to client jobs<br>2. Click "Delete" on job<br>3. Confirm deletion | Job deleted successfully, removed from listings | Medium |
| TC-025 | Job Search and Filter | User browsing jobs | 1. Navigate to job listings<br>2. Enter search keywords<br>3. Apply category filters<br>4. Set budget range | Jobs filtered according to criteria, results update dynamically | Medium |
| TC-026 | Job Status Management | Client logged in, job has accepted proposal | 1. Navigate to job with accepted proposal<br>2. Verify status shows "IN_PROGRESS"<br>3. Check status update triggers | Job status automatically updated when proposal accepted | Medium |
| **PROPOSAL MANAGEMENT** |
| TC-027 | Submit Job Proposal - Freelancer | Freelancer logged in, job is open | 1. Navigate to job details<br>2. Click "Apply Now"<br>3. Write cover letter<br>4. Enter bid amount<br>5. Submit proposal | Proposal submitted successfully, status "PENDING", visible in freelancer's proposals | High |
| TC-028 | Submit Duplicate Proposal | Freelancer already submitted proposal for job | 1. Navigate to job already applied to<br>2. Verify apply button state | "Proposal Submitted" message shown, cannot submit duplicate proposal | High |
| TC-029 | View Submitted Proposals - Freelancer | Freelancer logged in, has submitted proposals | 1. Navigate to /freelancer/proposals<br>2. Verify proposals list loads | List of submitted proposals with job details, status, bid amount, submit date | High |
| TC-030 | Edit Proposal - Pending Status | Freelancer logged in, has pending proposal | 1. Navigate to /freelancer/proposals<br>2. Click "Edit" on pending proposal<br>3. Modify cover letter and bid<br>4. Save changes | Proposal updated successfully, changes reflected in proposal list | Medium |
| TC-031 | Edit Proposal - Accepted Status | Freelancer logged in, has accepted proposal | 1. Try to edit accepted proposal | Edit function disabled or not available for accepted proposals | Medium |
| TC-032 | View Received Proposals - Client | Client logged in, job has proposals | 1. Navigate to /client/proposals<br>2. Click on job with proposals<br>3. View proposal details | List of received proposals with freelancer info, bid amounts, cover letters | High |
| TC-033 | Accept Proposal | Client logged in, job has pending proposals | 1. Navigate to job proposals<br>2. Click "Accept" on a proposal<br>3. Confirm acceptance | Proposal status changed to "ACCEPTED", job status to "IN_PROGRESS", contract created | High |
| TC-034 | Reject Proposal | Client logged in, job has pending proposals | 1. Navigate to job proposals<br>2. Click "Reject" on a proposal<br>3. Provide rejection reason | Proposal status changed to "REJECTED", freelancer notified | Medium |
| TC-035 | Withdraw Proposal | Freelancer logged in, has pending proposal | 1. Navigate to /freelancer/proposals<br>2. Click "Withdraw" on pending proposal<br>3. Confirm withdrawal | Proposal status changed to "WITHDRAWN", removed from client's view | Medium |
| **CONTRACT WORKFLOW** |
| TC-036 | Contract Creation | Client accepts a proposal | 1. Client accepts a freelancer proposal<br>2. Verify contract auto-creation<br>3. Check contract status "PENDING_FREELANCER_ACCEPTANCE" | Contract created automatically with proposal details, freelancer notified | High |
| TC-037 | Accept Contract - Freelancer | Contract in PENDING_FREELANCER_ACCEPTANCE status | 1. Freelancer navigates to contracts<br>2. Click "Accept Contract"<br>3. Confirm acceptance | Contract status changes to "ACTIVE", work can begin | High |
| TC-038 | Reject Contract - Freelancer | Contract in PENDING_FREELANCER_ACCEPTANCE status | 1. Freelancer navigates to contracts<br>2. Click "Decline Contract"<br>3. Confirm rejection | Contract status changes to "CANCELLED", client notified | High |
| TC-039 | Submit Work - Freelancer | Contract status is ACTIVE | 1. Navigate to active contract<br>2. Click "Submit Work"<br>3. Add description and files/links<br>4. Submit work | Contract status changes to "WORK_SUBMITTED", client notified for review | High |
| TC-040 | Approve Work - Client | Contract status is WORK_SUBMITTED | 1. Navigate to contract with submitted work<br>2. Review work submission<br>3. Click "Approve Work" | Contract status changes to "COMPLETED", freelancer notified | High |
| TC-041 | Request Revisions - Client | Contract status is WORK_SUBMITTED | 1. Navigate to contract with submitted work<br>2. Click "Request Revisions"<br>3. Provide revision comments<br>4. Submit request | Contract status changes to "REVISIONS_REQUESTED", freelancer notified | High |
| TC-042 | Resubmit Work After Revisions | Contract status is REVISIONS_REQUESTED | 1. Freelancer views revision request<br>2. Click "Resubmit Work"<br>3. Upload revised work<br>4. Submit | Contract status changes back to "WORK_SUBMITTED" | High |
| TC-043 | Cancel Active Contract | Contract status is ACTIVE or REVISIONS_REQUESTED | 1. Either party navigates to contract<br>2. Click "Cancel Contract"<br>3. Provide cancellation reason<br>4. Confirm cancellation | Contract status changes to "CANCELLED", both parties notified | Medium |
| TC-044 | Process Payment | Contract status is COMPLETED | 1. Client navigates to completed contract<br>2. Click "Mark as Paid"<br>3. Confirm payment processing | Contract status changes to "PAID", final status reached | High |
| TC-045 | Invalid Status Transition | Contract in any status | 1. Attempt to perform invalid action for current status<br>2. Check validation | Invalid actions disabled or show error message, status unchanged | High |
| **SKILLS & CATEGORIES MANAGEMENT (ADMIN)** |
| TC-046 | View Skills List | Admin logged in | 1. Navigate to /admin/skills<br>2. Verify skills list loads | Skills displayed with name, category, status, actions | Medium |
| TC-047 | Create New Skill | Admin logged in | 1. Navigate to /admin/skills<br>2. Click "Add Skill"<br>3. Enter skill name and select category<br>4. Set active status<br>5. Save skill | Skill created successfully, appears in skills list | Medium |
| TC-048 | Edit Skill | Admin logged in, skill exists | 1. Navigate to /admin/skills<br>2. Click "Edit" on a skill<br>3. Modify skill details<br>4. Update skill | Skill updated successfully, changes reflected in list | Medium |
| TC-049 | Delete Skill | Admin logged in, skill exists | 1. Navigate to /admin/skills<br>2. Click "Delete" on skill<br>3. Confirm deletion | Skill deleted, removed from list and job postings | Medium |
| TC-050 | Filter Skills by Category | Admin logged in, skills exist in multiple categories | 1. Navigate to /admin/skills<br>2. Open filters<br>3. Select specific category<br>4. Apply filter | Only skills from selected category displayed | Medium |
| TC-051 | View Job Categories | Admin logged in | 1. Navigate to /admin/job-categories<br>2. Verify categories list loads | Job categories displayed with name, description, skills count | Medium |
| TC-052 | Create Job Category | Admin logged in | 1. Navigate to /admin/job-categories<br>2. Click "Add Category"<br>3. Enter category name and description<br>4. Save category | Category created successfully, available for skill assignment | Medium |
| TC-053 | Edit Job Category | Admin logged in, category exists | 1. Navigate to /admin/job-categories<br>2. Click "Edit" on category<br>3. Modify details<br>4. Update category | Category updated, changes reflected in skills assignment | Medium |
| TC-054 | Delete Job Category | Admin logged in, category exists with no skills | 1. Navigate to /admin/job-categories<br>2. Click "Delete" on empty category<br>3. Confirm deletion | Category deleted successfully | Medium |
| TC-055 | Delete Category with Skills | Admin logged in, category has associated skills | 1. Try to delete category with skills<br>2. Verify prevention or warning | Deletion prevented or warning shown about associated skills | Medium |
| **DASHBOARD & REPORTING** |
| TC-056 | Client Dashboard Overview | Client logged in | 1. Navigate to /client/dashboard<br>2. Verify dashboard loads | Dashboard shows posted jobs, received proposals, active contracts, recent activity | High |
| TC-057 | Freelancer Dashboard Overview | Freelancer logged in | 1. Navigate to /freelancer/dashboard<br>2. Verify dashboard loads | Dashboard shows available jobs, submitted proposals, active contracts, earnings | High |
| TC-058 | Admin Dashboard Overview | Admin logged in | 1. Navigate to /admin/dashboard<br>2. Verify dashboard loads | Dashboard shows user statistics, job metrics, system activity, reports | High |
| TC-059 | Admin Reports - User Statistics | Admin logged in | 1. Navigate to /admin/reports<br>2. View user statistics section<br>3. Verify data accuracy | Accurate counts of total users by role, new users this month | Medium |
| TC-060 | Admin Reports - Job Statistics | Admin logged in | 1. Navigate to /admin/reports<br>2. View job statistics section<br>3. Check job status breakdown | Accurate counts of jobs by status, completion rates | Medium |
| **DATA VALIDATION & ERROR HANDLING** |
| TC-061 | Form Validation - Required Fields | User on any form | 1. Try to submit form without required fields<br>2. Verify validation messages | Form submission prevented, clear error messages for missing fields | High |
| TC-062 | Form Validation - Email Format | User entering email | 1. Enter invalid email format<br>2. Try to submit<br>3. Verify validation | Invalid email format rejected with appropriate error message | Medium |
| TC-063 | Form Validation - Password Strength | User creating/changing password | 1. Enter weak password<br>2. Verify strength indicator<br>3. Try to submit | Password strength shown, weak passwords rejected with requirements | Medium |
| TC-064 | File Upload Validation | User uploading files in work submission | 1. Try to upload oversized file<br>2. Try unsupported file type<br>3. Verify validation | File upload rejected with clear error message about size/type limits | Medium |
| TC-065 | Network Error Handling | User with poor internet connection | 1. Perform actions with simulated network issues<br>2. Verify error handling | Graceful error messages, retry options, offline indicators | Medium |
| TC-066 | API Rate Limiting | User making multiple rapid requests | 1. Make multiple API calls quickly<br>2. Verify rate limiting behavior | Appropriate throttling, user-friendly rate limit messages | Low |
| **PAGINATION & PERFORMANCE** |
| TC-067 | Large Data Set Pagination | Admin viewing large user list | 1. Navigate to users page with 100+ users<br>2. Test pagination controls<br>3. Verify performance | Smooth pagination, reasonable load times, accurate page counts | Medium |
| TC-068 | Search Performance | User searching large dataset | 1. Enter search terms in large lists<br>2. Verify search response time<br>3. Test with various terms | Search results returned quickly, accurate filtering | Medium |
| TC-069 | Table Sorting | User on any data table | 1. Click column headers to sort<br>2. Test ascending/descending<br>3. Verify sort accuracy | Data sorted correctly, sort indicators clear | Low |
| **SECURITY & AUTHORIZATION** |
| TC-070 | Direct URL Access - Unauthorized | User accessing unauthorized URL | 1. Manually enter admin URL as client user<br>2. Try accessing other user's data<br>3. Verify access control | Access denied, appropriate redirect or error message | High |
| TC-071 | Session Timeout | User with extended idle time | 1. Login and remain idle<br>2. Try to perform actions after timeout<br>3. Verify session handling | Session expired message, redirect to login, data protection | High |
| TC-072 | XSS Prevention | User entering script tags | 1. Enter script tags in text fields<br>2. Submit forms<br>3. Verify sanitization | Script tags sanitized, no code execution, safe data storage | High |
| TC-073 | Data Visibility - User Isolation | Multiple users with different data | 1. Login as different users<br>2. Verify data isolation<br>3. Check cross-user access | Users only see their own data, no data leakage between users | High |
| **EDGE CASES & BOUNDARY CONDITIONS** |
| TC-074 | Empty States | User with no data | 1. Login as new user with no data<br>2. Navigate to various sections<br>3. Verify empty state handling | Appropriate empty state messages, clear call-to-action buttons | Medium |
| TC-075 | Maximum Length Inputs | User entering very long text | 1. Enter text exceeding field limits<br>2. Try to submit forms<br>3. Verify length validation | Input length validation, truncation or error messages | Medium |
| TC-076 | Special Characters | User entering special characters | 1. Enter various special characters in forms<br>2. Submit and verify handling<br>3. Check display accuracy | Special characters handled correctly, proper encoding/display | Medium |
| TC-077 | Concurrent User Actions | Multiple users acting on same data | 1. Have two users edit same entity simultaneously<br>2. Submit changes<br>3. Verify conflict resolution | Appropriate conflict handling, data consistency maintained | Low |
| TC-078 | Browser Compatibility | User with different browsers | 1. Test core functionality in Chrome, Firefox, Safari, Edge<br>2. Verify responsive design<br>3. Check feature compatibility | Consistent functionality across browsers, responsive design works | Medium |
| TC-079 | Mobile Device Usage | User on mobile device | 1. Access platform on smartphone/tablet<br>2. Test key user flows<br>3. Verify mobile responsiveness | Mobile-friendly interface, core functionality accessible | Medium |
| TC-080 | Slow Network Conditions | User with slow internet | 1. Simulate slow network<br>2. Test page loads and form submissions<br>3. Verify loading states | Loading indicators shown, graceful degradation, acceptable performance | Low |

---

## Test Execution Guidelines

### Priority Levels
- **High**: Core functionality, security, data integrity
- **Medium**: User experience, business logic, validation
- **Low**: Performance, edge cases, compatibility

### Test Environment Setup
1. Ensure test database is populated with sample data
2. Create test user accounts for each role (Admin, Client, Freelancer)
3. Verify AWS Cognito authentication is properly configured
4. Test in clean browser environment (cleared cache/cookies)

### Regression Testing
Execute all High priority test cases for major releases, Medium priority for minor releases, and full test suite quarterly.

### Bug Reporting Format
- **Title**: Brief description of the issue
- **Steps to Reproduce**: Exact steps from test case
- **Expected vs Actual**: What should happen vs what actually happens
- **Environment**: Browser, OS, user role
- **Priority**: Based on impact and frequency
- **Screenshots**: For UI-related issues

---

## Automation Candidates

The following test cases are good candidates for future automation:
- Authentication flows (TC-001 to TC-010)
- CRUD operations (TC-011 to TC-018, TC-046 to TC-055)
- Form validations (TC-061 to TC-066)
- API response validations
- Core user workflows (job posting, proposal submission, contract flow)

---

*Last Updated: September 18, 2025*
*Version: 1.0*