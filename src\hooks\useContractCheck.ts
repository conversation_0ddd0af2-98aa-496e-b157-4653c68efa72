import { useState, useEffect } from 'react';
import { contractService } from '@/api/contracts/contract.service';

interface UseContractCheckResult {
  hasExistingContract: boolean;
  isLoading: boolean;
  error: string | null;
  checkContract: (proposalId: string) => Promise<void>;
}

/**
 * Custom hook to check if a proposal already has an associated contract
 * @param proposalId - The ID of the proposal to check
 * @returns Object containing contract existence status, loading state, and error
 */
export const useContractCheck = (proposalId?: string): UseContractCheckResult => {
  const [hasExistingContract, setHasExistingContract] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkContract = async (id: string) => {
    if (!id) return;
    
    try {
      setIsLoading(true);
      setError(null);
      const exists = await contractService.hasExistingContract(id);
      setHasExistingContract(exists);
    } catch (err) {
      console.error('Error checking contract:', err);
      setError('Failed to check contract status');
      setHasExistingContract(false);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (proposalId) {
      checkContract(proposalId);
    }
  }, [proposalId]);

  return {
    hasExistingContract,
    isLoading,
    error,
    checkContract,
  };
};

/**
 * Utility function to check if a "Create Contract" button should be shown
 * @param proposalStatus - The status of the proposal
 * @param hasExistingContract - Whether the proposal already has a contract
 * @returns boolean indicating if the button should be shown
 */
export const shouldShowCreateContractButton = (
  proposalStatus: string,
  hasExistingContract: boolean
): boolean => {
  return proposalStatus === 'ACCEPTED' && !hasExistingContract;
};

/**
 * Utility function to generate the contract creation URL
 * @param jobId - The ID of the job
 * @param proposalId - The ID of the proposal
 * @returns string URL for contract creation
 */
export const getCreateContractUrl = (jobId: string, proposalId: string): string => {
  return `/contracts/new?jobId=${jobId}&proposalId=${proposalId}`;
};
