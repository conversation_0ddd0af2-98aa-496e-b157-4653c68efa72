import React from 'react';
import { cn } from '@/lib/utils';

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'secondary' | 'destructive' | 'outline' | 'success' | 'warning';
}

const badgeVariants = {
  default: 'border-transparent bg-primary text-primary-foreground hover:bg-fern-green-600',
  secondary: 'border-transparent bg-secondary text-secondary-foreground hover:bg-sage-300',
  destructive: 'border-transparent bg-destructive text-destructive-foreground hover:bg-red-600',
  outline: 'text-foreground border-border',
  success: 'border-transparent bg-fern-green-500 text-white hover:bg-fern-green-600',
  warning: 'border-transparent bg-yellow-500 text-white hover:bg-yellow-600',
};

function Badge({ className, variant = 'default', ...props }: BadgeProps) {
  return (
    <div
      className={cn(
        'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
        badgeVariants[variant],
        className
      )}
      {...props}
    />
  );
}

export { Badge, badgeVariants };
