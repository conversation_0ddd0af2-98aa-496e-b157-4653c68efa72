"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";
import { skillService } from "@/api/skills/skill.service";
import { jobCategoryService } from "@/api/job-categories/job-category.service";
import { CreateSkillInput } from "@/types/features/skills/skill.types";
import { JobCategory } from "@/types/features/job-categories/job-category.types";
import { Icon } from "@/components/ui/Icon";
import { ContentHeader } from "@/components/layout/ContentHeader";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Textarea } from "@/components/ui/Textarea";
import useToaster from "@/hooks/useToaster";

export default function NewSkillPage() {
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const router = useRouter();
  const { showSuccess, showError } = useToaster();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [jobCategories, setJobCategories] = useState<JobCategory[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(true);

  const [formData, setFormData] = useState<CreateSkillInput>({
    name: "",
    jobCategoryId: "",
    description: "",
    isActive: true,
  });

  useEffect(() => {
    const loadJobCategories = async () => {
      try {
        setIsLoadingCategories(true);
        const response = await jobCategoryService.listJobCategories(
          undefined,
          100
        );
        setJobCategories(response.items || []);
      } catch (err) {
        console.error("Error loading job categories:", err);
        showError("Failed to load job categories. Please try again.");
      } finally {
        setIsLoadingCategories(false);
      }
    };

    if (isAuthenticated) {
      loadJobCategories();
    }
  }, [isAuthenticated, showError]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsSubmitting(true);
      setError(null);

      if (!formData.name.trim()) {
        setError("Skill name is required.");
        return;
      }

      await skillService.createSkill(formData);

      showSuccess("Skill created successfully");

      router.push("/admin/skills");
    } catch (err) {
      console.error("Error creating skill:", err);
      const errorMessage =
        err instanceof Error
          ? err.message
          : "Failed to create skill. Please try again.";
      setError(errorMessage);
      showError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    router.push("/admin/skills");
  };

  if (
    !authLoading &&
    (!isAuthenticated || user?.attributes?.["custom:role"] !== "ADMIN")
  ) {
    router.push("/login");
    return null;
  }

  if (authLoading || !isAuthenticated) {
    return (
      <div className="flex items-center justify-center h-full">
        <Icon name="Loader2" size="xl" className="animate-spin text-blue-500" />
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-4 sm:p-6">
      <ContentHeader
        title="Add New Skill"
        subtitle="Fill in the details below to create a new skill."
        breadcrumbs={[
          { label: "Dashboard", href: "/admin/dashboard" },
          { label: "Skills", href: "/admin/skills" },
          { label: "New Skill", current: true },
        ]}
        showBackButton={true}
      />

      {error && (
        <div className="rounded-md bg-red-50 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <Icon name="XCircle" size="md" className="text-red-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      <div className="bg-card rounded-lg shadow-sm p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Skill Name *
              </label>
              <Input
                type="text"
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
                placeholder="Enter skill name"
                required
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Job Category *
              </label>
              <select
                value={formData.jobCategoryId}
                onChange={(e) =>
                  setFormData({ ...formData, jobCategoryId: e.target.value })
                }
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                required
                disabled={isLoadingCategories}
              >
                <option value="">
                  {isLoadingCategories
                    ? "Loading categories..."
                    : "Select a job category"}
                </option>
                {jobCategories.map((category: JobCategory) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
              <p className="mt-1 text-sm text-gray-500">
                Choose the job category that best fits this skill.
              </p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <Textarea
              value={formData.description || ""}
              onChange={(e) =>
                setFormData({ ...formData, description: e.target.value })
              }
              placeholder="Enter skill description"
              rows={4}
              className="w-full"
            />
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="isActive"
              checked={formData.isActive}
              onChange={(e) =>
                setFormData({ ...formData, isActive: e.target.checked })
              }
              className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
            />
            <label
              htmlFor="isActive"
              className="text-sm font-medium text-gray-700"
            >
              Active (skill will be available for selection)
            </label>
          </div>

          <div className="flex justify-end space-x-3 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
              className="min-w-[100px]"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !formData.name.trim()}
              className="min-w-[100px]"
            >
              {isSubmitting ? (
                <>
                  <Icon
                    name="Loader2"
                    size="sm"
                    className="animate-spin mr-2"
                  />
                  Creating...
                </>
              ) : (
                <>
                  <Icon name="Plus" size="sm" className="mr-2" />
                  Create Skill
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
