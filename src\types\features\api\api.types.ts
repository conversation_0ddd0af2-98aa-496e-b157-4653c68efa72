/**
 * Base API response type for all API responses
 */
export type ApiResponse<T = any> = {
  /** The response data */
  data: T;
  /** Optional success message */
  message?: string;
  /** Indicates if the request was successful */
  success: boolean;
  /** Optional error code for error responses */
  code?: string;
  /** Optional validation errors */
  errors?: Record<string, string[]>;
};

/**
 * Paginated response type for list endpoints
 */
export type PaginatedResponse<T> = {
  /** Array of items in the current page */
  items: T[];
  /** Total number of items across all pages */
  total: number;
  /** Current page number (1-based) */
  page: number;
  /** Number of items per page */
  limit: number;
  /** Total number of pages */
  totalPages: number;
  /** URL for the next page, if available */
  nextPage?: string;
  /** URL for the previous page, if available */
  prevPage?: string;
};

/**
 * Standard error response type
 */
export type ApiError = {
  /** Error message */
  message: string;
  /** Error code for programmatic handling */
  code?: string;
  /** HTTP status code */
  statusCode?: number;
  /** Additional error details */
  details?: Record<string, unknown>;
  /** Validation errors, if any */
  validationErrors?: Record<string, string[]>;
};

/**
 * Standard pagination parameters for list endpoints
 */
export type PaginationParams = {
  /** Page number (1-based) */
  page?: number;
  /** Number of items per page */
  limit?: number;
  /** Field to sort by */
  sortBy?: string;
  /** Sort order (asc/desc) */
  sortOrder?: 'asc' | 'desc';
};

/**
 * Standard search parameters
 */
export type SearchParams = PaginationParams & {
  /** Search query string */
  query?: string;
  /** Additional filters */
  filters?: Record<string, unknown>;
};

/**
 * Standard response for create operations
 */
export type CreateResponse<T = { id: string }> = ApiResponse<{
  /** ID of the created resource */
  id: string;
  /** The created resource */
  data?: T;
}>;

/**
 * Standard response for update operations
 */
export type UpdateResponse<T = any> = ApiResponse<{
  /** Indicates if the resource was updated */
  updated: boolean;
  /** Number of affected rows */
  affected?: number;
  /** The updated resource */
  data?: T;
}>;

/**
 * Standard response for delete operations
 */
export type DeleteResponse = ApiResponse<{
  /** Indicates if the resource was deleted */
  deleted: boolean;
  /** Number of affected rows */
  affected?: number;
}>;

/**
 * Standard response for file uploads
 */
export type FileUploadResponse = ApiResponse<{
  /** URL of the uploaded file */
  url: string;
  /** File name */
  fileName: string;
  /** File size in bytes */
  size: number;
  /** File MIME type */
  mimeType: string;
  /** Additional metadata */
  metadata?: Record<string, unknown>;
}>;

/**
 * Standard request options for API calls
 */
export type ApiRequestOptions = {
  /** Request headers */
  headers?: Record<string, string>;
  /** Query parameters */
  params?: Record<string, any>;
  /** Request timeout in milliseconds */
  timeout?: number;
  /** Whether to include credentials (cookies, HTTP authentication) */
  withCredentials?: boolean;
  /** Response type (default: 'json') */
  responseType?: 'json' | 'text' | 'blob' | 'arraybuffer' | 'document';
  /** Request cancellation token */
  signal?: AbortSignal;
  /** Whether to show loading indicators (for UI) */
  showLoading?: boolean;
  /** Whether to show error messages (for UI) */
  showError?: boolean;
};
