import React, { ReactNode } from 'react';
import { cn } from '@/lib/utils';
import { Breadcrumb, BreadcrumbItem } from '@/components/ui/Breadcrumb';
import { Button } from '@/components/ui/Button';
import type { ButtonProps } from '@/types/components/Button';
import { Icon, IconName } from '@/components/ui/Icon';

export interface ContentActionButton {
  label: string;
  onClick: () => void;
  variant?: ButtonProps['variant'];
  size?: ButtonProps['size'];
  icon?: IconName;
  disabled?: boolean;
  isLoading?: boolean;
}

interface ContentHeaderProps {
  title: string;
  subtitle?: string;
  breadcrumbs?: BreadcrumbItem[];
  showBackButton?: boolean;
  backButtonLabel?: string;
  actions?: ReactNode | ContentActionButton[];
  className?: string;
  headerClassName?: string;
  titleClassName?: string;
  subtitleClassName?: string;
}

const ContentHeader: React.FC<ContentHeaderProps> = ({
  title,
  subtitle,
  breadcrumbs = [],
  showBackButton = false,
  backButtonLabel = 'Back',
  actions,
  className = '',
  headerClassName = '',
  titleClassName = '',
  subtitleClassName = '',
}) => {
  const renderActionButton = (action: ContentActionButton, index: number) => {
    const buttonProps = {
      variant: action.variant || 'default',
      size: action.size || 'default',
      onClick: action.onClick,
      disabled: action.disabled || action.isLoading,
      className: action.icon ? 'flex items-center gap-2' : ''
    } as const;

    return (
      <Button key={index} {...buttonProps}>
        {action.icon && <Icon name={action.icon} size="sm" />}
        {action.label}
      </Button>
    );
  };

  return (
    <div className={cn('w-full space-y-4', className)}>
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="flex-1">
          {breadcrumbs.length > 0 && (
            <Breadcrumb 
              items={breadcrumbs} 
              className="hidden sm:block" 
            />
          )}
        </div>
        
        <div className="flex-shrink-0">
          <div className="flex flex-wrap gap-2 justify-end items-center">
            {showBackButton && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.history.back()}
                className="flex items-center gap-1"
              >
                <Icon name="ArrowLeft" size="sm" className="h-4 w-4" />
                {backButtonLabel}
              </Button>
            )}
            
            {Array.isArray(actions) && actions.length > 0 && (
              <>
                {actions.map((action, index) => renderActionButton(action, index))}
              </>
            )}
            
            {!Array.isArray(actions) && actions && actions}
          </div>
        </div>
      </div>
      
      {/* Title and Subtitle */}
      <div className={cn('space-y-1', headerClassName)}>
        <h1 className={cn('text-2xl font-bold tracking-tight', titleClassName)}>
          {title}
        </h1>
        {subtitle && (
          <p className={cn('text-sm text-muted-foreground', subtitleClassName)}>
            {subtitle}
          </p>
        )}
      </div>
    </div>
  );
};

export { ContentHeader };
export type { ContentHeaderProps, ContentActionButton as ActionButton };
