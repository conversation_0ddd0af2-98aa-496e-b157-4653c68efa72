# MyVillage Freelance Platform

This is the frontend for the MyVillage Freelance Platform, built with [Next.js](https://nextjs.org/).

## Getting Started

### Prerequisites

- Node.js 18.0.0 or later
- npm or yarn package manager
- AWS Account with AppSync and Cognito configured

### Environment Setup

1. Copy the example environment file:
   ```bash
   cp env.example .env.local
   ```

2. Update the `.env.local` file with your AWS and AppSync credentials:
   ```env
   # AWS Configuration
   NEXT_PUBLIC_AWS_REGION=us-east-1
   
   # AppSync Configuration
   NEXT_PUBLIC_APPSYNC_GRAPHQL_ENDPOINT=your_appsync_graphql_endpoint
   NEXT_PUBLIC_APPSYNC_API_KEY=your_appsync_api_key
   NEXT_PUBLIC_APPSYNC_AUTH_TYPE=AMAZON_COGNITO_USER_POOLS
   
   # API Configuration
   NEXT_PUBLIC_API_BASE_URL=your_api_base_url
   
   # Environment
   NODE_ENV=development
   ```

### Installation

1. Install dependencies:
   ```bash
   npm install
   # or
   yarn
   ```

2. Run the development server:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

3. Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

### Development Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
