"use client";
import React from "react";
import { AuthProvider as NewAuthProvider } from "../lib/auth/AuthContext";
import { ensureAmplifyConfigured } from "../lib/graphql/graphqlClient";
import { Toaster } from "@/components/ui/toast";
import { StripeProvider } from "../providers/StripeProvider";

ensureAmplifyConfigured();

export default function Providers({ children }: { children: React.ReactNode }) {
  return (
    <NewAuthProvider>
      <StripeProvider>
        {children}
        <Toaster position="top-right" duration={4000} />
      </StripeProvider>
    </NewAuthProvider>
  );
}

