import { gql } from '@apollo/client';

export const CREATE_CONTRACT = gql`
  mutation CreateContract($input: CreateContractInput!) {
    createContract(input: $input) {
      id
      jobId
      proposalId
      clientId
      freelancerId
      title
      description
      type
      status
      terms
      startDate
      endDate
      budget
      createdAt
      updatedAt
    }
  }
`;

export const UPDATE_CONTRACT = gql`
  mutation UpdateContract($input: UpdateContractInput!) {
    updateContract(input: $input) {
      id
      status
      endDate
      terms
      updatedAt
    }
  }
`;

export const UPDATE_CONTRACT_STATUS = gql`
  mutation UpdateContract($input: UpdateContractInput!) {
    updateContract(input: $input) {
      id
      status
      updatedAt
    }
  }
`;

export const ACCEPT_CONTRACT = gql`
  mutation AcceptContract($id: ID!) {
    acceptContract(id: $id) {
      id
      status
      updatedAt
    }
  }
`;

export const REJECT_CONTRACT = gql`
  mutation RejectContract($id: ID!) {
    rejectContract(id: $id) {
      id
      status
      updatedAt
    }
  }
`;

export const COMPLETE_CONTRACT = gql`
  mutation CompleteContract($id: ID!) {
    completeContract(id: $id) {
      id
      status
      endDate
      updatedAt
    }
  }
`;

export const CREATE_PAYMENT_SCHEDULE = gql`
  mutation CreatePaymentSchedule($input: CreatePaymentScheduleInput!) {
    createPaymentSchedule(input: $input) {
      id
      contractId
      amount
      dueDate
      status
      description
      createdAt
      updatedAt
    }
  }
`;

export const UPDATE_PAYMENT_SCHEDULE = gql`
  mutation UpdatePaymentSchedule($input: UpdatePaymentScheduleInput!) {
    updatePaymentSchedule(input: $input) {
      id
      amount
      dueDate
      status
      description
      updatedAt
    }
  }
`;

export const CREATE_DELIVERABLE = gql`
  mutation CreateDeliverable($input: CreateDeliverableInput!) {
    createDeliverable(input: $input) {
      id
      contractId
      title
      description
      dueDate
      status
      attachments
      createdAt
      updatedAt
    }
  }
`;

export const UPDATE_DELIVERABLE = gql`
  mutation UpdateDeliverable($input: UpdateDeliverableInput!) {
    updateDeliverable(input: $input) {
      id
      title
      description
      dueDate
      status
      attachments
      updatedAt
    }
  }
`;

export const CREATE_WORK_SUBMISSION = gql`
  mutation CreateWorkSubmission($input: CreateWorkSubmissionInput!) {
    createWorkSubmission(input: $input) {
      id
      contractId
      description
      attachments
      links
      submittedAt
      status
      submittedById
      createdAt
      updatedAt
    }
  }
`;

export const UPDATE_WORK_SUBMISSION = gql`
  mutation UpdateWorkSubmission($input: UpdateWorkSubmissionInput!) {
    updateWorkSubmission(input: $input) {
      id
      status
      reviewNotes
      reviewedAt
      reviewedById
      updatedAt
    }
  }
`;

export const UPDATE_JOB_STATUS = gql`
  mutation UpdateJob($input: UpdateJobInput!) {
    updateJob(input: $input) {
      id
      status
      updatedAt
    }
  }
`;

export const CREATE_PAYMENT = gql`
  mutation CreatePayment($input: CreatePaymentInput!) {
    createPayment(input: $input) {
      id
      contractId
      amount
      status
      method
      paidAt
      createdAt
      updatedAt
    }
  }
`;
