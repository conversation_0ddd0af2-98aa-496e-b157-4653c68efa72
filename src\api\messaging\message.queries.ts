import { gql } from '@apollo/client';

export const LIST_MY_CONVERSATIONS = gql`
  query ListMyConversations($filter: ModelConversationFilterInput) {
    listConversations(filter: $filter) {
      items {
        id
        jobId
        clientId
        freelancerId
        createdAt
        updatedAt
        job {
          id
          title
        }
        client {
          id
          name
          email
          profilePhoto
        }
        freelancer {
          id
          name
          email
          profilePhoto
        }
        messagesData {
          items {
            id
            messageText
            createdAt
            updatedAt
            conversationId
            senderId
            sender {
              id
              name
              email
              profilePhoto
            }
          }
        }
      }
    }
  }
`;

export const MESSAGES_SUBSCRIPTION = gql`
  subscription OnCreateMessage($conversationId: ID!) {
    onCreateMessage(conversationId: $conversationId) {
      id
      messageText
      conversationId
      senderId
      receiverId
      createdAt
      updatedAt
      sender {
        id
        name
        email
        profilePhoto
      }
    }
  }
`;
