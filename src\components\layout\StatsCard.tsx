import React from 'react';
import { Icon } from '@/components/ui/Icon';
import { Badge } from '@/components/ui/Badge';
import type { IconName } from '@/types/components/Icon';

export interface StatsCardProps {
  /** The title/label for the statistic */
  title: string;
  
  /** The main value to display */
  value: React.ReactNode;
  
  /** Optional subtitle or description */
  subtitle?: string;
  
  /** Icon to display */
  icon?: IconName;
  
  /** Icon color classes */
  iconColor?: string;
  
  /** Background gradient classes */
  bgGradient?: string;
  
  /** Text color classes for the value */
  valueColor?: string;
  
  /** Text color classes for the title */
  titleColor?: string;
  
  /** Additional CSS classes */
  className?: string;
  
  /** Optional badge to display */
  badge?: {
    text: string;
    variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  };
  
  /** Loading state */
  loading?: boolean;
  
  /** Click handler */
  onClick?: () => void;
  
  /** Whether to show hover effects */
  hoverable?: boolean;
}

export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  iconColor,
  bgGradient = 'bg-gradient-to-br from-gray-50 to-gray-100',
  valueColor = 'text-gray-700',
  titleColor = 'text-gray-600',
  className = '',
  badge,
  loading = false,
  onClick,
  hoverable = false,
}) => {
  if (loading) {
    return (
      <div className={`text-center p-4 border rounded-lg ${bgGradient} animate-pulse ${className}`}>
        <div className={`h-6 w-16 mx-auto mb-2 bg-gray-300 rounded ${valueColor.replace('text-', 'bg-').replace('-700', '-300')}`}></div>
        <div className={`h-4 w-20 mx-auto bg-gray-300 rounded ${titleColor.replace('text-', 'bg-').replace('-600', '-300')}`}></div>
      </div>
    );
  }

  return (
    <div 
      className={`
        text-center p-4 border rounded-lg ${bgGradient} 
        ${hoverable ? 'hover:shadow-md transition-shadow duration-200' : ''}
        ${onClick ? 'cursor-pointer hover:scale-[1.02] transition-transform duration-200' : ''}
        ${className}
      `}
      onClick={onClick}
    >
      {/* Icon */}
      {icon && (
        <div className="flex justify-center mb-2">
          <div className={`p-2 rounded-lg ${iconColor || 'bg-blue-100'}`}>
            <Icon 
              name={icon} 
              size="sm" 
              className={iconColor ? iconColor.replace('bg-', 'text-').replace('-100', '-600') : 'text-blue-600'} 
            />
          </div>
        </div>
      )}
      
      {/* Value */}
      <div className={`text-2xl font-bold ${valueColor} mb-1`}>
        {value}
      </div>
      
      {/* Title and Badge */}
      <div className="flex items-center justify-center gap-2 flex-wrap">
        <div className={`text-sm ${titleColor}`}>
          {title}
        </div>
        {badge && (
          <Badge variant={badge.variant || 'secondary'} className="text-xs">
            {badge.text}
          </Badge>
        )}
      </div>
      
      {/* Subtitle */}
      {subtitle && (
        <div className="text-xs text-muted-foreground mt-1">
          {subtitle}
        </div>
      )}
    </div>
  );
};

export default StatsCard;