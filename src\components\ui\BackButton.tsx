'use client';

import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/Button';
import { Icon } from '@/components/ui/Icon';

interface BackButtonProps {
  label?: string;
  className?: string;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
}

export default function BackButton({ 
  label = 'Back', 
  className = '',
  variant = 'ghost',
  size = 'default'
}: BackButtonProps) {
  const router = useRouter();

  return (
    <Button
      variant={variant}
      size={size}
      onClick={() => router.back()}
      className={`cursor-pointer ${className}`}
      title={label}
    >
      <Icon name="ArrowLeft" size="sm" className="h-4 w-4 mr-2" />
      {label}
    </Button>
  );
}
