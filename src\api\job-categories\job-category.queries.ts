import { gql } from '@apollo/client';

export const GET_JOB_CATEGORY = gql`
  query GetJobCategory($id: ID!, $includeSkills: Boolean = false) {
    getJobCategory(id: $id) {
      id
      name
      description
      isActive
      skills @include(if: $includeSkills) {
        items {
          id
          name
          description
          isActive
          jobCategoryId
          createdAt
          updatedAt
        }
        nextToken
      }
      createdAt
      updatedAt
    }
  }
`;

export const LIST_JOB_CATEGORIES = gql`
  query ListJobCategories(
    $filter: ModelJobCategoryFilterInput
    $limit: Int
    $nextToken: String
    $includeSkills: Boolean = false
  ) {
    listJobCategories(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        name
        description
        isActive
        skills @include(if: $includeSkills) {
          items {
            id
            name
            description
            isActive
            jobCategoryId
            createdAt
            updatedAt
          }
          nextToken
        }
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;

export const LIST_SKILLS_BY_CATEGORY = gql`
  query ListSkillsByCategory(
    $filter: ModelSkillFilterInput!
    $limit: Int
    $nextToken: String
  ) {
    listSkills(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        name
        description
        isActive
        jobCategoryId
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;
