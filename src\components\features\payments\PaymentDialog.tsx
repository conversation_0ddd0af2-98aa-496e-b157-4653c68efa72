'use client';

import React, { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/Dialog';
import { Button } from '@/components/ui/Button';
import { CheckoutForm } from './CheckoutForm';
import { PaymentStatus } from './PaymentStatus';
import { Contract, ExtendedContract } from '@/types/features/contracts/contract.types';
import { PaymentError, PaymentStatus as PaymentStatusEnum } from '@/types/features/payments/payment.types';
import { Icon } from '@/components/ui/Icon';
import { useStripeContext } from '@/providers/StripeProvider';
import { Alert, AlertDescription } from '@/components/ui/Alert';

interface PaymentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  contract: Contract | ExtendedContract;
  clientId: string;
  clientData?: {
    id: string;
    name: string;
    email: string;
    profilePhoto?: string;
  };
  onPaymentSuccess?: (paymentResult: any) => void;
  onPaymentError?: (error: PaymentError) => void;
}

export const PaymentDialog: React.FC<PaymentDialogProps> = ({
  isOpen,
  onClose,
  contract,
  clientId,
  clientData,
  onPaymentSuccess,
  onPaymentError,
}) => {
  const { isLoading: stripeLoading, error: stripeError } = useStripeContext();
  const [paymentStatus, setPaymentStatus] = useState<{
    status: PaymentStatusEnum;
    transactionId?: string;
    amount?: number;
    currency?: string;
    error?: string;
  } | null>(null);

  const amount = 'budget' in contract ? contract.budget : 0;
  const currency = 'usd';

  const handlePaymentSuccess = (paymentResult: any) => {
    setPaymentStatus({
      status: PaymentStatusEnum.PAID,
      transactionId: paymentResult.paymentIntentId,
      amount: paymentResult.amount / 100,
      currency: currency,
    });

    if (onPaymentSuccess) {
      onPaymentSuccess(paymentResult);
    }
  };

  const handlePaymentError = (error: PaymentError) => {
    setPaymentStatus({
      status: PaymentStatusEnum.FAILED,
      error: error.message,
      amount: amount,
      currency: currency,
    });

    if (onPaymentError) {
      onPaymentError(error);
    }
  };

  const handleRetry = () => {
    setPaymentStatus(null);
  };

  const handleCloseDialog = () => {
    setPaymentStatus(null);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleCloseDialog}>
      <DialogContent className="max-w-lg min-w-[320px] sm:min-w-[400px] max-h-[90vh] overflow-y-auto shadow-2xl border border-white/10">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle>Process Payment</DialogTitle>
              <DialogDescription>
                Complete payment for contract: {contract.title}
              </DialogDescription>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleCloseDialog}
              className="h-6 w-6"
            >
              <Icon name="X" className="h-6 w-6" />
            </Button>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {stripeError && (
            <Alert variant="destructive">
              <Icon name="AlertCircle" className="h-5 w-5" />
              <AlertDescription>
                Stripe initialization failed: {stripeError}
              </AlertDescription>
            </Alert>
          )}
          
          {stripeLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <Icon name="Loader2" className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
                <p className="text-gray-600">Initializing secure payment system...</p>
              </div>
            </div>
          ) : paymentStatus ? (
            <PaymentStatus
              status={paymentStatus.status}
              amount={paymentStatus.amount || amount}
              currency={paymentStatus.currency || currency}
              transactionId={paymentStatus.transactionId}
              errorMessage={paymentStatus.error}
              onRetry={paymentStatus.status === PaymentStatusEnum.FAILED ? handleRetry : undefined}
              paidAt={paymentStatus.status === PaymentStatusEnum.PAID ? new Date().toISOString() : undefined}
            />
          ) : (
            <CheckoutForm
              contractId={contract.id}
              amount={amount}
              currency={currency}
              clientId={clientId}
              clientData={clientData}
              onPaymentSuccess={handlePaymentSuccess}
              onPaymentError={handlePaymentError}
              onCancel={handleCloseDialog}
            />
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};