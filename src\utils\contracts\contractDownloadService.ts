import { ExtendedContract, ContractType } from '@/types/features/contracts/contract.types';
import { format } from 'date-fns';

export interface ContractDocumentData {
  contract: ExtendedContract;
  generatedAt: string;
  documentId: string;
}

/**
 * Service for generating downloadable contract documents
 */
export class ContractDownloadService {
  private static instance: ContractDownloadService;

  private constructor() { }

  public static getInstance(): ContractDownloadService {
    if (!ContractDownloadService.instance) {
      ContractDownloadService.instance = new ContractDownloadService();
    }
    return ContractDownloadService.instance;
  }

  /**
   * Generate a document ID for the contract
   */
  private generateDocumentId(contractId: string): string {
    const timestamp = Date.now().toString(36).toUpperCase();
    const shortContractId = contractId.slice(-8).toUpperCase();
    return `CONTRACT-${shortContractId}-${timestamp}`;
  }

  /**
   * Format currency amount
   */
  private formatCurrency(amount?: number): string {
    if (amount === undefined) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  }

  /**
   * Format date
   */
  private formatDate(dateString?: string): string {
    if (!dateString) return 'N/A';
    try {
      return format(new Date(dateString), 'MMMM d, yyyy');
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  }

  /**
   * Get contract type label
   */
  private getContractTypeLabel(type: ContractType): string {
    switch (type) {
      case ContractType.FIXED_PRICE:
        return 'Fixed Price';
      case ContractType.HOURLY:
        return 'Hourly';
      default:
        return type;
    }
  }

  /**
   * Generate HTML content for the contract document
   */
  generateContractHTML(contract: ExtendedContract): string {
    const documentId = this.generateDocumentId(contract.id);
    const generatedAt = format(new Date(), 'MMMM d, yyyy \'at\' h:mm a');

    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Contract - ${contract.title}</title>
        <style>
          @media print {
            @page {
              margin: 1in;
            }
            body {
              print-color-adjust: exact;
            }
          }
          
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
          }
          
          .header {
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
          }
          
          .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
          }
          
          .document-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
          }
          
          .document-info {
            font-size: 14px;
            color: #666;
          }
          
          .contract-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
          }
          
          .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
          }
          
          .info-item {
            margin-bottom: 10px;
          }
          
          .label {
            font-weight: bold;
            color: #555;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
          
          .value {
            font-size: 16px;
            margin-top: 2px;
          }
          
          .section {
            margin: 30px 0;
          }
          
          .section-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #eee;
            padding-bottom: 5px;
          }
          
          .contract-amount {
            font-size: 24px;
            font-weight: bold;
            color: #28a745;
          }
          
          .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
          
          .status-active {
            background-color: #e3f2fd;
            color: #1976d2;
          }
          
          .status-completed {
            background-color: #e8f5e8;
            color: #2e7d32;
          }
          
          .status-draft {
            background-color: #fff3e0;
            color: #f57c00;
          }
          
          .status-cancelled {
            background-color: #ffebee;
            color: #d32f2f;
          }
          
          .status-disputed {
            background-color: #fff8e1;
            color: #f9a825;
          }
          
          .parties-section {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
          }
          
          .party-info {
            margin-bottom: 15px;
          }
          
          .terms-content {
            background: #fafafa;
            padding: 20px;
            border-radius: 5px;
            border: 1px solid #e0e0e0;
            white-space: pre-line;
            font-family: 'Courier New', monospace;
            font-size: 14px;
          }
          
          .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 12px;
            color: #666;
            text-align: center;
          }
          
          .signature-section {
            margin-top: 40px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 50px;
          }
          
          .signature-block {
            border-top: 1px solid #333;
            padding-top: 10px;
            text-align: center;
          }
          
          .signature-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
          }
          
          @media (max-width: 600px) {
            .info-grid, .signature-section {
              grid-template-columns: 1fr;
            }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="company-name">MyVillage Freelance</div>
          <div class="document-title">Freelance Service Contract</div>
          <div class="document-info">Document ID: ${documentId} | Generated: ${generatedAt}</div>
        </div>
        
        <div class="contract-info">
          <h2 style="margin-top: 0;">${contract.title}</h2>
          <div class="info-grid">
            <div class="info-item">
              <div class="label">Contract ID</div>
              <div class="value">${contract.id}</div>
            </div>
            <div class="info-item">
              <div class="label">Status</div>
              <div class="value">
                <span class="status-badge status-${contract.status.toLowerCase()}">${contract.status.replace('_', ' ')}</span>
              </div>
            </div>
            <div class="info-item">
              <div class="label">Contract Type</div>
              <div class="value">${this.getContractTypeLabel(contract.type)}</div>
            </div>
            <div class="info-item">
              <div class="label">Contract Value</div>
              <div class="value contract-amount">
                ${contract.type === ContractType.HOURLY
        ? `${this.formatCurrency(contract.hourlyRate)}/hour`
        : this.formatCurrency(contract.amount)}
              </div>
            </div>
            <div class="info-item">
              <div class="label">Start Date</div>
              <div class="value">${this.formatDate(contract.startDate)}</div>
            </div>
            <div class="info-item">
              <div class="label">End Date</div>
              <div class="value">${contract.endDate ? this.formatDate(contract.endDate) : 'Ongoing'}</div>
            </div>
          </div>
        </div>

        ${contract.client || contract.freelancer ? `
        <div class="section">
          <div class="section-title">Contracting Parties</div>
          <div class="parties-section">
            ${contract.client ? `
            <div class="party-info">
              <div class="label">Client</div>
              <div class="value">
                <strong>${contract.client.name || 'Client'}</strong><br>
                ${contract.client.email ? `Email: ${contract.client.email}<br>` : ''}
                ID: ${contract.client.id}
              </div>
            </div>
            ` : ''}
            
            ${contract.freelancer ? `
            <div class="party-info">
              <div class="label">Freelancer</div>
              <div class="value">
                <strong>${contract.freelancer.name || 'Freelancer'}</strong><br>
                ${contract.freelancer.email ? `Email: ${contract.freelancer.email}<br>` : ''}
                ID: ${contract.freelancer.id}
              </div>
            </div>
            ` : ''}
          </div>
        </div>
        ` : ''}

        <div class="section">
          <div class="section-title">Project Description</div>
          <p>${contract.description || 'No description provided.'}</p>
        </div>

        ${contract.scopeOfWork ? `
        <div class="section">
          <div class="section-title">Scope of Work</div>
          <div class="terms-content">${contract.scopeOfWork}</div>
        </div>
        ` : ''}

        ${contract.paymentTerms ? `
        <div class="section">
          <div class="section-title">Payment Terms</div>
          <div class="terms-content">${contract.paymentTerms}</div>
        </div>
        ` : ''}

        ${contract.paymentSchedule ? `
        <div class="section">
          <div class="section-title">Payment Schedule</div>
          <div class="terms-content">${contract.paymentSchedule}</div>
        </div>
        ` : ''}

        <div class="section">
          <div class="section-title">Terms and Conditions</div>
          <div class="terms-content">${contract.terms || 'Standard terms and conditions apply.'}</div>
        </div>

        ${contract.type === ContractType.HOURLY && (contract.hoursPerWeek || contract.hourlyRate) ? `
        <div class="section">
          <div class="section-title">Hourly Contract Details</div>
          <div class="info-grid">
            ${contract.hourlyRate ? `
            <div class="info-item">
              <div class="label">Hourly Rate</div>
              <div class="value">${this.formatCurrency(contract.hourlyRate)}</div>
            </div>
            ` : ''}
            ${contract.hoursPerWeek ? `
            <div class="info-item">
              <div class="label">Expected Hours per Week</div>
              <div class="value">${contract.hoursPerWeek} hours</div>
            </div>
            ` : ''}
          </div>
        </div>
        ` : ''}

        <div class="signature-section">
          <div class="signature-block">
            <div style="height: 40px;"></div>
            <div class="signature-label">Client Signature</div>
          </div>
          <div class="signature-block">
            <div style="height: 40px;"></div>
            <div class="signature-label">Freelancer Signature</div>
          </div>
        </div>

        <div class="footer">
          <p><strong>MyVillage Freelance Platform</strong></p>
          <p>This contract was generated electronically and is valid without physical signatures when agreed upon through the platform.</p>
          <p>Document generated on ${generatedAt}</p>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate and download contract as HTML file
   */
  async downloadContractHTML(contract: ExtendedContract): Promise<void> {
    try {
      const htmlContent = this.generateContractHTML(contract);
      const blob = new Blob([htmlContent], { type: 'text/html' });
      const url = URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = `contract-${contract.title.replace(/[^a-zA-Z0-9]/g, '-')}-${contract.id.slice(-8)}.html`;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading contract:', error);
      throw new Error('Failed to generate contract document');
    }
  }

  /**
   * Open contract in new window for printing/PDF saving
   */
  async printContract(contract: ExtendedContract): Promise<void> {
    try {
      const htmlContent = this.generateContractHTML(contract);
      const printWindow = window.open('', '_blank');

      if (!printWindow) {
        throw new Error('Failed to open print window. Please allow popups and try again.');
      }

      printWindow.document.write(htmlContent);
      printWindow.document.close();

      printWindow.addEventListener('load', () => {
        setTimeout(() => {
          printWindow.print();
        }, 500);
      });

    } catch (error) {
      console.error('Error printing contract:', error);
      throw new Error('Failed to open contract for printing');
    }
  }

  /**
   * Main method to handle contract download with user choice
   */
  async downloadContract(
    contract: ExtendedContract,
    method: 'html' | 'print' = 'print'
  ): Promise<void> {
    switch (method) {
      case 'html':
        return this.downloadContractHTML(contract);
      case 'print':
        return this.printContract(contract);
      default:
        throw new Error('Invalid download method');
    }
  }
}

export const contractDownloadService = ContractDownloadService.getInstance();