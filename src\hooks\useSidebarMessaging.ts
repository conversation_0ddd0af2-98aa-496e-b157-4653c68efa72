import { useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useMessaging } from '@/contexts/MessagingContext';

export function useSidebarMessaging() {
  const { openMessagingDrawer, isDrawerOpen } = useMessaging();
  const router = useRouter();

  const handleMessagesClick = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (window.innerWidth < 768 || !isDrawerOpen) {
      openMessagingDrawer();
    } else {
      router.push('/messages');
    }
  }, [openMessagingDrawer, isDrawerOpen, router]);

  return {
    handleMessagesClick,
    isMessagingDrawerOpen: isDrawerOpen,
  };
}
