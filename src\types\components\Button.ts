import { ButtonHTMLAttributes } from 'react';
import { BaseProps, ButtonVariant, LoadableProps, SizeVariant, WithChildren } from './common';

export interface ButtonProps 
  extends Omit<ButtonHTMLAttributes<HTMLButtonElement>, 'children'|'className'>,
    BaseProps,
    LoadableProps,
    WithChildren {
  /** Button variant */
  variant?: ButtonVariant;
  /** Button size */
  size?: SizeVariant | 'default';
  /** Whether the button should render as child */
  asChild?: boolean;
  /** Loading text to show when button is loading */
  loadingText?: string;
}
