import {
  <PERSON>,
  <PERSON>Status,
  <PERSON><PERSON><PERSON>er,
  CreateJobInput,
  UpdateJobInput,
  JobWithProposalList
} from '../../types/features/jobs/job.types';
import {
  JobProposal,
  CreateJobProposalInput,
  ProposalStatus
} from '../../types/features/proposals/proposal.types';
import { jobApi } from './job.api';
import {
  createError,
  ErrorCode,
  isAppError,
} from '../../utils/errorHandler';
import { validateJobFilter, validateJobInput } from './job.validations';

type JobWithProposals = Job & {
  proposals?: {
    items?: JobProposal[];
    nextToken?: string;
  };
};

const handleApiError = (operation: string, error: unknown): never => {
  if (isAppError(error)) {
    throw error;
  }

  const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
  console.error(`[JobService] Error in ${operation}:`, error);

  throw createError(
    `Failed to ${operation.toLowerCase()}: ${errorMessage}`,
    ErrorCode.INTERNAL_SERVER_ERROR,
    { statusCode: 500 }
  );
};

export const jobService = {
  /**
   * Create a new job posting
   * @param input Job creation data
   * @returns The created job
   * @throws {AppError} If input validation fails or job creation fails
   */
  async createJob(input: CreateJobInput): Promise<Job> {
    try {
      validateJobInput(input);
      const job = await jobApi.createJob(input);

      if (!job) {
        throw createError(
          'Failed to create job: No data returned',
          ErrorCode.INTERNAL_SERVER_ERROR,
          { statusCode: 500 }
        );
      }

      return job;
    } catch (error) {
      return handleApiError('createJob', error);
    }
  },

  /**
   * Get a job by ID with its proposals
   * @param id Job ID
   * @returns Job with proposals
   * @throws {AppError} If job is not found or an error occurs
   */
  async getJob(id: string): Promise<JobWithProposalList> {
    try {
      if (!id) {
        throw createError(
          'Job ID is required',
          ErrorCode.VALIDATION_ERROR,
          {
            statusCode: 400,
            details: { field: 'id', message: 'Job ID is required' }
          }
        );
      }

      const job = await jobApi.getJob(id) as JobWithProposals;

      if (!job) {
        throw createError(
          `Job with ID ${id} not found`,
          ErrorCode.NOT_FOUND,
          {
            statusCode: 404,
            details: {
              resource: 'Job',
              id,
              action: 'retrieve',
              suggestions: ['Check if the job ID is correct', 'Verify the job exists']
            }
          }
        );
      }

      const jobWithProposals: JobWithProposalList = {
        ...job,
        proposals: job.proposals?.items || [],
        proposalCount: job.proposals?.items?.length || 0,
        client: job.client ? {
          id: job.client.id,
          name: job.client.name || 'Unknown',
          email: job.client.email || '<EMAIL>',
          avatar: (job.client as any).profilePhoto || undefined
        } : {
          id: job.clientId || 'unknown',
          name: 'Unknown',
          email: '<EMAIL>'
        }
      };

      return jobWithProposals;
    } catch (error) {
      return handleApiError('getJob', error);
    }
  },

  /**
   * Update an existing job
   * @param input Job update data
   * @returns The updated job
   * @throws {AppError} If input validation fails or job update fails
   */
  async updateJob(input: UpdateJobInput): Promise<Job> {
    try {
      if (!input.id) {
        throw createError(
          'Job ID is required for update',
          ErrorCode.VALIDATION_ERROR,
          { statusCode: 400 }
        );
      }

      validateJobInput(input);
      const updatedJob = await jobApi.updateJob(input);

      if (!updatedJob) {
        throw createError(
          `Job with ID ${input.id} not found or update failed`,
          ErrorCode.NOT_FOUND,
          { statusCode: 404 }
        );
      }

      return updatedJob;
    } catch (error) {
      return handleApiError('updateJob', error);
    }
  },

  /**
   * Delete a job by ID
   * @param id Job ID to delete
   * @throws {AppError} If job deletion fails
   */
  async deleteJob(id: string): Promise<void> {
    try {
      if (!id) {
        throw createError(
          'Job ID is required',
          ErrorCode.VALIDATION_ERROR,
          { statusCode: 400 }
        );
      }

      try {
        await jobApi.deleteJob(id);
      } catch (error) {
        throw createError(
          'Failed to delete job',
          ErrorCode.INTERNAL_SERVER_ERROR,
          { statusCode: 500, originalError: error }
        );
      }
    } catch (error) {
      return handleApiError('deleteJob', error);
    }
  },

  /**
   * List jobs with optional filtering and pagination
   * @param filter Optional filters for job listing
   * @returns Paginated list of jobs
   * @throws {AppError} If job listing fails
   */
  async listJobs(filter: JobFilter = {}): Promise<{ items: Job[]; nextToken?: string }> {
    try {
      validateJobFilter(filter);

      const result = await jobApi.listJobs(filter);

      const items = (result.items || []).map(job => ({
        ...job,
        title: job.title || 'Untitled Job',
        description: job.description || '',
        budget: job.budget || 0,
        status: job.status || JobStatus.OPEN,
        skills: Array.isArray(job.skills) ? job.skills : [],
        category: job.category || 'other',
        clientId: job.clientId || '',
        createdAt: job.createdAt || new Date().toISOString(),
        updatedAt: job.updatedAt || new Date().toISOString(),
        proposals: {
          items: job.proposals?.items || [],
          nextToken: job.proposals?.nextToken
        }
      }));

      return {
        items,
        nextToken: result.nextToken
      };
    } catch (error) {
      return handleApiError('listJobs', error);
    }
  },

  /**
   * List jobs for a specific client
   * @param clientId ID of the client
   * @returns Array of jobs for the client
   * @throws {AppError} If client ID is invalid or listing fails
   */
  async listMyJobs(clientId: string): Promise<Job[]> {
    try {
      if (!clientId) {
        throw createError(
          'Client ID is required',
          ErrorCode.VALIDATION_ERROR,
          { statusCode: 400 }
        );
      }

      const jobsResult = await this.listJobs({ clientId });
      const jobs = jobsResult?.items || [];

      return jobs.map(job => ({
        ...job,
        status: (job.status || 'OPEN') as JobStatus,
        proposalCount: job.proposals?.items?.length || 0,
        proposals: {
          items: job.proposals?.items || [],
          nextToken: job.proposals?.nextToken
        },
        title: job.title || 'Untitled Job',
        description: job.description || '',
        budget: job.budget || 0,
        skills: Array.isArray(job.skills) ? job.skills : [],
        isRemote: Boolean(job.isRemote)
      }));
    } catch (error) {
      return handleApiError('listMyJobs', error);
    }
  },

  /**
   * Submit a proposal for a job
   * @param input Proposal details
   * @param freelancerId ID of the freelancer submitting the proposal
   * @returns The created proposal
   * @throws {AppError} If input validation fails or submission fails
   */
  async submitProposal(
    input: Omit<CreateJobProposalInput, 'status'>,
    freelancerId?: string
  ): Promise<JobProposal> {
    try {
      if (!freelancerId) {
        throw createError(
          'Authentication required to submit a proposal',
          ErrorCode.UNAUTHORIZED,
          { 
            statusCode: 401,
            details: { 
              action: 'redirect', 
              to: '/login',
              message: 'Please log in to submit a proposal'
            }
          }
        );
      }

      const proposalInput: Omit<CreateJobProposalInput, 'status'> = {
        jobId: input.jobId,
        coverLetter: input.coverLetter || '',
        bidAmount: input.bidAmount || 0,
        estimatedTime: input.estimatedTime || ''
      };

      const proposal = await jobApi.submitProposal(proposalInput);
      
      if (!proposal) {
        throw createError(
          'Failed to submit proposal. Please try again later.',
          ErrorCode.INTERNAL_SERVER_ERROR,
          { 
            statusCode: 500,
            details: { 
              action: 'retry',
              maxAttempts: 3,
              jobId: input.jobId,
              timestamp: new Date().toISOString()
            }
          }
        );
      }

      const freelancer = proposal.freelancer || {
        id: freelancerId || '',
        name: '',
        email: '',
        profilePhoto: undefined
      };

      const defaultJob: Partial<Job> = {
        id: input.jobId,
        title: '',
        description: '',
        budget: 0,
        status: JobStatus.OPEN,
        skills: [],
        category: 'other',
        clientId: '',
        isRemote: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const result: JobProposal = {
        ...proposal,
        status: 'PENDING' as ProposalStatus,
        bidAmount: proposal.bidAmount || 0,
        coverLetter: proposal.coverLetter || '',
        estimatedTime: proposal.estimatedTime || '',
        createdAt: proposal.createdAt || new Date().toISOString(),
        updatedAt: proposal.updatedAt || new Date().toISOString(),
        id: proposal.id || '',
        jobId: proposal.jobId,
        freelancerId: freelancerId || '',
        job: proposal.job || defaultJob,
        freelancer
      };
      
      return result;
    } catch (error) {
      return handleApiError('listMyJobs', error);
    }
  },
  async applyForJob(input: CreateJobProposalInput): Promise<JobProposal> {
    console.warn('applyForJob is deprecated. Use submitProposal instead.');
    return this.submitProposal(input);
  },

  /**
   * Get all proposals for a specific job
   * @param jobId - The ID of the job
   * @returns Array of job proposals
   * @throws {AppError} If job ID is invalid or an error occurs
   */
  async getJobProposals(jobId: string): Promise<JobProposal[]> {
    try {
      if (!jobId) {
        throw createError(
          'Job ID is required',
          ErrorCode.VALIDATION_ERROR,
          {
            statusCode: 400,
            details: { field: 'jobId', message: 'Job ID is required' }
          }
        );
      }

      const job = await jobApi.getJob(jobId);
      
      if (!job) {
        throw createError(
          `Job with ID ${jobId} not found`,
          ErrorCode.NOT_FOUND,
          { statusCode: 404 }
        );
      }

      return job.proposals?.items || [];
    } catch (error) {
      return handleApiError('getJobProposals', error);
    }
  },

  getJobApplications: async function (jobId: string): Promise<JobProposal[]> {
    console.warn('getJobApplications is deprecated. Use getJobProposals instead.');
    return this.getJobProposals(jobId);
  },

  /**
   * Updates the status of a job application
   * @deprecated Use updateProposalStatus instead
   * @param id - The ID of the proposal/application
   * @param status - The new status to set
   * @returns The updated proposal
   * @throws {AppError} If input validation fails or update fails
   */
  updateApplicationStatus: async function (
    id: string,
    status: ProposalStatus
  ): Promise<JobProposal> {
    console.warn('updateApplicationStatus is deprecated. Use updateProposalStatus instead.');
    
    try {
      if (!id) {
        throw createError(
          'Proposal ID is required',
          ErrorCode.VALIDATION_ERROR,
          {
            statusCode: 400,
            details: { field: 'id', message: 'Proposal ID is required' }
          }
        );
      }

      if (!Object.values(ProposalStatus).includes(status)) {
        throw createError(
          `Invalid status: ${status}`,
          ErrorCode.VALIDATION_ERROR,
          {
            statusCode: 400,
            details: { 
              field: 'status', 
              message: `Status must be one of: ${Object.values(ProposalStatus).join(', ')}`
            }
          }
        );
      }

      return await this.updateProposalStatus(id, status);
    } catch (error) {
      return handleApiError('updateApplicationStatus', error);
    }
  },

  listMyApplications: async function (freelancerId: string): Promise<JobProposal[]> {
    console.warn('listMyApplications is deprecated. Use listMyProposals instead.');
    return this.listMyProposals(freelancerId);
  },

  /**
   * Update the status of a proposal
   * @param id Proposal ID
   * @param status New status
   * @returns The updated proposal
   * @throws {AppError} If update fails or proposal not found
   */
  async updateProposalStatus(
    id: string,
    status: ProposalStatus
  ): Promise<JobProposal> {
    try {
      if (!id) {
        throw createError(
          'Proposal ID is required',
          ErrorCode.VALIDATION_ERROR,
          {
            statusCode: 400,
            details: { field: 'id', message: 'Proposal ID is required' }
          }
        );
      }

      const validStatuses: ProposalStatus[] = [
        ProposalStatus.PENDING,
        ProposalStatus.ACCEPTED,
        ProposalStatus.REJECTED,
        ProposalStatus.WITHDRAWN
      ];
      if (!validStatuses.includes(status)) {
        throw createError(
          `Invalid proposal status. Must be one of: ${validStatuses.join(', ')}`,
          ErrorCode.VALIDATION_ERROR,
          {
            statusCode: 400,
            details: {
              field: 'status',
              validStatuses,
              provided: status
            }
          }
        );
      }

      const updatedProposal = await jobApi.updateProposalStatus({ id, status });

      if (!updatedProposal) {
        throw createError(
          `Proposal with ID ${id} not found`,
          ErrorCode.NOT_FOUND,
          {
            statusCode: 404,
            details: {
              resource: 'Proposal',
              id,
              action: 'update',
              suggestions: ['Verify the proposal ID', 'Check if the proposal was deleted']
            }
          }
        );
      }

      return updatedProposal;
    } catch (error) {
      return handleApiError('updateProposalStatus', error);
    }
  },

  /**
   * Lists all proposals submitted by a specific freelancer
   * @param freelancerId - The ID of the freelancer
   * @returns Array of job proposals with job details
   * @throws {AppError} If freelancer ID is invalid or an error occurs
   */
  async listMyProposals(freelancerId: string): Promise<JobProposal[]> {
    try {
      if (!freelancerId) {
        throw createError(
          'Freelancer ID is required',
          ErrorCode.VALIDATION_ERROR,
          {
            statusCode: 400,
            details: { 
              field: 'freelancerId', 
              message: 'Freelancer ID is required' 
            }
          }
        );
      }

      const jobsResponse = await this.listJobs({ includeProposals: true });

      if (!jobsResponse || !Array.isArray(jobsResponse.items)) {
        throw createError(
          'Failed to fetch job listings',
          ErrorCode.INTERNAL_SERVER_ERROR,
          { statusCode: 500 }
        );
      }

      const proposals: JobProposal[] = [];

      for (const job of jobsResponse.items) {
        if (!job?.proposals?.items?.length) continue;

        const userProposal = job.proposals.items.find(
          (p: { freelancerId: string }) => p && p.freelancerId === freelancerId
        );

        if (userProposal) {
          const client = {
            id: job.client?.id || job.clientId || '',
            name: job.client?.name || 'Unknown Client',
            email: job.client?.email || ''
          };

          proposals.push({
            id: userProposal.id || '',
            jobId: job.id || '',
            freelancerId: userProposal.freelancerId || freelancerId,
            coverLetter: userProposal.coverLetter || '',
            bidAmount: userProposal.bidAmount || 0,
            proposedRate: userProposal.proposedRate || 0,
            status: userProposal.status as ProposalStatus || ProposalStatus.PENDING,
            createdAt: userProposal.createdAt || new Date().toISOString(),
            updatedAt: userProposal.updatedAt || new Date().toISOString(),
            job: {
              ...job,
              id: job.id || '',
              clientId: job.clientId || '',
              title: job.title || 'Untitled Job',
              description: job.description || '',
              budget: job.budget || 0,
              status: job.status || 'OPEN',
              isRemote: Boolean(job.isRemote),
              skills: Array.isArray(job.skills) ? job.skills : [],
              createdAt: job.createdAt || new Date().toISOString(),
              updatedAt: job.updatedAt || new Date().toISOString(),
              client
            }
          });
        }
      }

      return proposals;
    } catch (error) {
      return handleApiError('listMyProposals', error);
    }
  },


  /**
   * Withdraws a job proposal
   * @param proposalId - The ID of the proposal to withdraw
   * @throws {AppError} If proposal ID is invalid or withdrawal fails
   */
  async withdrawProposal(proposalId: string): Promise<void> {
    try {
      if (!proposalId) {
        throw createError(
          'Proposal ID is required',
          ErrorCode.VALIDATION_ERROR,
          {
            statusCode: 400,
            details: { field: 'proposalId', message: 'Proposal ID is required' }
          }
        );
      }

      await jobApi.withdrawProposal(proposalId);
    } catch (error) {
      return handleApiError('withdrawProposal', error);
    }
  },

  /**
   * Withdraws a job application (deprecated)
   * @deprecated Use withdrawProposal instead
   * @param proposalId - The ID of the proposal to withdraw
   * @throws {AppError} If proposal ID is invalid or withdrawal fails
   */
  withdrawApplication: async function (proposalId: string): Promise<void> {
    console.warn('withdrawApplication is deprecated. Use withdrawProposal instead.');
    
    try {
      if (!proposalId) {
        throw createError(
          'Proposal ID is required',
          ErrorCode.VALIDATION_ERROR,
          {
            statusCode: 400,
            details: { field: 'proposalId', message: 'Proposal ID is required' }
          }
        );
      }
      
      return await this.withdrawProposal(proposalId);
    } catch (error) {
      return handleApiError('withdrawApplication', error);
    }
  },

  /**
   * Checks if a freelancer has already submitted a proposal for a specific job
   * @param jobId - The ID of the job to check
   * @param freelancerId - The ID of the freelancer
   * @param job - Optional job object with proposals to check against
   * @returns boolean - True if the freelancer has already submitted a proposal for this job
   * @throws {AppError} If required parameters are missing or an error occurs
   */
  hasSubmittedProposal(
    jobId: string,
    freelancerId: string,
    job?: {
      proposals?: {
        items?: Array<{
          id: string;
          freelancerId: string;
          jobId?: string;
          status?: string;
        }>;
      };
    }
  ): boolean {
    try {
      if (!jobId) {
        throw createError(
          'Job ID is required',
          ErrorCode.VALIDATION_ERROR,
          {
            statusCode: 400,
            details: { field: 'jobId', message: 'Job ID is required' }
          }
        );
      }

      if (!freelancerId) {
        throw createError(
          'Freelancer ID is required',
          ErrorCode.VALIDATION_ERROR,
          {
            statusCode: 400,
            details: { field: 'freelancerId', message: 'Freelancer ID is required' }
          }
        );
      }

      if (process.env.NODE_ENV === 'development' && !job?.proposals?.items) {
        console.warn(
          'hasSubmittedProposal called without job proposals. ' +
          'This may result in unnecessary API calls. ' +
          'Consider passing the job object with proposals for better performance.',
          { jobId, freelancerId }
        );
      }

      if (job?.proposals?.items) {
        return job.proposals.items.some(
          (proposal) =>
            proposal &&
            proposal.freelancerId === freelancerId &&
            (proposal.jobId === jobId || !proposal.jobId)
        );
      }

      // In a real implementation, you would fetch the job with proposals
      // and check if the freelancer has a proposal for it
      // For now, we'll return false to be safe
      return false;
    } catch (error) {
      if (isAppError(error)) {
        throw error;
      }
      
      console.error('Unexpected error in hasSubmittedProposal:', error);
      throw createError(
        'Failed to check for existing proposal',
        ErrorCode.INTERNAL_SERVER_ERROR,
        { statusCode: 500 }
      );
    }
  }
};
