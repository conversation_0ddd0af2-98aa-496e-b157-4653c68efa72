import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

export async function POST(request: NextRequest) {
  try {
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);
    const body = await request.json();
    const { paymentIntentId, amount, reason = 'requested_by_customer', contractId, metadata = {} } = body;

    if (!paymentIntentId) {
      return NextResponse.json(
        { error: 'Payment Intent ID is required.' },
        { status: 400 }
      );
    }

    if (!contractId) {
      return NextResponse.json(
        { error: 'Contract ID is required.' },
        { status: 400 }
      );
    }

    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
    
    if (!paymentIntent.latest_charge) {
      return NextResponse.json(
        { error: 'No charge found for this payment intent.' },
        { status: 400 }
      );
    }

    const refundData: Stripe.RefundCreateParams = {
      charge: paymentIntent.latest_charge as string,
      reason: reason as Stripe.RefundCreateParams.Reason,
      metadata: {
        contractId,
        originalPaymentIntentId: paymentIntentId,
        ...metadata,
      },
    };

    if (amount) {
      const amountInCents = Math.round(amount * 100);
      if (amountInCents > paymentIntent.amount) {
        return NextResponse.json(
          { error: 'Refund amount cannot exceed the original payment amount.' },
          { status: 400 }
        );
      }
      refundData.amount = amountInCents;
    }

    const refund = await stripe.refunds.create(refundData);

    return NextResponse.json({
      id: refund.id,
      amount: refund.amount,
      currency: refund.currency,
      status: refund.status,
      reason: refund.reason,
      created: refund.created,
      metadata: refund.metadata,
    });

  } catch (error) {
    console.error('Error creating refund:', error);
    
    if (error instanceof Stripe.errors.StripeError) {
      return NextResponse.json(
        { 
          error: 'Refund processing error',
          details: error.message,
          type: error.type 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error while processing refund' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);
    const { searchParams } = new URL(request.url);
    const refundId = searchParams.get('refund_id');
    const paymentIntentId = searchParams.get('payment_intent_id');

    if (refundId) {
      const refund = await stripe.refunds.retrieve(refundId);
      
      return NextResponse.json({
        id: refund.id,
        amount: refund.amount,
        currency: refund.currency,
        status: refund.status,
        reason: refund.reason,
        created: refund.created,
        metadata: refund.metadata,
      });
    } else if (paymentIntentId) {
      const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
      
      if (!paymentIntent.latest_charge) {
        return NextResponse.json(
          { error: 'No charge found for this payment intent.' },
          { status: 400 }
        );
      }

      const refunds = await stripe.refunds.list({
        charge: paymentIntent.latest_charge as string,
      });

      return NextResponse.json({
        refunds: refunds.data.map(refund => ({
          id: refund.id,
          amount: refund.amount,
          currency: refund.currency,
          status: refund.status,
          reason: refund.reason,
          created: refund.created,
          metadata: refund.metadata,
        })),
      });
    } else {
      return NextResponse.json(
        { error: 'Either refund_id or payment_intent_id is required' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Error retrieving refund:', error);
    
    if (error instanceof Stripe.errors.StripeError) {
      return NextResponse.json(
        { 
          error: 'Refund retrieval error',
          details: error.message 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error while retrieving refund' },
      { status: 500 }
    );
  }
}