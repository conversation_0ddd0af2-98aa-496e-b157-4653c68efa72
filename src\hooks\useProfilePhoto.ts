import { useState, useEffect, useCallback } from 'react';
import { getCurrentUser, fetchUserAttributes } from 'aws-amplify/auth';
import { deleteProfilePhotoFromS3, checkIfFileExists } from '../utils/profilePhoto';
import { userService } from '@/api/users/user.service';
import S3Service from '@/lib/s3/s3Service';
import { UserProfile, UserRole } from '@/types/features/user';

class ProfilePhotoError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly details?: Record<string, unknown>
  ) {
    super(message);
    this.name = 'ProfilePhotoError';

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ProfilePhotoError);
    }
  }
}

interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

interface ProfilePhotoOptions {
  maxFileSize?: number;
  allowedTypes?: string[];
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
}

const DEFAULT_OPTIONS: ProfilePhotoOptions = {
  maxFileSize: 5 * 1024 * 1024,
  allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
  maxWidth: 1024,
  maxHeight: 1024,
  quality: 0.8,
};

interface UseProfilePhotoReturn {
  isUploading: boolean;
  uploadError: ProfilePhotoError | null;
  uploadProgress: number;
  uploadProfilePhoto: (
    file: File,
    userId: string,
    currentPhotoUrl?: string | null,
    options?: ProfilePhotoOptions
  ) => Promise<string>;
  deleteProfilePhoto: (photoKey: string, userId: string) => Promise<void>;
  resetError: () => void;
}

export const useProfilePhoto = (defaultOptions: ProfilePhotoOptions = {}): UseProfilePhotoReturn => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<ProfilePhotoError | null>(null);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress>({
    loaded: 0,
    total: 0,
    percentage: 0
  });

  const getMergedOptions = useCallback((options: ProfilePhotoOptions = {}) => ({
    ...DEFAULT_OPTIONS,
    ...defaultOptions,
    ...options
  }), [defaultOptions]);

  const [currentUser, setCurrentUser] = useState<UserProfile | null>(null);

  const resetError = useCallback(() => setUploadError(null), []);

  const updateProgress = useCallback((progress: { loaded: number; total: number }) => {
    setUploadProgress({
      loaded: progress.loaded,
      total: progress.total,
      percentage: progress.total > 0 ? Math.round((progress.loaded / progress.total) * 100) : 0
    });
  }, []);

  const validateFile = (file: File, options: ProfilePhotoOptions): void => {
    if (!file) {
      throw new ProfilePhotoError('No file provided', 'NO_FILE');
    }

    if (file.size > options.maxFileSize!) {
      throw new ProfilePhotoError(
        `File size exceeds the maximum allowed size of ${options.maxFileSize! / (1024 * 1024)}MB`,
        'FILE_TOO_LARGE',
        { maxSize: options.maxFileSize, actualSize: file.size }
      );
    }

    if (!options.allowedTypes?.includes(file.type)) {
      throw new ProfilePhotoError(
        `Invalid file type. Allowed types: ${options.allowedTypes?.join(', ')}`,
        'INVALID_FILE_TYPE',
        { fileType: file.type, allowedTypes: options.allowedTypes }
      );
    }
  };

  const processImage = async (file: File, options: ProfilePhotoOptions): Promise<File> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        return reject(new ProfilePhotoError('Could not create canvas context', 'CANVAS_ERROR'));
      }

      const reader = new FileReader();

      reader.onload = (event) => {
        if (!event.target?.result) {
          return reject(new ProfilePhotoError('Failed to read file', 'FILE_READ_ERROR'));
        }

        img.onload = () => {
          try {
            let width = img.width;
            let height = img.height;

            if (width > options.maxWidth! || height > options.maxHeight!) {
              const ratio = Math.min(options.maxWidth! / width, options.maxHeight! / height);
              width = Math.floor(width * ratio);
              height = Math.floor(height * ratio);
            }

            canvas.width = width;
            canvas.height = height;

            ctx.drawImage(img, 0, 0, width, height);

            canvas.toBlob(
              (blob) => {
                if (!blob) {
                  return reject(new ProfilePhotoError('Failed to process image', 'IMAGE_PROCESSING_ERROR'));
                }

                const processedFile = new File(
                  [blob],
                  file.name.replace(/\.[^/.]+$/, '.jpg'),
                  { type: 'image/jpeg' }
                );

                resolve(processedFile);
              },
              'image/jpeg',
              options.quality
            );
          } catch (error) {
            reject(new ProfilePhotoError(
              error instanceof Error ? error.message : 'Image processing failed',
              'IMAGE_PROCESSING_ERROR',
              { error }
            ));
          }
        };

        img.onerror = () => {
          reject(new ProfilePhotoError('Failed to load image', 'IMAGE_LOAD_ERROR'));
        };

        img.src = event.target.result as string;
      };

      reader.onerror = () => {
        reject(new ProfilePhotoError('Failed to read file', 'FILE_READ_ERROR'));
      };

      reader.readAsDataURL(file);
    });
  };

  useEffect(() => {
    const loadUser = async () => {
      try {
        const user = await getCurrentUser();
        const attributes = await fetchUserAttributes();

        const userProfile: UserProfile = {
          id: user.userId,
          sub: user.userId,
          userId: user.userId,
          email: attributes.email || '',
          name: attributes.name || '',
          role: (attributes['custom:role'] as UserRole) || UserRole.FREELANCER,
          createdAt: new Date().toISOString(),
          ...attributes,
        };

        setCurrentUser(userProfile);
      } catch (error) {
        console.error('Error loading user:', error);
      }
    };

    loadUser();
  }, []);

  const uploadProfilePhoto = async (
    file: File,
    userId: string,
    currentPhotoUrl?: string | null,
    options: ProfilePhotoOptions = {}
  ): Promise<string> => {
    const mergedOptions = getMergedOptions(options);

    try {
      if (!file) {
        throw new ProfilePhotoError('No file provided', 'NO_FILE');
      }

      if (!currentUser) {
        throw new ProfilePhotoError('No current user found', 'UNAUTHORIZED');
      }

      validateFile(file, mergedOptions);

      const processedFile = await processImage(file, mergedOptions);

      setIsUploading(true);
      setUploadError(null);
      setUploadProgress({
        loaded: 0,
        total: 0,
        percentage: 0
      });

      const fileExtension = processedFile.name.split('.').pop()?.toLowerCase() || 'jpg';
      const fileName = `user-${userId}-${Date.now()}.${fileExtension}`;
      const fileKey = `profile-photos/${fileName}`;

      if (currentPhotoUrl) {
        try {
          await deleteProfilePhoto(currentPhotoUrl, userId);
        } catch (error) {
          console.warn('Failed to delete old profile photo:', error);
        }
      }

      const photoUrl = await S3Service.uploadFile({
        file: processedFile,
        fileName: fileKey,
        progressCallback: (progress: { loaded: number; total: number }) => {
          updateProgress({
            loaded: progress.loaded,
            total: progress.total
          });
        },
        contentType: 'image/jpeg',
        level: 'protected' as const
      });

      await userService.updateUser({
        id: userId,
        profilePhoto: photoUrl
      });

      if (currentUser) {
        setCurrentUser(prev => ({
          ...prev!,
          photoUrl
        }));
      }

      return photoUrl;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to upload profile photo';
      const errorCode = error instanceof ProfilePhotoError ? error.code : 'UPLOAD_ERROR';
      const errorDetails = error instanceof ProfilePhotoError ? error.details : { error };

      const profilePhotoError = new ProfilePhotoError(errorMessage, errorCode, errorDetails);
      setUploadError(profilePhotoError);
      throw profilePhotoError;
    } finally {
      setIsUploading(false);
      setUploadProgress({
        loaded: 0,
        total: 0,
        percentage: 0
      });
    }
  };

  const deleteProfilePhoto = async (photoKey: string, userId: string): Promise<void> => {
    if (!photoKey) {
      console.error('No photo key provided for deletion');
      return;
    }

    setIsUploading(true);
    setUploadError(null);

    try {

      try {
        await deleteProfilePhotoFromS3(photoKey);

        await new Promise(resolve => setTimeout(resolve, 1000));

        const fileStillExists = await checkIfFileExists(photoKey);
        if (fileStillExists) {
          console.warn('File still exists after deletion, S3 propagation delay may be in effect');
        }

      } catch (error) {
        console.error('❌ Failed to delete or verify photo deletion from S3:', error);
        throw new Error('Failed to delete photo from storage. Please try again.');
      }

      await userService.updateUser({
        id: userId,
        profilePhoto: null,
        name: currentUser?.name || currentUser?.given_name || 'User',
        email: currentUser?.email || '',
        bio: currentUser?.bio || '',
        skills: currentUser?.skills || [],
      });

      if (currentUser) {
        setCurrentUser(prevUser => {
          if (!prevUser) return null;
          const updatedUser = { ...prevUser };
          delete updatedUser.profilePhoto;
          return updatedUser;
        });
      }

      window.dispatchEvent(new CustomEvent('profilePhotoUpdated'));

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete profile photo';
      const errorCode = error instanceof ProfilePhotoError ? error.code : 'DELETE_ERROR';
      const profilePhotoError = new ProfilePhotoError(errorMessage, errorCode, { error });
      setUploadError(profilePhotoError);
      throw profilePhotoError;
    } finally {
      setIsUploading(false);
    }
  };

  return {
    isUploading,
    uploadError,
    uploadProgress: uploadProgress.percentage,
    uploadProfilePhoto,
    deleteProfilePhoto,
    resetError,
  };
};

export default useProfilePhoto;
