import { gql } from '@apollo/client';

export const GET_CONTRACT = gql`
  query GetContract($id: ID!) {
    getContract(id: $id) {
      id
      jobId
      proposalId
      clientId
      freelancerId
      title
      description
      type
      status
      terms
      startDate
      endDate
      budget
      createdAt
      updatedAt
    }
  }
`;

export const LIST_CONTRACTS = gql`
  query ListContracts(
    $filter: ModelContractFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listContracts(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        jobId
        proposalId
        clientId
        freelancerId
        title
        description
        type
        status
        terms
        startDate
        endDate
        budget
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;

export const GET_CONTRACTS_BY_JOB = gql`
  query GetContractsByJob($jobId: ID!) {
    getContractsByJob(jobId: $jobId) {
      items {
        id
        title
        status
        type
        startDate
        endDate
        budget
        createdAt
      }
    }
  }
`;

export const GET_USER_CONTRACTS = gql`
  query GetUserContracts($userId: ID!, $status: ContractStatus) {
    getUserContracts(userId: $userId, status: $status) {
      items {
        id
        jobId
        title
        status
        type
        startDate
        endDate
        budget
        createdAt
      }
    }
  }
`;

export const GET_CONTRACT_WORK_SUBMISSIONS = gql`
  query GetContractWorkSubmissions($contractId: ID!) {
    listWorkSubmissions(filter: { contractId: { eq: $contractId } }) {
      items {
        id
        contractId
        description
        attachments
        links
        submittedAt
        reviewedAt
        status
        reviewNotes
        submittedById
        reviewedById
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;

export const GET_CONTRACT_DELIVERABLES = gql`
  query GetContractDeliverables($contractId: ID!) {
    listDeliverables(filter: { contractId: { eq: $contractId } }) {
      items {
        id
        contractId
        title
        description
        dueDate
        status
        attachments
        submissionNotes
        reviewNotes
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;

export const GET_CONTRACT_PAYMENT_SCHEDULES = gql`
  query GetContractPaymentSchedules($contractId: ID!) {
    listPaymentSchedules(filter: { contractId: { eq: $contractId } }) {
      items {
        id
        contractId
        amount
        dueDate
        status
        description
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;

export const GET_CONTRACT_PAYMENTS = gql`
  query GetContractPayments($contractId: ID!) {
    listPayments(filter: { contractId: { eq: $contractId } }) {
      items {
        id
        contractId
        amount
        status
        method
        paidAt
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;
