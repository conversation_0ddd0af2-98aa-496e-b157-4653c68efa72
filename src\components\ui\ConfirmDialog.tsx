import React, { ReactNode } from "react";
import { <PERSON><PERSON> } from "./Button";
import { Icon } from "./Icon";

export interface ConfirmDialogProps {
  /**
   * Whether the dialog is open
   */
  open: boolean;
  /**
   * Dialog title
   */
  title?: string;
  /**
   * Dialog message/description
   */
  message?: ReactNode;
  /**
   * Text for the confirm button
   * @default 'Confirm'
   */
  confirmText?: string;
  /**
   * Text for the cancel button
   * @default 'Cancel'
   */
  cancelText?: string;
  /**
   * Variant for the confirm button
   * @default 'default'
   */
  confirmVariant?: "default" | "destructive" | "outline" | "ghost";
  /**
   * Callback when the confirm button is clicked
   */
  onConfirm?: () => void;
  /**
   * Callback when the cancel button is clicked or the dialog is closed
   */
  onCancel: () => void;
  /**
   * Whether the confirm action is in progress
   * @default false
   */
  isLoading?: boolean;
  /**
   * Additional class name for the dialog
   */
  className?: string;
  /**
   * Custom content to render inside the dialog
   */
  children?: ReactNode;
  /**
   * Whether to show the footer with action buttons
   * @default true
   */
  showFooter?: boolean;
}

export const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  open,
  title,
  message,
  confirmText = "Confirm",
  cancelText = "Cancel",
  confirmVariant = "default",
  onConfirm,
  onCancel,
  isLoading = false,
  children,
  showFooter = true,
  className = "",
}) => {
  if (!open) return null;

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center p-4"
      style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
      onClick={onCancel}
    >
      <div 
        className={`bg-white dark:bg-gray-800 rounded-lg shadow-2xl w-full max-w-lg min-w-[320px] sm:min-w-[400px] transform transition-all duration-200 scale-100 ${className}`}
        onClick={(e) => e.stopPropagation()}
        style={{
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1)'
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          {title && (
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {title}
            </h3>
          )}
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors duration-200 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
            aria-label="Close"
          >
            <Icon name="X" className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 min-h-[80px]">
          {children || (message && (
            <div className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
              {message}
            </div>
          ))}
        </div>

        {/* Footer */}
        {showFooter && (typeof onConfirm === 'function' || typeof onCancel === 'function') && (
          <div className="bg-gray-50 dark:bg-gray-800/50 px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
            {onCancel && (
              <Button
                variant="outline"
                onClick={onCancel}
                disabled={isLoading}
                className="min-w-[120px] h-10 px-4"
              >
                {cancelText}
              </Button>
            )}
            {onConfirm && (
              <Button
                variant={confirmVariant}
                onClick={onConfirm}
                disabled={isLoading}
                className="min-w-[120px] h-10 px-4"
              >
                {isLoading ? (
                  <>
                    <Icon
                      name="Loader2"
                      className="mr-2 h-4 w-4 animate-spin"
                    />
                    {confirmText}
                  </>
                ) : (
                  confirmText
                )}
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ConfirmDialog;
