import React, { useState, useEffect } from "react";
import {
  Tabs as TabsPrimitive,
  TabItem as TabItemType,
} from "@/components/ui/Tabs";

interface HandbookTabsProps {
  defaultValue: string;
  tabs: Array<{
    value: string;
    label: string;
    content: React.ReactNode;
  }>;
}

export const HandbookTabs: React.FC<HandbookTabsProps> = ({
  defaultValue,
  tabs,
}) => {
  const [activeTab, setActiveTab] = useState(defaultValue);

  useEffect(() => {
    setActiveTab(defaultValue);
  }, [defaultValue]);

  const tabItems: TabItemType[] = tabs.map((tab) => ({
    id: tab.value,
    label: tab.label,
    content: tab.content,
    className: "cursor-pointer",
  }));

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  return (
    <TabsPrimitive
      items={tabItems}
      defaultTab={activeTab}
      onChange={handleTabChange}
      className="w-full"
      variant="default"
    />
  );
};

export default HandbookTabs;
