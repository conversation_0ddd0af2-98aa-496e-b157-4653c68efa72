import React from "react";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Card } from "@/components/ui/Card";
import { Badge } from "@/components/ui/Badge";
import { ConfirmDialogExample } from "../examples/ConfirmDialogExample";
import { ComponentCard } from "../examples/ComponentCard";

export const UIComponents = () => {
  return (
    <div className="space-y-12">
      <h2 className="text-3xl font-bold tracking-tight">UI Components</h2>

      <ComponentCard
        title="Confirm Dialog"
        description="A customizable dialog for confirming user actions."
        code={`<ConfirmDialog
  isOpen={isOpen}
  onClose={handleClose}
  onConfirm={handleConfirm}
  title="Confirm Action"
  description="Are you sure you want to perform this action?"
  confirmText="Confirm"
  cancelText="Cancel"
  variant="default"
/>`}
        usage={`import { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { ConfirmDialog } from '@/components/ui/ConfirmDialog';

export function MyComponent() {
  const [isOpen, setIsOpen] = useState(false);
  
  const handleConfirm = () => {
    setIsOpen(false);
  };

  return (
    <>
      <Button onClick={() => setIsOpen(true)}>Show Dialog</Button>
      <ConfirmDialog
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onConfirm={handleConfirm}
        title="Confirm Action"
        description="Are you sure you want to perform this action?"
        confirmText="Confirm"
        cancelText="Cancel"
        variant="default"
      />
    </>
  );
}`}
        props={[
          {
            name: "isOpen",
            type: "boolean",
            default: "false",
            description: "Controls the visibility of the dialog",
          },
          {
            name: "onClose",
            type: "() => void",
            default: "false",
            description: "Callback when the dialog is closed",
          },
          {
            name: "onConfirm",
            type: "() => void",
            default: "false",
            description: "Callback when the confirm button is clicked",
          },
          {
            name: "title",
            type: "string",
            default: "false",
            description: "The title of the dialog",
          },
          {
            name: "description",
            type: "string",
            default: "false",
            description: "The description text shown in the dialog",
          },
          {
            name: "confirmText",
            type: "string",
            default: '"Confirm"',
            description: "Text for the confirm button",
          },
          {
            name: "cancelText",
            type: "string",
            default: '"Cancel"',
            description: "Text for the cancel button",
          },
          {
            name: "variant",
            type: '"default" | "destructive" | "outline"',
            default: '"default"',
            description: "Variant of the confirm button",
          },
          {
            name: "isLoading",
            type: "boolean",
            default: "false",
            description: "Show loading state on confirm button",
          },
        ]}
      >
        <ConfirmDialogExample />
      </ComponentCard>

      <ComponentCard
        title="Button"
        description="A button component with multiple variants and sizes."
        code={`<Button>Click me</Button>`}
        usage={`import { Button } from '@/components/ui/Button';

<Button variant="default">Default</Button>
<Button variant="secondary">Secondary</Button>
<Button variant="outline">Outline</Button>
<Button variant="ghost">Ghost</Button>
<Button variant="link">Link</Button>`}
        props={[
          {
            name: "variant",
            type: '"default" | "secondary" | "outline" | "ghost" | "link"',
            default: '"default"',
            description: "The variant of the button",
          },
          {
            name: "size",
            type: '"default" | "sm" | "lg" | "icon"',
            default: '"default"',
            description: "The size of the button",
          },
          {
            name: "asChild",
            type: "boolean",
            default: "false",
            description: "Render as a child component",
          },
        ]}
      >
        <div className="flex flex-wrap gap-2">
          <Button variant="default">Default</Button>
          <Button variant="secondary">Secondary</Button>
          <Button variant="outline">Outline</Button>
          <Button variant="ghost">Ghost</Button>
          <Button variant="link">Link</Button>
        </div>
      </ComponentCard>

      <ComponentCard
        title="Input"
        description="A basic input field with various types and states."
        code={`<Input placeholder="Enter text" />`}
        usage={`import { Input } from '@/components/ui/Input';

<Input type="text" placeholder="Enter text" />
<Input type="email" placeholder="<EMAIL>" />
<Input type="password" placeholder="Enter password" />`}
        props={[
          {
            name: "type",
            type: "string",
            default: '"text"',
            description: "The type of the input",
          },
          {
            name: "placeholder",
            type: "string",
            default: '""',
            description: "The placeholder text",
          },
          {
            name: "disabled",
            type: "boolean",
            default: "false",
            description: "Whether the input is disabled",
          },
        ]}
      >
        <div className="space-y-4">
          <Input placeholder="Enter your name" />
          <Input type="email" placeholder="<EMAIL>" />
          <Input type="password" placeholder="Enter password" />
          <Input disabled placeholder="Disabled input" />
        </div>
      </ComponentCard>

      <ComponentCard
        title="Card"
        description="A flexible container for displaying content with a header and content area."
        code={`<Card>
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
    <CardDescription>Card Description</CardDescription>
  </CardHeader>
  <CardContent>
    <p>Card Content</p>
  </CardContent>
</Card>`}
        usage={`import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/Card';

<Card>
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
    <CardDescription>Optional description</CardDescription>
  </CardHeader>
  <CardContent>
    <p>Your content here</p>
  </CardContent>
</Card>`}
      >
        <Card>
          <div className="p-4">
            <h3 className="font-medium">Example Card</h3>
            <p className="text-sm text-muted-foreground">
              This is an example of a card component with some sample content.
            </p>
          </div>
        </Card>
      </ComponentCard>

      <ComponentCard
        title="Badge"
        description="A small badge component for displaying status, counts, or labels."
        code={`<Badge>Badge</Badge>`}
        usage={`import { Badge } from '@/components/ui/Badge';

<Badge>Default</Badge>
<Badge variant="secondary">Secondary</Badge>
<Badge variant="outline">Outline</Badge>`}
        props={[
          {
            name: "variant",
            type: '"default" | "secondary" | "outline" | "destructive"',
            default: '"default"',
            description: "The variant of the badge",
          },
        ]}
      >
        <div className="flex flex-wrap gap-2">
          <Badge>Default</Badge>
          <Badge variant="secondary">Secondary</Badge>
          <Badge variant="outline">Outline</Badge>
          <Badge variant="destructive">Destructive</Badge>
        </div>
      </ComponentCard>
    </div>
  );
};

export default UIComponents;
