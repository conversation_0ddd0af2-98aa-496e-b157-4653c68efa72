"use client";

import React, { createContext, useContext, useState, useCallback, useEffect } from "react";
import { loadStripe, Stripe } from "@stripe/stripe-js";

interface StripeContextType {
  stripe: Stripe | null;
  isLoading: boolean;
  error: string | null;
  initializeStripe?: () => Promise<void>;
}

const StripeContext = createContext<StripeContextType>({
  stripe: null,
  isLoading: false,
  error: null,
});

export const useStripeContext = () => {
  const context = useContext(StripeContext);
  if (!context) {
    throw new Error("useStripeContext must be used within a StripeProvider");
  }
  return context;
};

interface StripeProviderProps {
  children: React.ReactNode;
}

export const StripeProvider: React.FC<StripeProviderProps> = ({ children }) => {
  const [stripe, setStripe] = useState<Stripe | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  const initializeStripe = useCallback(async () => {
    if (isInitialized) return;

    try {
      setIsLoading(true);
      const publishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;

      if (!publishableKey) {
        throw new Error(
          "Stripe publishable key not found. Please add NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY to your environment variables."
        );
      }

      if (publishableKey.includes('51Hnz8UG4RXqAi3j8') || 
          publishableKey.includes('51234567890abcdef') ||
          !publishableKey.startsWith("pk_")) {
        
        console.warn('Using mock Stripe instance for development');
        const mockStripe = {
          confirmPayment: async () => ({ error: null, paymentIntent: { status: 'succeeded' } }),
          retrievePaymentIntent: async () => ({ paymentIntent: { status: 'succeeded' } }),
        } as any;
        
        setStripe(mockStripe);
        return;
      }

      if (!publishableKey.startsWith("pk_")) {
        throw new Error(
          'Invalid Stripe publishable key format. Key should start with "pk_".'
        );
      }

      const stripeInstance = await loadStripe(publishableKey);

      if (!stripeInstance) {
        throw new Error(
          "Failed to load Stripe. Please check your publishable key."
        );
      }

      setStripe(stripeInstance);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to initialize Stripe";
      setError(errorMessage);
      console.error("Stripe initialization error:", errorMessage);
    } finally {
      setIsLoading(false);
      setIsInitialized(true);
    }
  }, [isInitialized]);

  useEffect(() => {
    initializeStripe();
  }, [initializeStripe]);

  const contextValue: StripeContextType = {
    stripe,
    isLoading,
    error,
    initializeStripe,
  };

  if (isLoading) {
    return (
      <StripeContext.Provider value={contextValue}>
        {children}
      </StripeContext.Provider>
    );
  }

  if (error || !stripe) {
    return (
      <StripeContext.Provider value={contextValue}>
        {children}
      </StripeContext.Provider>
    );
  }

  return (
    <StripeContext.Provider value={contextValue}>
      {children}
    </StripeContext.Provider>
  );
};
