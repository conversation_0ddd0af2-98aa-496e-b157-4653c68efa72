import { dashboardApi } from './dashboard.api';
import { jobService } from '../jobs/job.service';
import { userService } from '../users/user.service';
import { skillService } from '../skills/skill.service';
import { jobCategoryService } from '../job-categories/job-category.service';
import type { GraphQLResponse, AdminDashboardData } from './dashboard.types';
import { JobStatus } from '../../types/features/jobs/job.types';
import { UserRole } from '../../types/enums';
import {
  ValidationError,
  ResourceNotFoundError,
  RateLimitError,
  type ServiceError
} from '../../types/errors';

const DASHBOARD_CACHE_TTL = 5 * 60 * 1000;

const dashboardCache = new Map<string, { data: any; timestamp: number }>();

function isServiceError(error: unknown): error is ServiceError {
  return error instanceof Error && 'name' in error;
}

function handleDashboardError(operation: string, error: unknown, id: string): never {
  if (isServiceError(error)) {
    throw error;
  }

  const errorMessage = error instanceof Error ? error.message : 'Unknown error';

  if (errorMessage.includes('not found')) {
    throw new ResourceNotFoundError('Dashboard data', id);
  }

  if (errorMessage.includes('rate limit') || errorMessage.includes('too many requests')) {
    throw new RateLimitError('Too many requests. Please try again later.');
  }

  console.error(`Error in ${operation}:`, {
    error,
    id,
    errorMessage,
    stack: error instanceof Error ? error.stack : undefined
  });

  throw new Error(`Failed to ${operation.toLowerCase()}: ${errorMessage}`);
}

/**
 * Validates an ID with basic security checks
 * @param id The ID to validate
 * @param idType Type of ID for error messages
 */
function validateId(id: string, idType: 'client' | 'freelancer'): void {
  if (!id) {
    throw new ValidationError(`${idType} ID is required`, 'id');
  }

  const idRegex = /^[a-z0-9-_]{8,64}$/i;
  if (!idRegex.test(id)) {
    throw new ValidationError(
      `Invalid ${idType} ID format. Must be 8-64 characters long and can only contain letters, numbers, dashes, and underscores.`,
      'id',
      { receivedId: id }
    );
  }
}

export interface DashboardService {
  getClientDashboardData: (clientId: string, forceRefresh?: boolean) => Promise<GraphQLResponse>;
  getFreelancerDashboardData: (freelancerId: string, forceRefresh?: boolean) => Promise<GraphQLResponse>;
  getAdminDashboardData: (forceRefresh?: boolean) => Promise<AdminDashboardData>;
  clearCache: () => void;
}

class DashboardServiceImpl implements DashboardService {
  async getAdminDashboardData(forceRefresh = false): Promise<AdminDashboardData> {
    const cacheKey = 'admin_dashboard';
    const now = Date.now();

    if (!forceRefresh) {
      const cached = dashboardCache.get(cacheKey);
      if (cached && now - cached.timestamp < DASHBOARD_CACHE_TTL) {
        return cached.data;
      }
    }

    try {
      const [usersResponse, jobsResponse, skillsResponse, categoriesResponse] = await Promise.all([
        userService.listUsers({ limit: 1000 }).catch(() => ({ items: [] })),
        jobService.listJobs({ limit: 1000 }).catch(() => ({ items: [] })),
        skillService.listSkills({ limit: 1000 }).catch(() => ({ items: [] })),
        jobCategoryService.listJobCategories(undefined, 1000).catch(() => ({ items: [] }))
      ]);

      const users = usersResponse.items || [];
      const jobs = jobsResponse.items || [];
      const skills = skillsResponse.items || [];
      const categories = categoriesResponse.items || [];

      const now30DaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

      const totalJobs = jobs.length;
      const openJobs = jobs.filter(j => j.status === JobStatus.OPEN).length;
      const inProgressJobs = jobs.filter(j => j.status === JobStatus.IN_PROGRESS).length;
      const completedJobs = jobs.filter(j => j.status === JobStatus.COMPLETED).length;
      const cancelledJobs = jobs.filter(j => j.status === JobStatus.CANCELLED).length;
      const newJobsThisMonth = jobs.filter(j => new Date(j.createdAt) > now30DaysAgo).length;

      const totalUsers = users.length;
      const totalClients = users.filter(u => u.role === UserRole.CLIENT).length;
      const totalFreelancers = users.filter(u => u.role === UserRole.FREELANCER).length;
      const totalAdmins = users.filter(u => u.role === UserRole.ADMIN).length;
      const newUsersThisMonth = users.filter(u => new Date(u.createdAt) > now30DaysAgo).length;

      const totalSkills = skills.length;
      const activeSkills = skills.filter(s => s.isActive).length;
      const inactiveSkills = skills.filter(s => !s.isActive).length;

      const totalJobBudget = jobs.reduce((sum, job) => sum + (job.budget || 0), 0);
      const averageJobBudget = totalJobs > 0 ? totalJobBudget / totalJobs : 0;

      const totalProposals = jobs.reduce((sum, job) => sum + (job.proposals?.items?.length || 0), 0);

      const recentJobs = jobs
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, 5)
        .map(job => ({
          id: job.id,
          title: job.title,
          status: job.status,
          createdAt: job.createdAt,
          client: {
            name: job.client?.name || 'Unknown Client'
          },
          budget: job.budget
        }));

      const recentSkills = skills
        .sort((a, b) => new Date(b.createdAt || '').getTime() - new Date(a.createdAt || '').getTime())
        .slice(0, 5)
        .map(skill => ({
          id: skill.id,
          name: skill.name,
          isActive: skill.isActive,
          category: skill.jobCategoryId || 'uncategorized',
          createdAt: skill.createdAt || new Date().toISOString()
        }));

      const response: AdminDashboardData & {
        totalUsers: number;
        totalClients: number;
        totalFreelancers: number;
        totalAdmins: number;
        newUsersThisMonth: number;
        newJobsThisMonth: number;
        totalJobBudget: number;
        averageJobBudget: number;
        cancelledJobs: number;
        totalJobCategories: number;
      } = {
        totalJobs,
        openJobs,
        inProgressJobs,
        completedJobs,
        totalSkills,
        activeSkills,
        inactiveSkills,
        totalProposals,
        recentJobs,
        recentSkills,

        totalUsers,
        totalClients,
        totalFreelancers,
        totalAdmins,
        newUsersThisMonth,
        newJobsThisMonth,
        totalJobBudget,
        averageJobBudget,
        cancelledJobs,
        totalJobCategories: categories.length
      };

      dashboardCache.set(cacheKey, {
        data: response,
        timestamp: now
      });

      return response;
    } catch (error) {
      console.error('Error fetching admin dashboard data:', error);
      const fallbackResponse: AdminDashboardData = {
        totalJobs: 0,
        openJobs: 0,
        inProgressJobs: 0,
        completedJobs: 0,
        totalSkills: 0,
        activeSkills: 0,
        inactiveSkills: 0,
        totalProposals: 0,
        recentJobs: [],
        recentSkills: []
      };

      return fallbackResponse;
    }
  }

  async getClientDashboardData(clientId: string, forceRefresh = false): Promise<GraphQLResponse> {
    try {
      validateId(clientId, 'client');

      const cacheKey = `client:${clientId}`;
      const cachedData = dashboardCache.get(cacheKey);

      if (!forceRefresh && cachedData && (Date.now() - cachedData.timestamp) < DASHBOARD_CACHE_TTL) {
        return cachedData.data;
      }

      const response = await dashboardApi.getClientDashboardData(clientId);
      const data = response.getUser || response;

      if (!data || !data.id) {
        throw new Error('Invalid dashboard data received from server');
      }

      dashboardCache.set(cacheKey, {
        data,
        timestamp: Date.now()
      });

      return data;
    } catch (error) {
      return handleDashboardError('getClientDashboardData', error, clientId);
    }
  }

  async getFreelancerDashboardData(freelancerId: string, forceRefresh = false): Promise<GraphQLResponse> {
    try {
      validateId(freelancerId, 'freelancer');

      const cacheKey = `freelancer:${freelancerId}`;
      const cachedData = dashboardCache.get(cacheKey);

      if (!forceRefresh && cachedData && (Date.now() - cachedData.timestamp) < DASHBOARD_CACHE_TTL) {
        return cachedData.data;
      }

      const response = await dashboardApi.getFreelancerDashboardData(freelancerId);
      const data = response.getUser || response;

      if (!data || !data.id) {
        throw new Error('Invalid dashboard data received from server');
      }

      dashboardCache.set(cacheKey, {
        data,
        timestamp: Date.now()
      });

      return data;
    } catch (error) {
      return handleDashboardError('getFreelancerDashboardData', error, freelancerId);
    }
  }

  clearCache(): void {
    dashboardCache.clear();
  }
}

const dashboardService = new DashboardServiceImpl();

export default dashboardService;
