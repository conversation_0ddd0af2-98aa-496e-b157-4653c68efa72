'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";
import { jobCategoryService } from '@/api/job-categories/job-category.service';
import { UpdateJobCategoryInput } from "@/types/features/job-categories/job-category.types";
import { JobCategory } from "@/types/features/job-categories/job-category.types";
import { Icon } from '@/components/ui/Icon';
import { ContentHeader } from '@/components/layout/ContentHeader';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Textarea } from '@/components/ui/Textarea';
import useToaster from '@/hooks/useToaster';

export default function EditJobCategoryPage() {
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const router = useRouter();
  const params = useParams();
  const { showSuccess, showError } = useToaster();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [category, setCategory] = useState<JobCategory | null>(null);

  const [formData, setFormData] = useState<UpdateJobCategoryInput>({
    id: params.id as string,
    name: "",
    description: "",
    isActive: true,
  });

  useEffect(() => {
    const loadCategory = async () => {
      if (!isAuthenticated || !params.id) return;

      try {
        setIsLoading(true);
        const categoryData = await jobCategoryService.getJobCategory(params.id as string);
        
        if (!categoryData) {
          throw new Error('Job category not found');
        }
        
        setCategory(categoryData);
        setFormData({
          id: categoryData.id,
          name: categoryData.name,
          description: categoryData.description || '',
          isActive: categoryData.isActive,
        });
      } catch (err: unknown) {
        console.error('Error loading job category:', err);
        const errorMessage = 'Failed to load job category data. Please try again.';
        setError(errorMessage);
        showError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    loadCategory();
  }, [isAuthenticated, params.id, showError]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setIsSubmitting(true);
      setError(null);
      
      if (typeof formData.name === 'undefined' || !formData.name.trim()) {
        setError('Category name is required.');
        return;
      }

      await jobCategoryService.updateJobCategory(formData);
      
      showSuccess('Job category updated successfully');
      
      router.push('/admin/job-categories');
    } catch (err: unknown) {
      console.error('Error updating job category:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to update job category. Please try again.';
      setError(errorMessage);
      showError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    router.push('/admin/job-categories');
  };

  if (!authLoading && (!isAuthenticated || user?.attributes?.['custom:role'] !== 'ADMIN')) {
    router.push('/login');
    return null;
  }

  if (authLoading || !isAuthenticated || isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Icon name="Loader2" size="xl" className="animate-spin text-blue-500" />
      </div>
    );
  }

  if (!category) {
    return (
      <div className="w-full max-w-6xl mx-auto p-4 sm:p-6">
        <div className="text-center py-8">
          <Icon name="AlertCircle" size="xl" className="mx-auto text-red-500 mb-4" />
          <h2 className="text-lg font-semibold text-gray-900 mb-2">Category Not Found</h2>
          <p className="text-gray-600 mb-4">The job category you&apos;re looking for doesn&apos;t exist.</p>
          <Button onClick={() => router.push('/admin/job-categories')}>
            Back to Job Categories
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-4 sm:p-6">
      <ContentHeader
        title="Edit Job Category"
        subtitle="Update the job category details below."
        breadcrumbs={[
          { label: 'Dashboard', href: '/admin/dashboard' },
          { label: 'Job Categories', href: '/admin/job-categories' },
          { label: category.name, href: `/admin/job-categories/${category.id}` },
          { label: 'Edit', current: true }
        ]}
        showBackButton={true}
      />
      
      {error && (
        <div className="rounded-md bg-red-50 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <Icon name="XCircle" size="md" className="text-red-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}
      
      <div className="bg-card rounded-lg shadow-sm p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Category Name *
            </label>
            <Input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="Enter job category name"
              required
              className="w-full"
            />
            <p className="mt-1 text-sm text-gray-500">
              Choose a clear, descriptive name for the job category (e.g., &quot;Web Development&quot;, &quot;Graphic Design&quot;).
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <Textarea
              value={formData.description || ''}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Enter a detailed description of this job category"
              rows={4}
              className="w-full"
            />
            <p className="mt-1 text-sm text-gray-500">
              Provide a clear description that helps users understand what types of jobs belong in this category.
            </p>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="isActive"
              checked={formData.isActive}
              onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
              className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
            />
            <label htmlFor="isActive" className="text-sm font-medium text-gray-700">
              Active (category will be available for job posting)
            </label>
          </div>

          <div className="flex justify-end space-x-3 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
              className="min-w-[100px]"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || typeof formData.name === 'undefined' || !formData.name?.trim()}
              className="min-w-[100px]"
            >
              {isSubmitting ? (
                <>
                  <Icon name="Loader2" size="sm" className="animate-spin mr-2" />
                  Updating...
                </>
              ) : (
                <>
                  <Icon name="Check" size="sm" className="mr-2" />
                  Update Category
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}