import React from "react";
import { Button } from "@/components/ui/Button";

interface ColorSwatchProps {
  name: string;
  value: string;
  className?: string;
}

const ColorSwatch: React.FC<ColorSwatchProps> = ({
  name,
  value,
  className = "",
}) => (
  <div className="flex flex-col items-center space-y-2">
    <div
      className={`w-24 h-24 rounded-lg ${className} border border-gray-200`}
    ></div>
    <div className="text-center">
      <div className="font-medium text-sm">{name}</div>
      <div className="text-xs text-gray-500">{value}</div>
    </div>
  </div>
);

export const DesignSystem = () => {
  return (
    <div className="space-y-8">
      <section>
        <h2 className="text-2xl font-bold mb-6">Color Palette</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-6">
          <ColorSwatch name="Primary" value="#4CAF50" className="bg-primary" />
          <ColorSwatch
            name="Primary Dark"
            value="#388E3C"
            className="bg-fern-green-600"
          />
          <ColorSwatch
            name="Secondary"
            value="#A5D6A7"
            className="bg-secondary"
          />
          <ColorSwatch
            name="Secondary Dark"
            value="#81C784"
            className="bg-sage-300"
          />
          <ColorSwatch
            name="Destructive"
            value="#EF4444"
            className="bg-destructive"
          />
          <ColorSwatch
            name="Destructive Dark"
            value="#DC2626"
            className="bg-red-600"
          />
        </div>
      </section>

      <section>
        <h2 className="text-2xl font-bold mb-6">Typography</h2>
        <div className="space-y-4">
          <div>
            <h1 className="text-4xl font-bold mb-2">Heading 1</h1>
            <p className="text-gray-600">Font: Inter, 48px, Semibold</p>
          </div>
          <div>
            <h2 className="text-3xl font-bold mb-2">Heading 2</h2>
            <p className="text-gray-600">Font: Inter, 36px, Semibold</p>
          </div>
          <div>
            <h3 className="text-2xl font-bold mb-2">Heading 3</h3>
            <p className="text-gray-600">Font: Inter, 24px, Semibold</p>
          </div>
          <div>
            <p className="text-base mb-2">Body Text</p>
            <p className="text-gray-600">Font: Inter, 16px, Regular</p>
          </div>
          <div>
            <p className="text-sm text-gray-500 mb-2">Caption/Small Text</p>
            <p className="text-gray-600">Font: Inter, 14px, Regular</p>
          </div>
        </div>
      </section>

      <section>
        <h2 className="text-2xl font-bold mb-6">Buttons</h2>
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-4">Primary Buttons</h3>
            <div className="flex flex-wrap gap-4">
              <Button variant="default">Primary</Button>
              <Button variant="default" disabled>
                Disabled
              </Button>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-medium mb-4">Secondary Buttons</h3>
            <div className="flex flex-wrap gap-4">
              <Button variant="secondary">Secondary</Button>
              <Button variant="secondary" disabled>
                Disabled
              </Button>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-medium mb-4">Outline Buttons</h3>
            <div className="flex flex-wrap gap-4">
              <Button variant="outline">Outline</Button>
              <Button variant="outline" disabled>
                Disabled
              </Button>
            </div>
          </div>
        </div>
      </section>

      <section>
        <h2 className="text-2xl font-bold mb-6">Spacing</h2>
        <div className="space-y-4">
          {[0, 1, 2, 3, 4, 5, 6, 8, 10, 12, 16, 20, 24, 32, 40, 48, 56, 64].map(
            (size) => (
              <div key={size} className="flex items-center">
                <div className="w-20 text-sm text-gray-500">{size * 4}px</div>
                <div className="h-6 bg-gray-200 flex-1 flex items-center">
                  <div
                    className="h-4 bg-primary"
                    style={{ width: `${size * 4}px` }}
                  ></div>
                </div>
              </div>
            )
          )}
        </div>
      </section>
    </div>
  );
};

export default DesignSystem;
