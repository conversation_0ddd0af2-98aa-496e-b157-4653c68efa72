"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";
import { jobService } from "@/api/jobs/job.service";
import { userService } from "@/api/users/user.service";
import { skillService } from "@/api/skills/skill.service";
import { jobCategoryService } from "@/api/job-categories/job-category.service";
import { Icon } from "@/components/ui/Icon";
import { ContentHeader } from "@/components/layout/ContentHeader";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import useToaster from "@/hooks/useToaster";
import { Card, CardContent } from "@/components/ui/Card";
import { formatDistanceToNow } from "date-fns";
import { JobStatus, Job } from "@/types/features/jobs/job.types";
import { User } from "@/types/features/user/user.types";
import { UserRole } from "@/types/enums";

interface ReportsData {
  totalUsers: number;
  totalAdmins: number;
  totalClients: number;
  totalFreelancers: number;
  newUsersThisMonth: number;

  totalJobs: number;
  openJobs: number;
  inProgressJobs: number;
  completedJobs: number;
  cancelledJobs: number;
  newJobsThisMonth: number;
  totalJobBudget: number;
  averageJobBudget: number;

  totalSkills: number;
  activeSkills: number;
  inactiveSkills: number;

  totalJobCategories: number;
  activeJobCategories: number;

  popularCategories: Array<{
    name: string;
    count: number;
  }>;

  recentJobs: Job[];
  recentUsers: User[];
}

export default function ReportsPage() {
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const router = useRouter();
  const { showError } = useToaster();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [reportsData, setReportsData] = useState<ReportsData | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState<
    "7d" | "30d" | "90d" | "1y"
  >("30d");

  const loadReportsData = useCallback(async () => {
    if (!isAuthenticated) return;

    try {
      setIsLoading(true);
      setError(null);

      const [usersResponse, jobsResponse, skillsResponse, categoriesResponse] =
        await Promise.all([
          userService.listUsers({ limit: 100 }),
          jobService.listJobs({ limit: 100 }),
          skillService.listSkills({ limit: 100 }),
          jobCategoryService.listJobCategories(undefined, 100),
        ]);

      const users = usersResponse.items || [];
      const jobs = jobsResponse.items || [];
      const skills = skillsResponse.items || [];
      const categories = categoriesResponse.items || [];

      const now = new Date();
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      const totalUsers = users.length;
      const totalAdmins = users.filter((u) => u.role === UserRole.ADMIN).length;
      const totalClients = users.filter(
        (u) => u.role === UserRole.CLIENT
      ).length;
      const totalFreelancers = users.filter(
        (u) => u.role === UserRole.FREELANCER
      ).length;
      const newUsersThisMonth = users.filter(
        (u) => new Date(u.createdAt) > thirtyDaysAgo
      ).length;

      const totalJobs = jobs.length;
      const openJobs = jobs.filter((j) => j.status === JobStatus.OPEN).length;
      const inProgressJobs = jobs.filter(
        (j) => j.status === JobStatus.IN_PROGRESS
      ).length;
      const completedJobs = jobs.filter(
        (j) => j.status === JobStatus.COMPLETED
      ).length;
      const cancelledJobs = jobs.filter(
        (j) => j.status === JobStatus.CANCELLED
      ).length;
      const newJobsThisMonth = jobs.filter(
        (j) => new Date(j.createdAt) > thirtyDaysAgo
      ).length;

      const totalJobBudget = jobs.reduce(
        (sum, job) => sum + (job.budget || 0),
        0
      );
      const averageJobBudget = totalJobs > 0 ? totalJobBudget / totalJobs : 0;

      const totalSkills = skills.length;
      const activeSkills = skills.filter((s) => s.isActive).length;
      const inactiveSkills = skills.filter((s) => !s.isActive).length;

      const totalJobCategories = categories.length;
      const activeJobCategories = categories.filter((c) => c.isActive).length;

      const categoryCount: Record<string, number> = {};
      jobs.forEach((job) => {
        if (job.category) {
          categoryCount[job.category] = (categoryCount[job.category] || 0) + 1;
        }
      });

      const popularCategories = Object.entries(categoryCount)
        .map(([name, count]) => ({ name, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);

      const recentJobs = jobs
        .sort(
          (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        )
        .slice(0, 5);

      const recentUsers = users
        .sort(
          (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        )
        .slice(0, 5);

      setReportsData({
        totalUsers,
        totalAdmins,
        totalClients,
        totalFreelancers,
        newUsersThisMonth,
        totalJobs,
        openJobs,
        inProgressJobs,
        completedJobs,
        cancelledJobs,
        newJobsThisMonth,
        totalJobBudget,
        averageJobBudget,
        totalSkills,
        activeSkills,
        inactiveSkills,
        totalJobCategories,
        activeJobCategories,
        popularCategories,
        recentJobs,
        recentUsers,
      });
    } catch (err) {
      console.error("Error loading reports data:", err);
      setError("Failed to load reports data. Please try again.");
      showError("Failed to load reports data. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, showError]);

  useEffect(() => {
    loadReportsData();
  }, [loadReportsData]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const getStatusBadgeVariant = (status: JobStatus) => {
    const statusColors: Record<JobStatus, string> = {
      [JobStatus.OPEN]: "bg-green-100 text-green-800",
      [JobStatus.IN_PROGRESS]: "bg-blue-100 text-blue-800",
      [JobStatus.COMPLETED]: "bg-purple-100 text-purple-800",
      [JobStatus.CANCELLED]: "bg-red-100 text-red-800",
    };
    return statusColors[status] || "bg-gray-100 text-gray-800";
  };

  const getRoleBadgeVariant = (role: UserRole) => {
    const roleColors: Record<UserRole, string> = {
      [UserRole.ADMIN]: "bg-blue-100 text-blue-800",
      [UserRole.CLIENT]: "bg-green-100 text-green-800",
      [UserRole.FREELANCER]: "bg-purple-100 text-purple-800",
    };
    return roleColors[role] || "bg-gray-100 text-gray-800";
  };

  if (
    !authLoading &&
    (!isAuthenticated || user?.attributes?.["custom:role"] !== "ADMIN")
  ) {
    router.push("/login");
    return null;
  }

  if (authLoading || !isAuthenticated || isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Icon name="Loader2" size="xl" className="animate-spin text-blue-500" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full max-w-6xl mx-auto p-4 sm:p-6">
        <div className="text-center py-8">
          <Icon
            name="AlertCircle"
            size="xl"
            className="mx-auto text-red-500 mb-4"
          />
          <h2 className="text-lg font-semibold text-gray-900 mb-2">
            Error Loading Reports
          </h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>Try Again</Button>
        </div>
      </div>
    );
  }

  if (!reportsData) {
    return (
      <div className="w-full max-w-6xl mx-auto p-4 sm:p-6">
        <div className="text-center py-8">
          <Icon
            name="FileText"
            size="xl"
            className="mx-auto text-gray-400 mb-4"
          />
          <h2 className="text-lg font-semibold text-gray-900 mb-2">
            No Reports Data
          </h2>
          <p className="text-gray-600 mb-4">
            Unable to generate reports at this time.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-7xl mx-auto p-4 sm:p-6 space-y-6">
      <ContentHeader
        title="Reports & Analytics"
        subtitle="Detailed analytics and exportable data for compliance, auditing, and in-depth analysis"
        breadcrumbs={[
          { label: "Dashboard", href: "/admin/dashboard" },
          { label: "Reports", current: true },
        ]}
        showBackButton={true}
        actions={
          <div className="flex items-center space-x-3">
            <select
              value={selectedTimeRange}
              onChange={(e) =>
                setSelectedTimeRange(
                  e.target.value as "7d" | "30d" | "90d" | "1y"
                )
              }
              className="rounded-md border border-input bg-background px-3 py-2 text-sm"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="1y">Last year</option>
            </select>
            <Button size="sm" onClick={() => window.print()}>
              <Icon name="Download" size="sm" className="mr-2" />
              Export PDF
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                const csvData =
                  `Report Type,Value\n` +
                  `Total Users,${reportsData?.totalUsers || 0}\n` +
                  `Total Jobs,${reportsData?.totalJobs || 0}\n` +
                  `Active Skills,${reportsData?.activeSkills || 0}\n`;
                const blob = new Blob([csvData], { type: "text/csv" });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement("a");
                a.href = url;
                a.download = `platform-report-${selectedTimeRange}.csv`;
                a.click();
              }}
            >
              <Icon name="FileSpreadsheet" size="sm" className="mr-2" />
              Export CSV
            </Button>
          </div>
        }
      />

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Icon name="Users" className="w-8 h-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Users</p>
                <p className="text-2xl font-bold text-gray-900">
                  {reportsData.totalUsers}
                </p>
                <p className="text-xs text-gray-500">
                  +{reportsData.newUsersThisMonth} this month
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Icon name="Briefcase" className="w-8 h-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Jobs</p>
                <p className="text-2xl font-bold text-gray-900">
                  {reportsData.totalJobs}
                </p>
                <p className="text-xs text-gray-500">
                  +{reportsData.newJobsThisMonth} this month
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Icon name="DollarSign" className="w-8 h-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">
                  Total Budget
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(reportsData.totalJobBudget)}
                </p>
                <p className="text-xs text-gray-500">
                  Avg: {formatCurrency(reportsData.averageJobBudget)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Icon name="Tag" className="w-8 h-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">
                  Active Skills
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {reportsData.activeSkills}
                </p>
                <p className="text-xs text-gray-500">
                  of {reportsData.totalSkills} total
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Platform Performance Summary */}
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Platform Performance Summary
          </h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Metric
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Current Period
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Previous Period
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Change
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Total Users
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {reportsData.totalUsers}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {Math.max(
                      0,
                      reportsData.totalUsers - reportsData.newUsersThisMonth
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                    +{reportsData.newUsersThisMonth} (
                    {reportsData.totalUsers > 0
                      ? Math.round(
                          (reportsData.newUsersThisMonth /
                            reportsData.totalUsers) *
                            100
                        )
                      : 0}
                    %)
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Total Jobs
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {reportsData.totalJobs}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {Math.max(
                      0,
                      reportsData.totalJobs - reportsData.newJobsThisMonth
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                    +{reportsData.newJobsThisMonth} (
                    {reportsData.totalJobs > 0
                      ? Math.round(
                          (reportsData.newJobsThisMonth /
                            reportsData.totalJobs) *
                            100
                        )
                      : 0}
                    %)
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Total Job Budget
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatCurrency(reportsData.totalJobBudget)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatCurrency(reportsData.totalJobBudget * 0.85)} *
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                    +15% growth
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Average Job Budget
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatCurrency(reportsData.averageJobBudget)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatCurrency(reportsData.averageJobBudget * 0.92)} *
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                    +8% increase
                  </td>
                </tr>
              </tbody>
            </table>
            <p className="text-xs text-gray-500 mt-2">
              * Previous period data is estimated based on current trends
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Jobs */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                Recent Jobs (Last 30 Days)
              </h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push("/admin/jobs")}
              >
                View All
                <Icon name="ArrowRight" className="w-4 h-4 ml-2" />
              </Button>
            </div>
            <div className="space-y-4">
              {reportsData.recentJobs.map((job) => (
                <div key={job.id} className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {job.title}
                    </p>
                    <p className="text-sm text-gray-500">
                      {formatCurrency(job.budget || 0)} •{" "}
                      {formatDistanceToNow(new Date(job.createdAt), {
                        addSuffix: true,
                      })}
                    </p>
                  </div>
                  <Badge className={getStatusBadgeVariant(job.status)}>
                    {job.status}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Users */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                New User Registrations
              </h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push("/admin/users")}
              >
                View All
                <Icon name="ArrowRight" className="w-4 h-4 ml-2" />
              </Button>
            </div>
            <div className="space-y-4">
              {reportsData.recentUsers.map((user) => (
                <div key={user.id} className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {user.name}
                    </p>
                    <p className="text-sm text-gray-500">
                      {user.email} •{" "}
                      {formatDistanceToNow(new Date(user.createdAt), {
                        addSuffix: true,
                      })}
                    </p>
                  </div>
                  <Badge className={getRoleBadgeVariant(user.role)}>
                    {user.role}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
