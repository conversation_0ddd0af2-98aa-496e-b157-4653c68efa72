import { ReactNode } from 'react';

export interface PaginationProps {
  /** Current page number (1-based) */
  currentPage: number;
  /** Total number of pages */
  totalPages: number;
  /** Callback when page changes */
  onPageChange: (page: number) => void;
  /** Number of pages to show before and after the current page */
  pageRangeDisplayed?: number;
  /** Number of pages to show at the start and end of the pagination */
  marginPagesDisplayed?: number;
  /** Label for the previous button */
  previousLabel?: ReactNode;
  /** Label for the next button */
  nextLabel?: ReactNode;
  /** Label for the first page button */
  firstLabel?: ReactNode;
  /** Label for the last page button */
  lastLabel?: ReactNode;
  /** Label for the jump to page input */
  jumpToLabel?: string;
  /** Whether to show the first and last page buttons */
  showFirstLastButtons?: boolean;
  /** Whether to show the previous and next buttons */
  showPrevNextButtons?: boolean;
  /** Whether to show the page numbers */
  showPageNumbers?: boolean;
  /** Whether to show the page info */
  showPageInfo?: boolean;
  /** Custom page info template function */
  pageInfoTemplate?: (current: number, total: number) => string;
  /** Additional CSS classes */
  className?: string;
  /** Additional CSS classes for the container */
  containerClassName?: string;
  /** Additional CSS classes for the page buttons */
  pageClassName?: string;
  /** Additional CSS classes for the active page button */
  activeClassName?: string;
  /** Additional CSS classes for the disabled buttons */
  disabledClassName?: string;
  /** Additional CSS classes for the previous button */
  previousClassName?: string;
  /** Additional CSS classes for the next button */
  nextClassName?: string;
  /** Additional CSS classes for the first page button */
  firstClassName?: string;
  /** Additional CSS classes for the last page button */
  lastClassName?: string;
  /** Additional CSS classes for the page info */
  pageInfoClassName?: string;
  /** Whether to disable the pagination */
  disabled?: boolean;
  /** Whether to hide the pagination when there's only one page */
  hideOnSinglePage?: boolean;
  /** Whether to show the jump to page input */
  showJumpToPage?: boolean;
  /** Callback when the jump to page input changes */
  onJumpToPage?: (page: number) => void;
  /** Additional props for the jump to page input */
  jumpToPageInputProps?: React.InputHTMLAttributes<HTMLInputElement>;
  /** Custom renderer for page buttons */
  renderPageButton?: (page: number, isActive: boolean, onClick: () => void, disabled: boolean) => ReactNode;
  /** Custom renderer for the previous button */
  renderPreviousButton?: (onClick: () => void, disabled: boolean) => ReactNode;
  /** Custom renderer for the next button */
  renderNextButton?: (onClick: () => void, disabled: boolean) => ReactNode;
  /** Custom renderer for the first page button */
  renderFirstButton?: (onClick: () => void, disabled: boolean) => ReactNode;
  /** Custom renderer for the last page button */
  renderLastButton?: (onClick: () => void, disabled: boolean) => ReactNode;
  /** Custom renderer for the page info */
  renderPageInfo?: (current: number, total: number) => ReactNode;
  /** Custom renderer for the jump to page input */
  renderJumpToPage?: (onJump: (page: number) => void, currentPage: number, totalPages: number) => ReactNode;
}
