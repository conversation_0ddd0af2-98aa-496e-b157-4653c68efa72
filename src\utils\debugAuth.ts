/**
 * Debug utility to test authentication and role extraction
 */

import { fetchAuthSession, fetchUserAttributes } from 'aws-amplify/auth';

export async function debugAuthState() {
  try {

    const session = await fetchAuthSession();
    const attributes = await fetchUserAttributes();

    return {
      session,
      attributes,
      groups: session.tokens?.accessToken?.payload['cognito:groups'],
      customRole: attributes['custom:role']
    };

  } catch (error) {
    console.error('Debug auth error:', error);
    return null;
  }
}

if (typeof window !== 'undefined') {
  (window as any).debugAuth = debugAuthState;
}
