import {
  PaymentError,
  PaymentRetryConfig,
  PaymentValidationResult
} from '@/types/features/payments/payment.types';

/**
 * Default retry configuration for payment operations
 */
export const DEFAULT_RETRY_CONFIG: PaymentRetryConfig = {
  maxRetries: 3,
  retryDelays: [1000, 2000, 5000],
  retryableErrors: [
    'network_error',
    'api_error',
    'rate_limit_error',
    'temporary_failure',
    'card_declined',
  ],
};

/**
 * Utility class for handling payment errors and retries
 */
export class PaymentErrorHandler {
  private static instance: PaymentErrorHandler;

  private constructor() { }

  public static getInstance(): PaymentErrorHandler {
    if (!PaymentErrorHandler.instance) {
      PaymentErrorHandler.instance = new PaymentErrorHandler();
    }
    return PaymentErrorHandler.instance;
  }

  /**
   * Determines if an error is retryable
   */
  isRetryableError(error: PaymentError, config: PaymentRetryConfig = DEFAULT_RETRY_CONFIG): boolean {
    const errorCode = error?.code?.toLowerCase() || 'unknown_error';
    return config.retryableErrors.includes(errorCode);
  }

  /**
   * Get user-friendly error message based on error code
   */
  getUserFriendlyMessage(error: PaymentError): string {
    const errorMappings: Record<string, string> = {
      'card_declined': 'Your card was declined. Please try a different payment method.',
      'expired_card': 'Your card has expired. Please use a different card.',
      'incorrect_cvc': 'The security code (CVC) is incorrect. Please check and try again.',
      'insufficient_funds': 'Your card has insufficient funds. Please try a different payment method.',
      'invalid_expiry_month': 'The expiration month is invalid. Please check your card details.',
      'invalid_expiry_year': 'The expiration year is invalid. Please check your card details.',
      'invalid_number': 'The card number is invalid. Please check and try again.',

      'processing_error': 'There was an error processing your payment. Please try again.',
      'payment_intent_authentication_failure': 'Payment authentication failed. Please try again.',
      'payment_method_unactivated': 'This payment method is not activated. Please use a different method.',

      'network_error': 'Network connection error. Please check your internet connection and try again.',
      'api_error': 'Payment service temporarily unavailable. Please try again in a few moments.',
      'rate_limit_error': 'Too many requests. Please wait a moment and try again.',

      'invalid_amount': 'The payment amount is invalid. Please check the amount and try again.',
      'amount_too_small': 'The payment amount is too small. Minimum amount is $0.50.',
      'amount_too_large': 'The payment amount exceeds the maximum allowed. Please contact support.',

      'payment_failed': 'Payment failed. Please check your payment details and try again.',
      'payment_canceled': 'Payment was cancelled. You can try again when ready.',
      'payment_timeout': 'Payment request timed out. Please try again.',
    };

    const errorCode = error?.code?.toLowerCase() || 'unknown_error';
    return errorMappings[errorCode] || error?.message || 'An unexpected error occurred.';
  }

  /**
   * Validates payment amount
   */
  validatePaymentAmount(amount: number, currency: string = 'USD'): PaymentValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (amount <= 0) {
      errors.push('Amount must be greater than 0');
    }

    const minimums: Record<string, number> = {
      USD: 0.50,
      EUR: 0.50,
      GBP: 0.30,
      JPY: 50,
    };

    const minAmount = minimums[currency.toUpperCase()] || 0.50;
    if (amount < minAmount) {
      errors.push(`Minimum payment amount for ${currency} is ${minAmount}`);
    }

    const maxAmount = 999999.99;
    if (amount > maxAmount) {
      errors.push(`Maximum payment amount is ${maxAmount}`);
    }

    if (amount > 10000) {
      warnings.push('Large payment amount detected. Please verify the amount is correct.');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Validates contract for payment processing
   */
  validateContractForPayment(contract: any): PaymentValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!contract) {
      errors.push('Contract is required');
      return { isValid: false, errors };
    }

    if (!contract.id) {
      errors.push('Contract ID is required');
    }

    if (!contract.clientId) {
      errors.push('Client ID is required');
    }

    if (!contract.freelancerId) {
      errors.push('Freelancer ID is required');
    }

    const amount = 'budget' in contract ? contract.budget : 0;
    const amountValidation = this.validatePaymentAmount(amount);
    errors.push(...amountValidation.errors);
    warnings.push(...(amountValidation.warnings || []));

    const validStatuses = ['COMPLETED', 'WORK_SUBMITTED'];
    if (!validStatuses.includes(contract.status)) {
      warnings.push(`Contract status '${contract.status}' may not be ready for payment`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Creates a retry delay based on attempt number
   */
  getRetryDelay(attempt: number, config: PaymentRetryConfig = DEFAULT_RETRY_CONFIG): number {
    if (attempt >= config.retryDelays.length) {
      return config.retryDelays[config.retryDelays.length - 1];
    }
    return config.retryDelays[attempt];
  }

  /**
   * Logs payment error for monitoring
   */
  logPaymentError(error: PaymentError, context: Record<string, any> = {}): void {
    const logData = {
      timestamp: new Date().toISOString(),
      error: {
        code: error.code,
        message: error.message,
        details: error.details,
      },
      context,
    };

    console.error('Payment Error:', logData);

  }

  /**
   * Sanitizes sensitive data from payment metadata
   */
  sanitizePaymentMetadata(metadata: Record<string, any>): Record<string, any> {
    const sanitized = { ...metadata };

    const sensitiveFields = [
      'cardNumber',
      'cvc',
      'ssn',
      'bankAccount',
      'routingNumber',
      'password',
      'token',
      'secret',
    ];

    sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        delete sanitized[field];
      }
    });

    if (sanitized.email && typeof sanitized.email === 'string') {
      const emailParts = sanitized.email.split('@');
      if (emailParts.length === 2) {
        const username = emailParts[0];
        const maskedUsername = username.length > 2
          ? username.substring(0, 2) + '*'.repeat(username.length - 2)
          : '*'.repeat(username.length);
        sanitized.email = maskedUsername + '@' + emailParts[1];
      }
    }

    return sanitized;
  }
}

export const paymentErrorHandler = PaymentErrorHandler.getInstance();

/**
 * Retry function with exponential backoff
 */
export async function retryPaymentOperation<T>(
  operation: () => Promise<T>,
  config: PaymentRetryConfig = DEFAULT_RETRY_CONFIG,
  context: string = 'payment operation'
): Promise<T> {
  let lastError: Error | undefined;

  for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;

      if (attempt === config.maxRetries) {
        break;
      }

      if (error instanceof PaymentError && !paymentErrorHandler.isRetryableError(error, config)) {
        break;
      }

      const delay = paymentErrorHandler.getRetryDelay(attempt, config);
      console.warn(`${context} failed (attempt ${attempt + 1}/${config.maxRetries + 1}), retrying in ${delay}ms:`, error);

      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  if (lastError && lastError instanceof PaymentError) {
    paymentErrorHandler.logPaymentError(lastError, { context, attempts: config.maxRetries + 1 });
  }

  if (!lastError) {
    throw new Error('Operation failed without error details');
  }

  throw lastError;
}