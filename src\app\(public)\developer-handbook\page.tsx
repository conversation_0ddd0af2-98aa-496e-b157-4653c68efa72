'use client';

import React from 'react';
import { HandbookTabs } from '@/components/developer-handbook/examples/HandbookTabs';
import { GettingStarted } from '@/components/developer-handbook/sections/GettingStarted';
import { DesignSystem } from '@/components/developer-handbook/sections/DesignSystem';
import { UIComponents } from '@/components/developer-handbook/sections/UIComponents';
import { LayoutComponents } from '@/components/developer-handbook/sections/LayoutComponents';

const DeveloperHandbook = () => {
  const tabs = [
    {
      value: 'getting-started',
      label: 'Getting Started',
      content: <GettingStarted />,
    },
    {
      value: 'design-system',
      label: 'Design System',
      content: <DesignSystem />,
    },
    {
      value: 'ui-components',
      label: 'UI Components',
      content: <UIComponents />,
    },
    {
      value: 'layout-components',
      label: 'Layout Components',
      content: <LayoutComponents />,
    },
  ];

  return (
    <div className="py-8">
      <div className="container mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-4xl font-bold tracking-tight mb-2">Developer Handbook</h1>
          <p className="text-lg text-muted-foreground">
            A comprehensive guide to the MyVillage component library and design system.
          </p>
        </div>

        <HandbookTabs 
          defaultValue="getting-started"
          tabs={tabs}
        />
      </div>
    </div>
  );
};

export default DeveloperHandbook;
