import { gql } from '@apollo/client';

export const GET_CLIENT_DASHBOARD_DATA = gql`
  query GetClientDashboardData($id: ID!) {
    getUser(id: $id) {
      id
      name
      role
      jobs {
        items {
          id
          title
          description
          status
          budget
          deadline
          isRemote
          skills
          createdAt
          updatedAt
          proposals {
            items {
              id
              status
              bidAmount
              coverLetter
              freelancer {
                id
                name
                email
                profilePhoto
                skills
              }
            }
          }
        }
      }
      clientContracts {
        items {
          id
          title
          status
          startDate
          endDate
          budget
          freelancer {
            id
            name
            email
            profilePhoto
          }
          client {
            id
            name
            email
            profilePhoto
          }
          job {
            id
            title
            description
          }
          deliverables {
            items {
              id
              title
              description
              status
              dueDate
            }
          }
          payments {
            items {
              id
              amount
              status
            }
          }
        }
      }
    }
  }
`;

export const GET_FREELANCER_DASHBOARD_DATA = gql`
  query GetFreelancerDashboardData($id: ID!) {
  getUser(id: $id) {
    id
    name
    role
    proposals {
      items {
        id
        status
        bidAmount
        coverLetter
        freelancer {
          id
          name
          email
          profilePhoto
          skills
        }
        job {
          id
          title
          description
          client {
            id
            name
            email
            profilePhoto
          }
        }
        contract {
          id
          title
          status
          deliverables {
            items {
              id
              title
              description
              status
              dueDate
            }
          }
          payments {
            items {
              id
              amount
              status
            }
          }
        }
      }
    }
    freelancerContracts {
      items {
        id
        title
        status
        startDate
        endDate
        budget
        client {
          id
          name
          email
          profilePhoto
        }
      }
    }
  }
}
`;
