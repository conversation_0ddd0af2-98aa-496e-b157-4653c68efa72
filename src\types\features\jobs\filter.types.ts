import { JobStatus, JobType } from './job.types';

/**
 * Filter conditions for querying jobs
 * This is used for GraphQL filter input
 */
export interface JobFilterCondition {
  eq?: string | number | boolean | JobStatus | JobType;
  ne?: string | number | boolean | JobStatus | JobType;
  le?: number | string;
  lt?: number | string;
  ge?: number | string;
  gt?: number | string;
  contains?: string;
  notContains?: string;
  between?: [number, number] | [string, string];
  in?: (string | number)[];
  notIn?: (string | number)[];
  attributeExists?: boolean;
  attributeType?: 'string' | 'number' | 'boolean' | 'list' | 'map' | 'null';
  size?: { size: number };
  and?: JobFilterCondition[];
  or?: JobFilterCondition[];
  not?: JobFilterCondition;
}

/**
 * Transformed job filters for GraphQL queries
 */
export interface TransformedJobFilter {
  id?: JobFilterCondition;
  clientId?: JobFilterCondition;
  category?: JobFilterCondition;
  status?: JobFilterCondition;
  type?: JobFilterCondition;
  isRemote?: JobFilterCondition;
  title?: JobFilterCondition;
  description?: JobFilterCondition;
  budget?: {
    between?: [number, number];
    ge?: number;
    le?: number;
  };
  skills?: {
    contains?: string;
    in?: string[];
  };
  createdAt?: JobFilterCondition;
  and?: JobFilterCondition[];
  or?: JobFilterCondition[];
}

/**
 * Input filters for job listings
 */
export interface JobFilterInput {
  query?: string;
  searchTerm?: string;
  clientId?: string;
  category?: string;
  categories?: string[];
  minBudget?: number;
  maxBudget?: number;
  jobType?: JobType;
  status?: JobStatus;
  isRemote?: boolean;
  skills?: string[];
  limit?: number;
  nextToken?: string;
}

export type JobFilter = JobFilterInput;
