import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';



export async function POST(request: NextRequest) {
  try {
    if (!process.env.STRIPE_SECRET_KEY) {
      console.error('STRIPE_SECRET_KEY environment variable is not configured');
    }

    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || 'sk_test_placeholder');
    const body = await request.json();
    const { amount, currency = 'usd', contractId, clientId, metadata = {} } = body;

    console.log('Create payment intent request:', {
      amount,
      currency,
      contractId,
      clientId,
      hasMetadata: Object.keys(metadata).length > 0
    });

    if (!amount || amount <= 0) {
      console.error('Invalid amount:', amount);
      return NextResponse.json(
        { error: 'Invalid amount. Amount must be greater than 0.' },
        { status: 400 }
      );
    }

    if (!contractId) {
      console.error('Missing contractId');
      return NextResponse.json(
        { error: 'Contract ID is required.' },
        { status: 400 }
      );
    }

    if (!clientId) {
      console.error('Missing clientId');
      return NextResponse.json(
        { error: 'Client ID is required.' },
        { status: 400 }
      );
    }

    const amountInCents = Math.round(amount * 100);
    console.log('Converting amount to cents:', amount, '→', amountInCents);

    console.log('Creating Stripe payment intent...');
    const paymentIntent = await stripe.paymentIntents.create({
      amount: amountInCents,
      currency: currency.toLowerCase(),
      metadata: {
        contractId,
        clientId,
        ...metadata,
      },
      automatic_payment_methods: {
        enabled: true,
      },
      setup_future_usage: 'off_session',
    });

    console.log('Payment intent created successfully:', {
      id: paymentIntent.id,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      status: paymentIntent.status
    });

    return NextResponse.json({
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      status: paymentIntent.status,
    });

  } catch (error) {
    console.error('Error creating payment intent:', error);

    if (error instanceof Stripe.errors.StripeError) {
      return NextResponse.json(
        {
          error: 'Payment processing error',
          details: error.message,
          type: error.type
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error while creating payment intent' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    if (!process.env.STRIPE_SECRET_KEY) {
      console.error('STRIPE_SECRET_KEY environment variable is not configured');
    }

    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || 'sk_test_placeholder');
    const { searchParams } = new URL(request.url);
    const paymentIntentId = searchParams.get('payment_intent_id');

    if (!paymentIntentId) {
      return NextResponse.json(
        { error: 'Payment Intent ID is required' },
        { status: 400 }
      );
    }

    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    return NextResponse.json({
      id: paymentIntent.id,
      status: paymentIntent.status,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      metadata: paymentIntent.metadata,
      created: paymentIntent.created,
      last_payment_error: paymentIntent.last_payment_error,
    });

  } catch (error) {
    console.error('Error retrieving payment intent:', error);

    if (error instanceof Stripe.errors.StripeError) {
      return NextResponse.json(
        {
          error: 'Payment retrieval error',
          details: error.message
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error while retrieving payment intent' },
      { status: 500 }
    );
  }
}