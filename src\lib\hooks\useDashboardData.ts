import { useEffect, useState, useCallback } from 'react';
import { useAuth } from '@/lib/auth/AuthContext';
import dashboardService from '@/api/dashboard/dashboard.service';
import type { GraphQLResponse } from '@/api/dashboard/dashboard.types';

interface DashboardData {
  activeJobs: number;
  activeContracts: number;
  activeContractsData?: Array<{
    id: string;
    title: string;
    status: string;
    startDate: string;
    endDate?: string;
    budget: number;
    client: {
      id: string;
      name: string;
      email: string;
      profilePhoto?: string;
    };
  }>;
  totalEarnings: number;
  totalJobsCompleted: number;
  recentJobs: Array<{
    id: string;
    title: string;
    status: string;
    budget: number;
    proposalsCount: number;
  }>;
  recentContracts: Contract[];
  recentProposals: Array<{
    id: string;
    status: string;
    job: {
      id: string;
      title: string;
      budget: number;
      client: {
        id: string;
        name: string;
      };
    };
  }>;
  notifications: Array<{
    id: string;
    title: string;
    message: string;
    read: boolean;
    createdAt: string;
  }>;
}

interface UseDashboardDataProps {
  role: 'client' | 'freelancer';
}

function transformClientData(data: GraphQLResponse): DashboardData {

  const userData = data.getUser || data;

  const contracts = (
    (userData.clientContracts?.items || []).length > 0
      ? userData.clientContracts?.items
      : userData.contractsByClientId?.items
  ) || [];


  const activeContracts = contracts.filter((contract: Contract) =>
    ['ACTIVE', 'WORK_SUBMITTED', 'REVISIONS_REQUESTED', 'PENDING_FREELANCER_ACCEPTANCE'].includes(contract.status)
  );
  const completedOrPaid = contracts.filter((contract: Contract) =>
    ['COMPLETED', 'PAID'].includes(contract.status)
  );

  const recentContracts = [...contracts]
    .sort((a: Contract, b: Contract) =>
      new Date(b.startDate).getTime() - new Date(a.startDate).getTime()
    )
    .slice(0, 5)
    .map((contract: Contract) => ({
      ...contract,
      deliverables: contract.deliverables || { items: [] },
      payments: contract.payments || { items: [] },
      job: contract.job || {
        id: '',
        title: contract.title.replace('Contract for ', ''),
        description: ''
      }
    }));

  const totalEarnings = completedOrPaid.reduce(
    (sum: number, contract: Contract) => sum + (contract.budget || 0),
    0
  );

  const result: DashboardData = {
    activeJobs: 0,
    activeContracts: activeContracts.length,
    totalEarnings,
    totalJobsCompleted: completedOrPaid.length,
    recentJobs: [],
    recentContracts,
    recentProposals: [],
    notifications: []
  };

  return result;
}

interface Contract {
  id: string;
  title: string;
  status: string;
  startDate: string;
  endDate?: string;
  budget: number;
  client: {
    id: string;
    name: string;
    email: string;
    profilePhoto?: string;
  };
  job?: {
    id: string;
    title: string;
  };
  deliverables?: {
    items: any[];
  };
  payments?: {
    items: any[];
  };
  createdAt?: string;
  description?: string;
}

interface FreelancerDashboardData {
  activeJobs: number;
  activeContracts: number;
  totalEarnings: number;
  totalJobsCompleted: number;
  recentJobs: any[];
  recentContracts: Array<{
    id: string;
    title: string;
    status: string;
    budget: number;
    startDate: string;
    endDate?: string;
    client: {
      id: string;
      name: string;
      email: string;
      profilePhoto?: string;
    };
    job?: {
      id: string;
      title: string;
    };
  }>;
  recentProposals: Array<{
    id: string;
    status: string;
    job: {
      id: string;
      title: string;
      budget: number;
      client: {
        id: string;
        name: string;
      };
    };
  }>;
  notifications: any[];
}

function transformFreelancerData(data: any): FreelancerDashboardData {

  if (!data) {
    return {
      activeJobs: 0,
      activeContracts: 0,
      totalEarnings: 0,
      totalJobsCompleted: 0,
      recentJobs: [],
      recentContracts: [],
      recentProposals: [],
      notifications: []
    };
  }

  const userData = data.getUser || data;
  if (!userData) {
    console.warn('[transformFreelancerData] No user data found');
    return {
      activeJobs: 0,
      activeContracts: 0,
      totalEarnings: 0,
      totalJobsCompleted: 0,
      recentJobs: [],
      recentContracts: [],
      recentProposals: [],
      notifications: []
    };
  }

  const proposals = userData.proposals?.items || [];

  const contracts = userData.freelancerContracts?.items || [];

  const activeProposals = proposals.filter((p: any) => p.status === 'PENDING').length;

  const recentContracts = contracts
    .sort((a: any, b: any) => new Date(b.startDate).getTime() - new Date(a.startDate).getTime())
    .slice(0, 5)
    .map((contract: any) => ({
      id: contract.id,
      title: contract.title,
      status: contract.status,
      budget: contract.budget,
      startDate: contract.startDate,
      endDate: contract.endDate,
      client: contract.client,
      job: contract.job || { id: '', title: contract.title }
    }));

  const recentProposals = proposals
    .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 5)
    .map((proposal: any) => ({
      id: proposal.id,
      status: proposal.status,
      job: {
        id: proposal.job?.id || '',
        title: proposal.job?.title || 'Untitled Job',
        budget: proposal.job?.budget || 0,
        client: {
          id: proposal.job?.client?.id || '',
          name: proposal.job?.client?.name || 'Unknown Client'
        }
      }
    }));

  const completedOrPaid = contracts.filter((c: any) =>
    ['COMPLETED', 'PAID'].includes(c.status)
  );

  const totalEarnings = completedOrPaid.reduce(
    (sum: number, contract: any) => sum + (contract.budget || 0),
    0
  );

  const activeContractsData = contracts.filter((c: any) =>
    ['ACTIVE', 'WORK_SUBMITTED', 'REVISIONS_REQUESTED', 'PENDING_FREELANCER_ACCEPTANCE', 'PAID', 'DRAFT'].includes(c.status)
  );

  const result = {
    activeJobs: activeProposals,
    activeContracts: activeContractsData.length,
    activeContractsData,
    totalEarnings,
    totalJobsCompleted: completedOrPaid.length,
    recentJobs: [],
    recentContracts,
    recentProposals,
    notifications: []
  };

  return result;
}

export const useDashboardData = ({ role }: UseDashboardDataProps) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [data, setData] = useState<DashboardData>({
    activeJobs: 0,
    activeContracts: 0,
    totalEarnings: 0,
    totalJobsCompleted: 0,
    recentJobs: [],
    recentContracts: [],
    recentProposals: [],
    notifications: []
  });

  const transformData = useCallback((responseData: GraphQLResponse | null): DashboardData => {
    if (!responseData) {
      return {
        activeJobs: 0,
        activeContracts: 0,
        totalEarnings: 0,
        totalJobsCompleted: 0,
        recentJobs: [],
        recentContracts: [],
        recentProposals: [],
        notifications: []
      };
    }

    try {
      return role === 'client'
        ? transformClientData(responseData)
        : transformFreelancerData(responseData);
    } catch (err) {
      console.error('Error in transformData:', err);
      throw err;
    }
  }, [role]);

  useEffect(() => {
    const userId = user?.attributes?.sub || user?.username;

    if (!userId) {
      setError(new Error('No user ID available in auth context'));
      setLoading(false);
      return;
    }

    let isMounted = true;

    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        const result = role === 'client'
          ? await dashboardService.getClientDashboardData(userId)
          : await dashboardService.getFreelancerDashboardData(userId);

        if (!isMounted) return;

        const transformedData = transformData(result);
        setData(transformedData);
      } catch (err) {
        if (!isMounted) return;

        const error = err instanceof Error ? err : new Error('Failed to fetch dashboard data');
        console.error(`[${new Date().toISOString()}] Error in fetchData:`, {
          error,
          userId,
          role,
          errorMessage: error.message,
          stack: error.stack
        });

        setError(error);
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchData();

    return () => {
      isMounted = false;
    };
  }, [user?.attributes?.sub, user?.username, role, transformData]);

  const refetch = useCallback(async () => {
    const userId = user?.attributes?.sub || user?.username;
    if (!userId) return;

    try {
      setLoading(true);
      setError(null);

      const result = role === 'client'
        ? await dashboardService.getClientDashboardData(userId)
        : await dashboardService.getFreelancerDashboardData(userId);

      const transformedData = transformData(result);

      setData(transformedData);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to refetch dashboard data');
      console.error('Error in refetch:', error);
      setError(error);
    } finally {
      setLoading(false);
    }
  }, [user?.attributes?.sub, user?.username, role, transformData]);

  return {
    data,
    loading,
    error,
    refetch
  };
};
