export type Skill = {
  id: string;
  name: string;
  jobCategoryId: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
};

export type CreateSkillInput = {
  name: string;
  jobCategoryId: string;
  description?: string;
  isActive?: boolean;
};

export type UpdateSkillInput = {
  id: string;
  name?: string;
  jobCategoryId?: string;
  description?: string;
  isActive?: boolean;
};

export type SkillFilter = {
  jobCategoryId?: string;
  isActive?: boolean;
  searchTerm?: string;
  limit?: number;
  nextToken?: string;
};

