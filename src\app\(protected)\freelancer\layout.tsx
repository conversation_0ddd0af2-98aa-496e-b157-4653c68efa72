'use client';

import { usePathname } from 'next/navigation';
import { useAuth } from '@/lib/auth/AuthContext';
import { useEffect, useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Loading } from '@/components/ui';
import { useProposalCount } from '@/hooks/useProposalCount';

export default function FreelancerLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const {  loading: authLoading, isInitialized } = useAuth();
  const pathname = usePathname();
  const [mounted, setMounted] = useState(false);
  const { counts: { total: proposalCount }, loading: countsLoading } = useProposalCount();

  useEffect(() => {
    setMounted(true);
  }, []);

  if (authLoading || !mounted || !isInitialized || countsLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Loading size="lg" />
      </div>
    );
  }

  const breadcrumbs = [
    { label: 'Home', href: '/freelancer/dashboard' },
  ];

  if (pathname !== '/freelancer/dashboard') {
    const pathParts = pathname.split('/').filter(Boolean);
    if (pathParts.length > 1) {
      const currentPage = pathParts[pathParts.length - 1];
      breadcrumbs.push({
        label: currentPage.charAt(0).toUpperCase() + currentPage.slice(1).replace(/-/g, ' '),
        href: pathname,
      });
    }
  }

  return (
    <DashboardLayout 
      breadcrumbs={breadcrumbs}
      proposalCount={proposalCount || 0}
    >
      {children}
    </DashboardLayout>
  );
}
