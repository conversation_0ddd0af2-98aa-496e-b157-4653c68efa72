'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/lib/auth/AuthContext';
import ContractForm from '@/components/features/contracts/ContractForm';
import contractService from '@/api/contracts/contract.service';
import { jobService } from '@/api/jobs/job.service';
import { proposalService } from '@/api/proposals/proposal.service';
import type { Job } from '@/types/features/jobs/job.types';
import type { JobProposal } from '@/types/features/proposals/proposal.types';
import { ContractType } from '@/types/features/contracts/contract.types';
import { ContentHeader } from '@/components/layout/ContentHeader';
import { Icon } from '@/components/ui/Icon';
import notFound from '@/app/not-found';

interface JobData extends Omit<Job, 'type'> {
  type?: string;
}

export default function NewContractPage() {
  const searchParams = useSearchParams();
  const jobId = searchParams?.get('jobId');
  const proposalId = searchParams?.get('proposalId');

  const { isAuthenticated, user, cognitoUserId, loading: authLoading } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [job, setJob] = useState<JobData | null>(null);
  const [proposal, setProposal] = useState<JobProposal | null>(null);
  const [existingContractId, setExistingContractId] = useState<string | null>(null);

  useEffect(() => {
    if (!isAuthenticated || !jobId || !proposalId) {
      setIsLoading(false);
      return;
    }

    const fetchData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const jobData = await jobService.getJob(jobId) as JobData;
        if (!user || jobData.clientId !== user.attributes.sub) {
          setError('Unauthorized');
          return;
        }
        setJob(jobData);

        const proposalData = await proposalService.getProposalById(proposalId);
        if (!proposalData || proposalData.jobId !== jobId) {
          setError('Proposal not found');
          return;
        }
        setProposal(proposalData);

        const { items: contracts } = await contractService.listContracts({ jobId });
        const existingContract = contracts.find(c => c.proposalId === proposalId);
        if (existingContract) {
          setExistingContractId(existingContract.id);
          return;
        }
      } catch (err) {
        console.error('Error loading contract data:', err);
        setError('Failed to load contract data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [isAuthenticated, user, jobId, proposalId]);

  useEffect(() => {
    if (existingContractId) {
      router.push(`/contracts/${existingContractId}`);
    }
  }, [existingContractId, router]);

  const handleSubmit = async (data: any) => {
    try {
      setIsSubmitting(true);
      setError(null);

      if (!cognitoUserId) {
        const errorMsg = 'User not authenticated. Please log in again.';
        console.error(errorMsg);
        setError(errorMsg);
        return;
      }

      const payload = {
        jobId: data.jobId,
        proposalId: data.proposalId,
        title: data.title,
        description: data.description,
        type: data.type,
        status: data.status,
        terms: data.terms,
        startDate: data.startDate,
        endDate: data.endDate,
        budget: data.budget,
        clientId: cognitoUserId,
        freelancerId: data.freelancerId,
      };

      try {
        const result = await contractService.createContract(payload);
        router.push(`/contracts/${result.id}`);
      } catch (serviceError) {
        if (serviceError instanceof Error) {
          console.error('Service error details:', {
            name: serviceError.name,
            message: serviceError.message,
            stack: serviceError.stack
          });
        }
        throw serviceError;
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to create contract. Please try again.';
      setError(errorMsg);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!authLoading && (!isAuthenticated || user?.attributes?.['custom:role'] !== 'CLIENT')) {
    router.push('/login');
    return null;
  }

  if (authLoading || isLoading || !isAuthenticated) {
    return (
      <div className="flex items-center justify-center h-full">
        <Icon name="Loader2" size="xl" className="animate-spin text-blue-500" />
      </div>
    );
  }

  if (!jobId || !proposalId) {
    return notFound();
  }

  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <Icon name="XCircle" size="md" className="text-red-400" />
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-red-800">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!job || !proposal) {
    return notFound();
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-4 sm:p-6">
      <ContentHeader
        title="Create New Contract"
        subtitle="Create a contract based on a job proposal."
        breadcrumbs={[
          { label: 'Dashboard', href: '/client/dashboard' },
          { label: 'Contracts', href: '/contracts' },
          { label: 'New Contract', current: true },
        ]}
        showBackButton
      />

      <div className="bg-card rounded-lg shadow-sm p-6">
        <ContractForm
          initialData={{
            jobId,
            proposalId,
            title: `Contract for ${job.title}`,
            description: job.description || '',
            terms: `This contract is based on the proposal submitted for job: ${job.title}`,
            budget: proposal.bidAmount || job.budget || 0,
            freelancerId: proposal.freelancerId,
            clientId: cognitoUserId || '',
            type: ContractType.FIXED_PRICE,
            startDate: new Date().toISOString(),
            endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          }}
          onSubmit={handleSubmit}
          isLoading={isSubmitting}
          submitButtonText="Create Contract"
        />
      </div>
    </div>
  );
}
