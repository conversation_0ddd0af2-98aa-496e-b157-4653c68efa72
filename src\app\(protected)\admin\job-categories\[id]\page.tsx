'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";
import { jobCategoryService } from '@/api/job-categories/job-category.service';
import { JobCategory } from "@/types/features/job-categories/job-category.types";
import { Icon } from '@/components/ui/Icon';
import { ContentHeader } from '@/components/layout/ContentHeader';
import { CategorySummaryCard, CategoryDetailsInfoCard } from '@/components/layout';
import { Button } from '@/components/ui/Button';
import { ConfirmDialog } from '@/components/ui/ConfirmDialog';
import useToaster from '@/hooks/useToaster';

export default function ViewJobCategoryPage() {
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const router = useRouter();
  const params = useParams();
  const { showSuccess, showError } = useToaster();
  const [isLoading, setIsLoading] = useState(true);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [category, setCategory] = useState<JobCategory | null>(null);

  useEffect(() => {
    const loadCategory = async () => {
      if (!isAuthenticated || !params.id) return;

      try {
        setIsLoading(true);
        const categoryData = await jobCategoryService.getJobCategory(params.id as string);
        setCategory(categoryData);
      } catch (err: unknown) {
        console.error('Error loading job category:', err);
        const errorMessage = 'Failed to load job category data. Please try again.';
        showError(errorMessage);
        router.push('/admin/job-categories');
      } finally {
        setIsLoading(false);
      }
    };

    loadCategory();
  }, [isAuthenticated, params.id, router, showError]);

  const handleEdit = () => {
    router.push(`/admin/job-categories/${params.id}/edit`);
  };

  const handleDelete = () => {
    setShowDeleteDialog(true);
  };

  const handleConfirmDelete = async () => {
    if (!category) return;

    try {
      setDeleteLoading(true);
      await jobCategoryService.deleteJobCategory(category.id);
      
      showSuccess('Job category deleted successfully');
      
      router.push('/admin/job-categories');
    } catch (err: unknown) {
      console.error('Error deleting job category:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete job category. Please try again.';
      showError(errorMessage);
    } finally {
      setDeleteLoading(false);
      setShowDeleteDialog(false);
    }
  };

  const handleCancelDelete = () => {
    setShowDeleteDialog(false);
  };

  if (!authLoading && (!isAuthenticated || user?.attributes?.['custom:role'] !== 'ADMIN')) {
    router.push('/login');
    return null;
  }

  if (authLoading || !isAuthenticated || isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Icon name="Loader2" size="xl" className="animate-spin text-blue-500" />
      </div>
    );
  }

  if (!category) {
    return (
      <div className="w-full max-w-6xl mx-auto p-4 sm:p-6">
        <div className="text-center py-8">
          <Icon name="AlertCircle" size="xl" className="mx-auto text-red-500 mb-4" />
          <h2 className="text-lg font-semibold text-gray-900 mb-2">Category Not Found</h2>
          <p className="text-gray-600 mb-4">The job category you&apos;re looking for doesn&apos;t exist.</p>
          <Button onClick={() => router.push('/admin/job-categories')}>
            Back to Job Categories
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-4 sm:p-6">
      <ContentHeader
        title={category.name}
        subtitle="View job category details and manage settings"
        breadcrumbs={[
          { label: 'Dashboard', href: '/admin/dashboard' },
          { label: 'Job Categories', href: '/admin/job-categories' },
          { label: category.name, current: true }
        ]}
        showBackButton={true}
      />
      
      <div className="space-y-6">
        <CategorySummaryCard 
          category={category} 
          onEdit={handleEdit} 
          onDelete={handleDelete} 
          deleteLoading={deleteLoading} 
          loading={isLoading} 
        />
        <CategoryDetailsInfoCard 
          category={category} 
          loading={isLoading} 
        />
      </div>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={showDeleteDialog}
        title="Delete Job Category"
        message={`Are you sure you want to delete "${category.name}"? This action cannot be undone and will permanently delete the category and all associated data.`}
        confirmText={deleteLoading ? 'Deleting...' : 'Delete'}
        cancelText="Cancel"
        confirmVariant="destructive"
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        isLoading={deleteLoading}
      />
    </div>
  );
}