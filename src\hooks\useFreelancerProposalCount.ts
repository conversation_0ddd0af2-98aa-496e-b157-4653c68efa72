import { useState, useEffect, useCallback } from 'react';
import { proposalService } from '@/api/proposals/proposal.service';
import { useAuth } from '@/lib/auth/AuthContext';

export const useFreelancerProposalCount = () => {
  const [proposalCount, setProposalCount] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  const fetchProposalCount = useCallback(async () => {
    if (!user?.username) return;
    
    try {
      setLoading(true);
      const proposals = await proposalService.listMyProposals(user.username);
      setProposalCount(proposals.length || null);
    } catch (error) {
      console.error('Error fetching proposal count:', error);
      setProposalCount(null);
    } finally {
      setLoading(false);
    }
  }, [user?.username]);

  useEffect(() => {
    fetchProposalCount();
    
    const interval = setInterval(fetchProposalCount, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, [fetchProposalCount]);

  return { proposalCount, loading, refresh: fetchProposalCount };
};
