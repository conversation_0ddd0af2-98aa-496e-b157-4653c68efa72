import awsconfig from '@/aws-exports';
import S3Service from '@/lib/s3/s3Service';
import { getUrl } from 'aws-amplify/storage';

/**
 * Get the full URL for a profile photo
 * @param filename The filename (without path) of the photo
 * @returns Full URL to the profile photo or default logo if no filename provided
 */
export const getProfilePhotoUrl = (filename?: string | null): string => {
  if (!filename) return '/logo.png';
  
  if (filename.startsWith('http')) {
    return filename;
  }
  
  const baseUrl = `https://${awsconfig.aws_user_files_s3_bucket}.s3.${awsconfig.aws_project_region}.amazonaws.com`;
  let cleanPath = filename.replace(/^\/+|^public\//g, '');
  cleanPath = cleanPath.replace(/^profile-photos\//, '');
  return `${baseUrl}/public/profile-photos/${cleanPath}`;
};

/**
 * Extract the S3 key from a profile photo URL
 * @param url The profile photo URL
 * @returns The S3 key or null if not a valid S3 URL
 */
export const extractProfilePhotoKey = (url: string): string | null => {
  if (!url) return null;
  
  const baseUrl = `https://${awsconfig.aws_user_files_s3_bucket}.s3.${awsconfig.aws_project_region}.amazonaws.com`;
  let key = url.replace(baseUrl, '').replace(/^\/+/, '');
  
  key = key.replace(/^public\//, '');
  
  return key || null;
};

/**
 * Delete a profile photo from S3
 * @param key The S3 object key to delete (can be full URL, full path, or just the filename)
 * @returns Promise that resolves when the file is deleted
 */
/**
 * Check if a file exists in S3
 * @param key The S3 object key to check
 * @returns Promise that resolves to true if the file exists, false otherwise
 */
export const checkIfFileExists = async (key: string): Promise<boolean> => {
  if (!key) return false;
  
  try {
    let cleanKey = key;
    
    if (key.includes('amazonaws.com/')) {
      const url = new URL(key);
      cleanKey = url.pathname.substring(1);
    }
    
    cleanKey = cleanKey.split('?')[0];
    
    await getUrl({
      key: cleanKey,
      options: {
        accessLevel: 'protected',
        expiresIn: 60
      }
    });
    
    return true;
  } catch (error) {
    if (error?.toString().includes('404')) {
      return false;
    }
    console.error('Error checking if file exists in S3:', error);
    return true;
  }
};

/**
 * Delete a profile photo from S3
 * @param key The S3 object key to delete (can be full URL, full path, or just the filename)
 * @returns Promise that resolves when the file is deleted
 */
export const deleteProfilePhotoFromS3 = async (key: string): Promise<void> => {
  if (!key) {
    console.error('No key provided for deletion');
    return;
  }
  
  try {
    let cleanKey = key;
    
    if (key.includes('amazonaws.com/')) {
      const url = new URL(key);
      cleanKey = url.pathname.substring(1);
    }
    
    cleanKey = cleanKey.split('?')[0];
    
    if (cleanKey.includes('/')) {
      cleanKey = cleanKey.split('/').pop() || cleanKey;
    }
    
    if (!cleanKey.startsWith('profile-photos/')) {
      cleanKey = `profile-photos/${cleanKey}`;
    }
    
    await S3Service.deleteFile({ 
      key: cleanKey,
      level: 'guest'
    });
    
  } catch (error) {
    console.error('Error deleting photo from S3:', error);
    throw error;
  }
};
