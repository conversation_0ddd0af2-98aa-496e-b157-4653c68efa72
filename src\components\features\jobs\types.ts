import { Job, JobWithProposalList } from '@/types/features/jobs/job.types';

export interface JobCardProps {
  job: Job | JobWithProposalList;
  onEdit?: (jobId: string) => void;
  onDelete?: (jobId: string) => Promise<void>;
  showActions?: boolean;
  className?: string;
}

export interface JobFormProps {
  initialValues?: Partial<Job>;
  onSubmit: (values: any) => Promise<void>;
  isSubmitting?: boolean;
  submitButtonText?: string;
  className?: string;
}
