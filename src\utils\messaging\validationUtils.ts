import { UIMessage } from '@/types/features/messaging/messaging.types';

export const isValidMessage = (message: unknown): message is UIMessage => {
  if (!message || typeof message !== 'object') return false;
  
  const msg = message as Record<string, unknown>;
  
  return (
    typeof msg.id === 'string' &&
    typeof msg.content === 'string' &&
    typeof msg.senderId === 'string' &&
    typeof msg.conversationId === 'string' &&
    typeof msg.createdAt === 'string' &&
    (typeof msg.updatedAt === 'string' || typeof msg.updatedAt === 'undefined') &&
    typeof msg.status === 'string' &&
    ['sending', 'sent', 'delivered', 'read', 'error'].includes(
      String(msg.status).toLowerCase()
    ) &&
    typeof msg.type === 'string' &&
    ['text', 'file', 'image'].includes(String(msg.type).toLowerCase())
  );
};

export const validateMessageInput = (content: string, file?: File): { isValid: boolean; error?: string } => {
  if (!content?.trim() && !file) {
    return { isValid: false, error: 'Message cannot be empty' };
  }

  if (content && content.length > 2000) {
    return { isValid: false, error: 'Message is too long (max 2000 characters)' };
  }

  if (file) {
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      return { isValid: false, error: 'File size exceeds 10MB limit' };
    }

    const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    if (!allowedTypes.includes(file.type)) {
      return { isValid: false, error: 'Unsupported file type' };
    }
  }

  return { isValid: true };
};

export const filterValidMessages = (messages: unknown[]): UIMessage[] => {
  return messages.filter(isValidMessage);
};
