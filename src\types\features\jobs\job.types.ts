import { ApiResponse, PaginatedResponse } from '@/types/common/api.types';

export enum JobStatus {
  OPEN = 'OPEN',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

export enum JobType {
  FIXED_PRICE = 'FIXED_PRICE',
  HOURLY = 'HOURLY'
}

/**
 * Represents a job posting in the system
 */
export type Job = {
  id: string;
  /** ID of the client who posted the job */
  clientId: string;
  title: string;
  description: string;
  budget: number;
  type?: JobType;
  /** Current status of the job */
  status: JobStatus;
  skills: string[];
  category: string;
  deadline?: string;
  isRemote?: boolean;
  location?: {
    type: string;
    coordinates: [number, number];
    address?: string;
  } | string;
  attachments?: string[];
  /** ISO timestamp of when the job was created */
  createdAt: string;
  /** ISO timestamp of when the job was last updated */
  updatedAt?: string;
  client?: {
    id: string;
    name: string;
    email?: string;
    avatar?: string;
    rating?: number;
  };
  /** Number of proposals received for this job */
  proposalsCount?: number;
  /** @deprecated Use proposalsCount instead */
  proposalCount?: number;
  /** Paginated list of proposals for this job */
  proposals?: {
    items?: any[];
    nextToken?: string;
  };
};

export type CreateJobDto = {
  title: string;
  description: string;
  budget: number;
  type?: JobType;
  skills: string[];
  category: string;
  deadline?: string;
  isRemote?: boolean;
  location?: {
    type: string;
    coordinates: [number, number];
    address?: string;
  } | string;
  attachments?: string[];
  clientId?: string;
};

export type UpdateJobDto = Partial<CreateJobDto> & {
  id?: string;
  status?: JobStatus;
};

export type JobsResponse = PaginatedResponse<Job>;
export type JobResponse = ApiResponse<Job>;

export type JobCardProps = {
  job: Job;
  onView?: (job: Job) => void;
  onApply?: (job: Job) => void;
  className?: string;
};

export type JobFilters = {
  query?: string;
  minBudget?: number;
  maxBudget?: number;
  categories?: string[];
  category?: string;
  skills?: string[];
  jobType?: JobType;
  status?: JobStatus;
  sortBy?: 'newest' | 'budget_high' | 'budget_low';
  clientId?: string;
  isRemote?: boolean;
  searchTerm?: string;
  includeProposals?: boolean;
  limit?: number;
  nextToken?: string;
};

export type JobSearchParams = {
  query?: string;
  category?: string;
  minBudget?: number;
  maxBudget?: number;
  skills?: string[];
  type?: JobType;
  status?: JobStatus;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
};

export type CreateJobInput = CreateJobDto;
export type UpdateJobInput = UpdateJobDto;
export type JobFilter = JobFilters;
export type JobCategory = string;

export type JobWithProposalList = Job & {
  proposals: any[];
  proposalCount: number;
  client?: {
    id: string;
    name: string;
    email: string;
  };
};