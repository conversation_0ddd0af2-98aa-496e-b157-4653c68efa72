import React from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CardT<PERSON><PERSON> 
} from '@/components/ui/Card';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

export const GettingStarted = () => {
  const installationCode = `
npm install @radix-ui/react-slot class-variance-authority clsx tailwind-merge

npm install -D @types/node @types/react @types/react-dom`;

  const usageCode = `
import { Button } from '@/components/ui/button';

export function MyComponent() {
  return (
    <div className="flex gap-2">
      <Button>Primary</Button>
      <Button variant="outline">Secondary</Button>
    </div>
  );
}`;

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight mb-4">Getting Started</h2>
        <p className="text-muted-foreground">
          Welcome to the MyVillage Component Library. This guide will help you get started with our
          collection of reusable React components built with Tailwind CSS and Radix UI.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Installation</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p>Make sure you have the following dependencies installed:</p>
            <SyntaxHighlighter 
              language="bash" 
              style={vscDarkPlus}
              className="rounded-md text-sm"
            >
              {installationCode}
            </SyntaxHighlighter>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Basic Usage</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p>Here&apos;s how to use the components in your project:</p>
            <SyntaxHighlighter 
              language="tsx" 
              style={vscDarkPlus}
              className="rounded-md text-sm"
            >
              {usageCode}
            </SyntaxHighlighter>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Styling</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p>
              All components are styled with Tailwind CSS. You can customize the look and feel by
              using Tailwind&apos;s utility classes or by extending the theme in your{' '}
              <code className="bg-muted px-1 rounded">tailwind.config.js</code> file.
            </p>
            <p>
              Each component&apos;s styles are scoped to avoid conflicts with your existing styles.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default GettingStarted;
