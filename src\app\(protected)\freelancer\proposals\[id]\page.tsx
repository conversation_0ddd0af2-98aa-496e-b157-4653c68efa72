"use client";

import { useState, useEffect, useCallback } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";
import { proposalService } from "@/api/proposals/proposal.service";
import { But<PERSON> } from "@/components/ui";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Icon } from "@/components/ui";
import { Badge } from "@/components/ui/Badge";
import { formatDistanceToNow } from "date-fns";
import Link from "next/link";
import {
  useContractCheck,
  shouldShowCreateContractButton,
  getCreateContractUrl,
} from "@/hooks/useContractCheck";
import { toast } from "@/components/ui/toast";

interface Job {
  id: string;
  title: string;
  description: string;
  budget: number;
  status: string;
  category: string;
  deadline: string;
  isRemote: boolean;
  clientId: string;
  createdAt: string;
  client?: {
    id: string;
    name: string;
    email: string;
  };
}

interface JobProposal {
  id: string;
  jobId: string;
  freelancerId: string;
  coverLetter: string;
  bidAmount: number;
  status: string;
  createdAt: string;
  updatedAt: string;
  job?: Job;
}

interface ProposalDetails extends JobProposal {}

const ProposalDetailsPage = () => {
  const params = useParams();
  const router = useRouter();
  const { isAuthenticated } = useAuth();
  const [proposal, setProposal] = useState<ProposalDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { hasExistingContract, isLoading: contractCheckLoading } =
    useContractCheck(proposal?.id);

  const proposalId = params.id as string;

  const fetchProposalDetails = useCallback(async () => {
    if (!proposalId) return;

    try {
      setIsLoading(true);
      const data = await proposalService.getProposalById(proposalId);
      if (data) {
        const jobData = data.job
          ? {
              ...data.job,
              id: data.job.id || "",
              title: data.job.title || "",
              description: data.job.description || "",
              budget: data.job.budget ?? 0,
              status: data.job.status || "OPEN",
              isRemote: data.job.isRemote ?? false,
              category: data.job.category || "General",
              deadline: data.job.deadline || new Date().toISOString(),
              clientId: data.job.clientId || "",
              createdAt: data.job.createdAt || new Date().toISOString(),
              client: data.job.client
                ? {
                    id: data.job.client.id || "",
                    name: data.job.client.name || "Unknown",
                    email: data.job.client.email || "",
                  }
                : undefined,
            }
          : undefined;

        const proposalData: ProposalDetails = {
          ...data,
          updatedAt: data.updatedAt || new Date().toISOString(),
          job: jobData,
        };

        setProposal(proposalData);
      } else {
        setError("Proposal not found");
        toast.error("Could not find the requested proposal");
        router.push("/freelancer/proposals");
      }
    } catch (err) {
      console.error("Error fetching proposal:", err);
      setError("Failed to load proposal details");
      toast.error("Failed to load proposal details");
      router.push("/freelancer/proposals");
    } finally {
      setIsLoading(false);
    }
  }, [proposalId, router]);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/login");
      return;
    }

    fetchProposalDetails();
  }, [isAuthenticated, fetchProposalDetails, router]);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING: {
        variant: "warning" as const,
        label: "Pending Review",
        icon: "Clock",
        className:
          "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400 border-amber-200 dark:border-amber-800",
      },
      ACCEPTED: {
        variant: "success" as const,
        label: "Accepted",
        icon: "CheckCircle",
        className:
          "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 border-green-200 dark:border-green-800",
      },
      REJECTED: {
        variant: "destructive" as const,
        label: "Rejected",
        icon: "XCircle",
        className:
          "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 border-red-200 dark:border-red-800",
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "secondary" as const,
      label: status,
      icon: "HelpCircle",
      className: "",
    };

    return (
      <Badge
        variant={config.variant}
        className={`flex items-center gap-1 border ${config.className}`}
      >
        <Icon name={config.icon as any} size="sm" />
        {config.label}
      </Badge>
    );
  };

  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Icon name="Loader2" size="xl" className="animate-spin" />
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <Icon name="ArrowLeft" size="sm" className="mr-2" />
            Back
          </Button>
        </div>
        <div className="flex items-center justify-center py-12">
          <Icon name="Loader2" size="xl" className="animate-spin" />
        </div>
      </div>
    );
  }

  if (contractCheckLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <Icon name="ArrowLeft" size="sm" className="mr-2" />
            Back
          </Button>
        </div>
        <div className="flex items-center justify-center py-12">
          <Icon name="Loader2" size="xl" className="animate-spin" />
        </div>
      </div>
    );
  }

  if (error || !proposal) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <Icon name="ArrowLeft" size="sm" className="mr-2" />
            Back
          </Button>
        </div>
        <Card>
          <CardContent className="py-12 text-center">
            <Icon
              name="AlertCircle"
              size="xl"
              className="mx-auto mb-4 text-muted-foreground"
            />
            <h3 className="text-lg font-semibold mb-2">
              {error || "Proposal not found"}
            </h3>
            <p className="text-muted-foreground mb-4">
              The proposal you&#39;re looking for doesn&#39;t exist or you
              don&#39;t have permission to view it.
            </p>
            <Button asChild>
              <Link href="/freelancer/proposals">View All Proposals</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" onClick={() => router.back()}>
          <Icon name="ArrowLeft" size="sm" className="mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-2xl font-bold">Proposal Details</h1>
          <p className="text-muted-foreground">
            Submitted{" "}
            {formatDistanceToNow(new Date(proposal.createdAt), {
              addSuffix: true,
            })}
          </p>
        </div>
      </div>

      {/* Job Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Icon name="Briefcase" size="md" />
            Job Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold text-lg mb-2">
              {proposal.job?.title || "Job Title Not Available"}
            </h3>
            <p className="text-muted-foreground">
              {proposal.job?.description || "No description available"}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Budget
              </label>
              <p className="text-lg font-semibold">
                ${proposal.job?.budget?.toLocaleString() || "N/A"}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Client
              </label>
              <p className="font-medium">
                {proposal.job?.client?.name ||
                  "Client information not available"}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Work Type
              </label>
              <p className="font-medium">
                {proposal.job?.isRemote ? "Remote" : "On-site"}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Proposal Details */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="flex items-center gap-2">
            <Icon name="FileText" size="md" />
            Your Proposal
          </CardTitle>
          {getStatusBadge(proposal.status)}
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium text-muted-foreground">
              Bid Amount
            </label>
            <p className="text-2xl font-bold text-primary">
              ${proposal.bidAmount?.toLocaleString() || "0"}
            </p>
          </div>

          <div className="pt-4 border-t">
            <label className="text-sm font-medium text-muted-foreground">
              Cover Letter
            </label>
            <div className="mt-2 p-4 bg-muted/50 rounded-lg">
              <p className="whitespace-pre-wrap">
                {proposal.coverLetter || "No cover letter provided"}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <Card>
        <CardContent className="py-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <Button asChild className="flex-1">
              <Link href={`/freelancer/jobs/${proposal.jobId}`}>
                <Icon name="Eye" size="sm" className="mr-2" />
                View Job Details
              </Link>
            </Button>

            {proposal.status === "PENDING" && (
              <Button variant="outline" asChild className="flex-1">
                <Link href={`/freelancer/proposals/${proposal.id}/edit`}>
                  <Icon name="Edit" size="sm" className="mr-2" />
                  Edit Proposal
                </Link>
              </Button>
            )}

            {shouldShowCreateContractButton(
              proposal.status,
              hasExistingContract
            ) && (
              <Button asChild className="flex-1">
                <Link href={getCreateContractUrl(proposal.jobId, proposal.id)}>
                  <Icon name="FileText" size="sm" className="mr-2" />
                  Create Contract
                </Link>
              </Button>
            )}

            <Button variant="outline" asChild className="flex-1">
              <Link href="/freelancer/proposals">
                <Icon name="List" size="sm" className="mr-2" />
                All Proposals
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProposalDetailsPage;
