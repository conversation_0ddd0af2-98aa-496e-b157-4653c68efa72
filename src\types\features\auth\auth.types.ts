import { SignInOutput } from 'aws-amplify/auth';
import { UserRole } from '../../enums';

export { UserRole } from '../../enums';

export interface UpdateRoleResult {
  success: boolean;
  message: string;
  role?: UserRole;
}

/**
 * Authentication-related types and interfaces
 */

export interface UserAttributes {
  email: string;
  name?: string;
  bio?: string;
  profilePhoto?: string;
  skills?: string[];
  'custom:role'?: UserRole;
  sub?: string;
  [key: string]: string | string[] | undefined;
}

/**
 * Represents the output of a sign-in operation with additional user attributes.
 * Extends AWS Amplify's SignInOutput with our custom user properties.
 */
export interface CustomSignInOutput extends SignInOutput {
  /** The username of the authenticated user */
  username: string;

  /** User attributes including custom attributes like role */
  attributes: UserAttributes;

  /** Direct access to user's name (also available in attributes) */
  name?: string;

  /** Direct access to user's bio (also available in attributes) */
  bio?: string;

  /** Direct access to user's skills (also available in attributes) */
  skills?: string[];

  /** Challenge name if additional authentication steps are required */
  challengeName?: string;
}

/**
 * Creates a CognitoUser object with all required properties.
 * Ensures that all CognitoUser objects have the required Amplify properties.
 */
export function createCognitoUser(
  user: Omit<CustomSignInOutput, 'isSignedIn' | 'nextStep'>
): CognitoUser {
  return {
    ...user,
    isSignedIn: true,
    nextStep: {
      signInStep: 'DONE',
    },
    username: user.username,
    attributes: {
      ...user.attributes,
    },
  };
}

/**
 * Represents an authenticated user in the system.
 * Extends CustomSignInOutput which includes both AWS Amplify and our custom properties.
 */
export type CognitoUser = CustomSignInOutput;

/**
 * Parameters required for user sign-up
 */
export interface SignUpParams {
  username: string;
  password: string;
  attributes: {
    email: string;
    name?: string;
    'custom:role'?: UserRole;
    [key: string]: string | undefined;
  };
}

/**
 * Result of a sign-up operation
 */
export interface SignUpResult {
  user: CognitoUser;
  isSignUpComplete: boolean;
  nextStep: {
    signUpStep: 'CONFIRM_SIGN_UP' | 'DONE';
  };
}

/**
 * Parameters that can be updated in a user's profile
 */
export interface ProfileUpdateParams {
  profilePhoto?: string;
  name?: string;
  bio?: string;
  skills?: string[];
  'custom:role'?: UserRole;
  [key: string]: string | string[] | undefined;
}

/**
 * Represents a user during the password challenge flow
 */
export interface ChallengeUser {
  username?: string;
  challengeName?: string;
  attributes?: {
    email?: string;
    name?: string;
    [key: string]: string | undefined;
  };
}

/**
 * Auth context type for the application
 */
export interface AuthContextTypeDef {
  isAuthenticated: boolean;
  user: CognitoUser | null;
  loading: boolean;
  error: string | null;
  cognitoUserId: string | null;
  signIn: (email: string, password: string) => Promise<CognitoUser | void>;
  signUp: (params: SignUpParams) => Promise<SignUpResult>;
  confirmSignUp: (username: string, code: string, password?: string) => Promise<SignInOutput | boolean>;
  signOut: () => Promise<void>;
  forgotPassword: (email: string) => Promise<void>;
  forgotPasswordSubmit: (email: string, code: string, newPassword: string) => Promise<void>;
  completeNewPassword: (user: ChallengeUser, newPassword: string) => Promise<SignInOutput>;
  updateProfile: (attributes: ProfileUpdateParams) => Promise<void>;
  refresh: () => Promise<CognitoUser>;
}

export type { AuthContextTypeDef as AuthContextType };

export interface UserProfile {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  avatar?: string;
  phoneNumber?: string;
  bio?: string;
  skills?: string[];
  location?: string;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: UserProfile | null;
  isLoading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  role: UserRole;
  phoneNumber?: string;
}

export interface ResetPasswordData {
  email: string;
  code: string;
  newPassword: string;
}

export interface AuthResponse {
  user: UserProfile;
  token: string;
  refreshToken: string;
}

export interface ProtectedRouteProps {
  roles?: UserRole[];
  children: React.ReactNode;
  redirectTo?: string;
}

export interface AuthFormProps {
  type: 'login' | 'register' | 'forgot-password' | 'reset-password';
  onSubmit: (data: any) => void;
  isLoading?: boolean;
  error?: string | null;
}
