import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/Card";
import { Icon } from "@/components/ui/Icon";
import { Skeleton } from "@/components/ui/Skeleton";
import { Badge } from "@/components/ui/Badge";
import { animations } from "@/utils/animations";
import { formatCurrency } from "@/utils/format";

type IconName =
  | "FileText"
  | "Briefcase"
  | "DollarSign"
  | "Users"
  | "CheckCircle2"
  | "MessageSquare"
  | "CheckCircle"
  | "Tag"
  | "Clock"
  | "TrendingUp"
  | "TrendingDown"
  | "UserPlus"
  | "Building";

export interface StatCardProps {
  title: string;
  value: React.ReactNode;
  change?: string;
  changeType?: "increase" | "decrease" | "neutral";
  percentage?: number;
  icon?: IconName;
  iconColor?: string;
  loading?: boolean;
  className?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
  href?: string;
  subtitle?: string;
  badge?: {
    text: string;
    variant?: "default" | "secondary" | "destructive" | "outline";
  };
  trend?: {
    value: number;
    period: string;
    isPositive?: boolean;
  };
  format?: "number" | "currency" | "percentage";
}

export const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  change,
  changeType = "neutral",
  percentage,
  icon,
  iconColor,
  loading = false,
  className = "",
  style = {},
  onClick,
  subtitle,
  badge,
  trend,
  format = "number",
}) => {
  const formatValue = (val: React.ReactNode) => {
    if (typeof val === "number") {
      switch (format) {
        case "currency":
          return formatCurrency(val);
        case "percentage":
          return `${val}%`;
        default:
          return val.toLocaleString();
      }
    }
    return val;
  };

  const getTrendColor = () => {
    if (trend) {
      return trend.isPositive !== false ? "text-green-600" : "text-red-600";
    }
    switch (changeType) {
      case "increase":
        return "text-green-600";
      case "decrease":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  const getTrendIcon = () => {
    if (trend) {
      return trend.isPositive !== false ? "TrendingUp" : "TrendingDown";
    }
    switch (changeType) {
      case "increase":
        return "TrendingUp";
      case "decrease":
        return "TrendingDown";
      default:
        return undefined;
    }
  };

  if (loading) {
    return (
      <Card
        className={`${animations.cardHover} ${animations.hoverScaleSm} opacity-0 animate-fade-in ${className}`}
        style={style}
      >
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-4 rounded-full" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-8 w-16 mb-2" />
          <Skeleton className="h-3 w-20 mb-1" />
          <Skeleton className="h-3 w-16" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card
      className={`${animations.cardHover} ${
        animations.hoverScaleSm
      } opacity-0 animate-fade-in hover:shadow-lg transition-all duration-200 h-full ${
        onClick ? "cursor-pointer" : ""
      } ${className}`}
      style={style}
      onClick={onClick}
    >
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div className="flex-1">
          <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
            {title}
          </CardTitle>
          {subtitle && (
            <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
              {subtitle}
            </p>
          )}
        </div>
        <div className="flex items-center space-x-2">
          {badge && (
            <Badge variant={badge.variant || "default"} className="text-xs">
              {badge.text}
            </Badge>
          )}
          {icon && (
            <div
              className={`p-2 rounded-lg ${
                iconColor || "bg-blue-50 dark:bg-blue-900/20"
              }`}
            >
              <Icon
                name={icon}
                className={`h-5 w-5 ${
                  iconColor ? "" : "text-blue-600 dark:text-blue-400"
                }`}
              />
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          {formatValue(value)}
        </div>

        <div className="flex items-center justify-between">
          <div className="flex-1">
            {change && <p className={`text-xs ${getTrendColor()}`}>{change}</p>}

            {trend && (
              <div
                className={`flex items-center text-xs ${getTrendColor()} mt-1`}
              >
                {getTrendIcon() && (
                  <Icon name={getTrendIcon()!} className="h-3 w-3 mr-1" />
                )}
                <span>
                  {trend.isPositive !== false ? "+" : ""}
                  {trend.value}% {trend.period}
                </span>
              </div>
            )}
          </div>

          {percentage !== undefined && (
            <div className="flex items-center">
              <div className="w-12 bg-gray-200 dark:bg-gray-700 rounded-full h-2 ml-2">
                <div
                  className={`h-2 rounded-full ${
                    percentage > 75
                      ? "bg-green-500"
                      : percentage > 50
                      ? "bg-yellow-500"
                      : percentage > 25
                      ? "bg-orange-500"
                      : "bg-red-500"
                  }`}
                  style={{
                    width: `${Math.min(100, Math.max(0, percentage))}%`,
                  }}
                />
              </div>
              <span className="text-xs text-gray-500 ml-1">{percentage}%</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
