"use client";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";
import { userService } from "@/api/users/user.service";
import { UserRole } from "@/types/enums";

export default function OnboardRoleGate({
  desiredRole,
  name,
}: {
  desiredRole: UserRole;
  name: string;
}) {
  const { user, updateProfile } = useAuth();
  const router = useRouter();

  useEffect(() => {
    async function run() {
      const userId = user?.username;
      if (!userId) return;

      if (user.attributes?.["custom:role"]) return;

      try {
        await updateProfile({
          "custom:role": desiredRole,
        });

        let existing;
        try {
          existing = await userService.getUserById(userId);
        } catch (error: any) {
          if (error.name !== "ResourceNotFoundError") {
            console.error("Error fetching user:", error);
          }
          existing = null;
        }

        if (!existing) {
          try {
            await userService.createUser({
              id: userId,
              name,
              email: user.attributes?.email || "",
              role: desiredRole,
              bio: "",
              skills: [],
            });
            console.log("Successfully created user in database");
          } catch (createError) {
            console.error("Error creating user in database:", createError);
          }
        }

        router.refresh();
      } catch (e) {
        console.error("Error in OnboardRoleGate:", e);
      }
    }

    run();
  }, [user, desiredRole, name, router, updateProfile]);

  return null;
}
